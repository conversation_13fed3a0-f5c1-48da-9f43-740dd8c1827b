package com.inxaiot.edge.controller.device;

import com.inxaiot.edge.common.response.ApiResponse;
import com.inxaiot.edge.entity.device.DeviceInfo;
import com.inxaiot.edge.service.device.DeviceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备信息控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/device/info")
@Validated
public class DeviceInfoController {
    
    @Autowired
    private DeviceInfoService deviceInfoService;
    
    /**
     * 添加设备信息
     */
    @PostMapping
    public ApiResponse<Boolean> addDevice(@Valid @RequestBody DeviceInfo deviceInfo) {
        log.info("添加设备信息: {}", deviceInfo.getDeviceId());
        
        boolean result = deviceInfoService.addDevice(deviceInfo);
        if (result) {
            return ApiResponse.success("设备添加成功", true);
        } else {
            return ApiResponse.error("设备添加失败");
        }
    }
    
    /**
     * 删除设备信息
     */
    @DeleteMapping("/{deviceId}")
    public ApiResponse<Boolean> deleteDevice(@PathVariable @NotBlank String deviceId) {
        log.info("删除设备信息: {}", deviceId);
        
        boolean result = deviceInfoService.deleteDevice(deviceId);
        if (result) {
            return ApiResponse.success("设备删除成功", true);
        } else {
            return ApiResponse.error("设备删除失败");
        }
    }
    
    /**
     * 更新设备信息
     */
    @PutMapping
    public ApiResponse<Boolean> updateDevice(@Valid @RequestBody DeviceInfo deviceInfo) {
        log.info("更新设备信息: {}", deviceInfo.getDeviceId());
        
        boolean result = deviceInfoService.updateDevice(deviceInfo);
        if (result) {
            return ApiResponse.success("设备更新成功", true);
        } else {
            return ApiResponse.error("设备更新失败");
        }
    }
    
    /**
     * 根据设备ID查询设备信息
     */
    @GetMapping("/{deviceId}")
    public ApiResponse<DeviceInfo> getDevice(@PathVariable @NotBlank String deviceId) {
        log.info("查询设备信息: {}", deviceId);
        
        DeviceInfo device = deviceInfoService.getDeviceById(deviceId);
        if (device != null) {
            return ApiResponse.success(device);
        } else {
            return ApiResponse.notFound("设备不存在");
        }
    }
    
    /**
     * 查询所有设备信息
     */
    @GetMapping
    public ApiResponse<List<DeviceInfo>> getAllDevices() {
        log.info("查询所有设备信息");
        
        List<DeviceInfo> devices = deviceInfoService.getAllDevices();
        return ApiResponse.success(devices);
    }
    
    /**
     * 根据设备类型查询设备信息
     */
    @GetMapping("/type/{deviceType}")
    public ApiResponse<List<DeviceInfo>> getDevicesByType(@PathVariable @NotBlank String deviceType) {
        log.info("根据类型查询设备信息: {}", deviceType);
        
        List<DeviceInfo> devices = deviceInfoService.getDevicesByType(deviceType);
        return ApiResponse.success(devices);
    }
    
    /**
     * 根据状态查询设备信息
     */
    @GetMapping("/status/{status}")
    public ApiResponse<List<DeviceInfo>> getDevicesByStatus(@PathVariable @NotNull Integer status) {
        log.info("根据状态查询设备信息: {}", status);
        
        List<DeviceInfo> devices = deviceInfoService.getDevicesByStatus(status);
        return ApiResponse.success(devices);
    }
    
    /**
     * 分页查询设备信息
     */
    @GetMapping("/page")
    public ApiResponse<List<DeviceInfo>> getDevicesByPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("分页查询设备信息: page={}, size={}", page, size);
        
        List<DeviceInfo> devices = deviceInfoService.getDevicesByPage(page, size);
        return ApiResponse.success(devices);
    }
    
    /**
     * 查询设备总数
     */
    @GetMapping("/count")
    public ApiResponse<Integer> getDeviceCount() {
        log.info("查询设备总数");
        
        int count = deviceInfoService.getDeviceCount();
        return ApiResponse.success(count);
    }
    
    /**
     * 更新设备状态
     */
    @PutMapping("/{deviceId}/status")
    public ApiResponse<Boolean> updateDeviceStatus(
            @PathVariable @NotBlank String deviceId,
            @RequestParam @NotNull Integer status) {
        log.info("更新设备状态: {} -> {}", deviceId, status);
        
        boolean result = deviceInfoService.updateDeviceStatus(deviceId, status);
        if (result) {
            return ApiResponse.success("设备状态更新成功", true);
        } else {
            return ApiResponse.error("设备状态更新失败");
        }
    }
}
