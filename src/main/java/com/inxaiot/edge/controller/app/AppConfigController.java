package com.inxaiot.edge.controller.app;

import com.inxaiot.edge.entity.app.AppConfig;
import com.inxaiot.edge.service.app.AppConfigService;
import com.inxaiot.edge.common.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 应用配置控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/app/config")
@Validated
public class AppConfigController {
    
    @Autowired
    private AppConfigService appConfigService;
    
    /**
     * 设置应用配置
     */
    @PostMapping("/{appName}/{configKey}")
    public ApiResponse<Boolean> setConfig(
            @PathVariable @NotBlank String appName,
            @PathVariable @NotBlank String configKey,
            @RequestParam @NotBlank String configValue,
            @RequestParam(defaultValue = "string") String configType,
            @RequestParam(required = false) String description) {
        
        log.info("设置应用配置: {} - {} = {}", appName, configKey, configValue);
        
        boolean result = appConfigService.setConfig(appName, configKey, configValue, configType, description);
        if (result) {
            return ApiResponse.success("配置设置成功", true);
        } else {
            return ApiResponse.error("配置设置失败");
        }
    }
    
    /**
     * 获取应用配置
     */
    @GetMapping("/{appName}/{configKey}")
    public ApiResponse<String> getConfig(
            @PathVariable @NotBlank String appName,
            @PathVariable @NotBlank String configKey) {
        
        log.info("获取应用配置: {} - {}", appName, configKey);
        
        String configValue = appConfigService.getConfig(appName, configKey);
        if (configValue != null) {
            return ApiResponse.success(configValue);
        } else {
            return ApiResponse.notFound("配置不存在");
        }
    }
    
    /**
     * 获取应用配置对象
     */
    @GetMapping("/{appName}/{configKey}/object")
    public ApiResponse<AppConfig> getConfigObject(
            @PathVariable @NotBlank String appName,
            @PathVariable @NotBlank String configKey) {
        
        log.info("获取应用配置对象: {} - {}", appName, configKey);
        
        AppConfig config = appConfigService.getConfigObject(appName, configKey);
        if (config != null) {
            return ApiResponse.success(config);
        } else {
            return ApiResponse.notFound("配置不存在");
        }
    }
    
    /**
     * 删除应用配置
     */
    @DeleteMapping("/{appName}/{configKey}")
    public ApiResponse<Boolean> deleteConfig(
            @PathVariable @NotBlank String appName,
            @PathVariable @NotBlank String configKey) {
        
        log.info("删除应用配置: {} - {}", appName, configKey);
        
        boolean result = appConfigService.deleteConfig(appName, configKey);
        if (result) {
            return ApiResponse.success("配置删除成功", true);
        } else {
            return ApiResponse.error("配置删除失败");
        }
    }
    
    /**
     * 根据应用名称获取所有配置
     */
    @GetMapping("/{appName}")
    public ApiResponse<List<AppConfig>> getConfigsByAppName(@PathVariable @NotBlank String appName) {
        log.info("获取应用所有配置: {}", appName);
        
        List<AppConfig> configs = appConfigService.getConfigsByAppName(appName);
        return ApiResponse.success(configs);
    }
    
    /**
     * 获取所有应用配置
     */
    @GetMapping
    public ApiResponse<List<AppConfig>> getAllConfigs() {
        log.info("获取所有应用配置");
        
        List<AppConfig> configs = appConfigService.getAllConfigs();
        return ApiResponse.success(configs);
    }
}
