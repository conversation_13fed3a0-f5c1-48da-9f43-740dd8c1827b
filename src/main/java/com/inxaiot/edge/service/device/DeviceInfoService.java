package com.inxaiot.edge.service.device;

import com.inxaiot.edge.entity.device.DeviceInfo;

import java.util.List;

/**
 * 设备信息服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface DeviceInfoService {
    
    /**
     * 添加设备信息
     */
    boolean addDevice(DeviceInfo deviceInfo);
    
    /**
     * 删除设备信息
     */
    boolean deleteDevice(String deviceId);
    
    /**
     * 更新设备信息
     */
    boolean updateDevice(DeviceInfo deviceInfo);
    
    /**
     * 根据设备ID查询设备信息
     */
    DeviceInfo getDeviceById(String deviceId);
    
    /**
     * 查询所有设备信息
     */
    List<DeviceInfo> getAllDevices();
    
    /**
     * 根据设备类型查询设备信息
     */
    List<DeviceInfo> getDevicesByType(String deviceType);
    
    /**
     * 根据状态查询设备信息
     */
    List<DeviceInfo> getDevicesByStatus(Integer status);
    
    /**
     * 分页查询设备信息
     */
    List<DeviceInfo> getDevicesByPage(int page, int size);
    
    /**
     * 查询设备总数
     */
    int getDeviceCount();
    
    /**
     * 更新设备状态
     */
    boolean updateDeviceStatus(String deviceId, Integer status);
    
    /**
     * 检查设备是否存在
     */
    boolean deviceExists(String deviceId);
}
