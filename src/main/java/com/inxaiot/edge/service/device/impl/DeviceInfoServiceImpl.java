package com.inxaiot.edge.service.device.impl;

import com.inxaiot.edge.mapper.device.DeviceInfoMapper;
import com.inxaiot.edge.entity.device.DeviceInfo;
import com.inxaiot.edge.service.device.DeviceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备信息服务实现
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class DeviceInfoServiceImpl implements DeviceInfoService {
    
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    
    @Override
    @Transactional
    @CacheEvict(value = "devices", allEntries = true)
    public boolean addDevice(DeviceInfo deviceInfo) {
        try {
            if (deviceExists(deviceInfo.getDeviceId())) {
                log.warn("设备已存在: {}", deviceInfo.getDeviceId());
                return false;
            }
            
            deviceInfo.setCreatedTime(LocalDateTime.now());
            deviceInfo.setUpdatedTime(LocalDateTime.now());
            
            int result = deviceInfoMapper.insert(deviceInfo);
            log.info("添加设备成功: {}", deviceInfo.getDeviceId());
            return result > 0;
        } catch (Exception e) {
            log.error("添加设备失败: {}", deviceInfo.getDeviceId(), e);
            return false;
        }
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "devices", allEntries = true)
    public boolean deleteDevice(String deviceId) {
        try {
            int result = deviceInfoMapper.deleteByDeviceId(deviceId);
            log.info("删除设备成功: {}", deviceId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除设备失败: {}", deviceId, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "devices", allEntries = true)
    public boolean updateDevice(DeviceInfo deviceInfo) {
        try {
            deviceInfo.setUpdatedTime(LocalDateTime.now());
            int result = deviceInfoMapper.update(deviceInfo);
            log.info("更新设备成功: {}", deviceInfo.getDeviceId());
            return result > 0;
        } catch (Exception e) {
            log.error("更新设备失败: {}", deviceInfo.getDeviceId(), e);
            return false;
        }
    }
    
    @Override
    @Cacheable(value = "devices", key = "#deviceId")
    public DeviceInfo getDeviceById(String deviceId) {
        try {
            return deviceInfoMapper.selectByDeviceId(deviceId);
        } catch (Exception e) {
            log.error("查询设备失败: {}", deviceId, e);
            return null;
        }
    }
    
    @Override
    @Cacheable(value = "devices", key = "'all'")
    public List<DeviceInfo> getAllDevices() {
        try {
            return deviceInfoMapper.selectAll();
        } catch (Exception e) {
            log.error("查询所有设备失败", e);
            return null;
        }
    }
    
    @Override
    public List<DeviceInfo> getDevicesByType(String deviceType) {
        try {
            return deviceInfoMapper.selectByDeviceType(deviceType);
        } catch (Exception e) {
            log.error("根据类型查询设备失败: {}", deviceType, e);
            return null;
        }
    }
    
    @Override
    public List<DeviceInfo> getDevicesByStatus(Integer status) {
        try {
            return deviceInfoMapper.selectByStatus(status);
        } catch (Exception e) {
            log.error("根据状态查询设备失败: {}", status, e);
            return null;
        }
    }
    
    @Override
    public List<DeviceInfo> getDevicesByPage(int page, int size) {
        try {
            int offset = (page - 1) * size;
            return deviceInfoMapper.selectByPage(offset, size);
        } catch (Exception e) {
            log.error("分页查询设备失败: page={}, size={}", page, size, e);
            return null;
        }
    }
    
    @Override
    public int getDeviceCount() {
        try {
            return deviceInfoMapper.selectCount();
        } catch (Exception e) {
            log.error("查询设备总数失败", e);
            return 0;
        }
    }
    
    @Override
    @CacheEvict(value = "devices", key = "#deviceId")
    public boolean updateDeviceStatus(String deviceId, Integer status) {
        try {
            int result = deviceInfoMapper.updateStatus(deviceId, status);
            log.info("更新设备状态成功: {} -> {}", deviceId, status);
            return result > 0;
        } catch (Exception e) {
            log.error("更新设备状态失败: {} -> {}", deviceId, status, e);
            return false;
        }
    }
    
    @Override
    public boolean deviceExists(String deviceId) {
        try {
            DeviceInfo device = deviceInfoMapper.selectByDeviceId(deviceId);
            return device != null;
        } catch (Exception e) {
            log.error("检查设备是否存在失败: {}", deviceId, e);
            return false;
        }
    }
}
