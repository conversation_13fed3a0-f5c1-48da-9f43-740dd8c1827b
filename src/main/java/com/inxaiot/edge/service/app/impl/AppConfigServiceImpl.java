package com.inxaiot.edge.service.app.impl;

import com.inxaiot.edge.mapper.app.AppConfigMapper;
import com.inxaiot.edge.entity.app.AppConfig;
import com.inxaiot.edge.service.app.AppConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 应用配置服务实现
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class AppConfigServiceImpl implements AppConfigService {
    
    @Autowired
    private AppConfigMapper appConfigMapper;
    
    @Override
    @Transactional
    @CacheEvict(value = "app-configs", key = "#appName + ':' + #configKey")
    public boolean setConfig(String appName, String configKey, String configValue, String configType, String description) {
        try {
            AppConfig config = new AppConfig();
            config.setAppName(appName);
            config.setConfigKey(configKey);
            config.setConfigValue(configValue);
            config.setConfigType(configType);
            config.setDescription(description);
            config.setCreatedTime(LocalDateTime.now());
            config.setUpdatedTime(LocalDateTime.now());
            
            int result = appConfigMapper.insertOrUpdate(config);
            log.info("设置应用配置成功: {} - {} = {}", appName, configKey, configValue);
            return result > 0;
        } catch (Exception e) {
            log.error("设置应用配置失败: {} - {} = {}", appName, configKey, configValue, e);
            return false;
        }
    }
    
    @Override
    @Cacheable(value = "app-configs", key = "#appName + ':' + #configKey")
    public String getConfig(String appName, String configKey) {
        try {
            AppConfig config = appConfigMapper.selectByAppNameAndKey(appName, configKey);
            return config != null ? config.getConfigValue() : null;
        } catch (Exception e) {
            log.error("获取应用配置失败: {} - {}", appName, configKey, e);
            return null;
        }
    }
    
    @Override
    @Cacheable(value = "app-configs", key = "#appName + ':' + #configKey + ':object'")
    public AppConfig getConfigObject(String appName, String configKey) {
        try {
            return appConfigMapper.selectByAppNameAndKey(appName, configKey);
        } catch (Exception e) {
            log.error("获取应用配置对象失败: {} - {}", appName, configKey, e);
            return null;
        }
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "app-configs", key = "#appName + ':' + #configKey")
    public boolean deleteConfig(String appName, String configKey) {
        try {
            int result = appConfigMapper.deleteByAppNameAndKey(appName, configKey);
            log.info("删除应用配置成功: {} - {}", appName, configKey);
            return result > 0;
        } catch (Exception e) {
            log.error("删除应用配置失败: {} - {}", appName, configKey, e);
            return false;
        }
    }
    
    @Override
    @Cacheable(value = "app-configs", key = "#appName + ':all'")
    public List<AppConfig> getConfigsByAppName(String appName) {
        try {
            return appConfigMapper.selectByAppName(appName);
        } catch (Exception e) {
            log.error("获取应用所有配置失败: {}", appName, e);
            return null;
        }
    }
    
    @Override
    public List<AppConfig> getAllConfigs() {
        try {
            return appConfigMapper.selectAll();
        } catch (Exception e) {
            log.error("获取所有应用配置失败", e);
            return null;
        }
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "app-configs", allEntries = true)
    public boolean batchSetConfigs(List<AppConfig> configs) {
        try {
            for (AppConfig config : configs) {
                config.setCreatedTime(LocalDateTime.now());
                config.setUpdatedTime(LocalDateTime.now());
                appConfigMapper.insertOrUpdate(config);
            }
            log.info("批量设置应用配置成功，共{}条", configs.size());
            return true;
        } catch (Exception e) {
            log.error("批量设置应用配置失败", e);
            return false;
        }
    }
}
