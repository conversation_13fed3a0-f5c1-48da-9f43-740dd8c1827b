package com.inxaiot.edge.service.app;

import com.inxaiot.edge.entity.app.AppConfig;

import java.util.List;

/**
 * 应用配置服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface AppConfigService {
    
    /**
     * 设置应用配置
     */
    boolean setConfig(String appName, String configKey, String configValue, String configType, String description);
    
    /**
     * 获取应用配置
     */
    String getConfig(String appName, String configKey);
    
    /**
     * 获取应用配置对象
     */
    AppConfig getConfigObject(String appName, String configKey);
    
    /**
     * 删除应用配置
     */
    boolean deleteConfig(String appName, String configKey);
    
    /**
     * 根据应用名称获取所有配置
     */
    List<AppConfig> getConfigsByAppName(String appName);
    
    /**
     * 获取所有应用配置
     */
    List<AppConfig> getAllConfigs();
    
    /**
     * 批量设置应用配置
     */
    boolean batchSetConfigs(List<AppConfig> configs);
}
