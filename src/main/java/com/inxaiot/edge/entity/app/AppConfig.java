package com.inxaiot.edge.entity.app;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 应用配置实体
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AppConfig {
    
    /** 主键ID */
    private Long id;
    
    /** 应用名称 */
    private String appName;
    
    /** 配置键 */
    private String configKey;
    
    /** 配置值 */
    private String configValue;
    
    /** 配置类型 */
    private String configType;
    
    /** 配置描述 */
    private String description;
    
    /** 创建时间 */
    private LocalDateTime createdTime;
    
    /** 更新时间 */
    private LocalDateTime updatedTime;
}
