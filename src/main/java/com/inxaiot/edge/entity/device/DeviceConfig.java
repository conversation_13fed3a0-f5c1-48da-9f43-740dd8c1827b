package com.inxaiot.edge.entity.device;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 设备配置实体
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DeviceConfig {
    
    /** 主键ID */
    private Long id;
    
    /** 设备ID */
    private String deviceId;
    
    /** 配置键 */
    private String configKey;
    
    /** 配置值 */
    private String configValue;
    
    /** 配置类型 */
    private String configType;
    
    /** 配置描述 */
    private String description;
    
    /** 创建时间 */
    private LocalDateTime createdTime;
    
    /** 更新时间 */
    private LocalDateTime updatedTime;
}
