package com.inxaiot.edge.entity.device;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 设备状态实体
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DeviceStatus {
    
    /** 主键ID */
    private Long id;
    
    /** 设备ID */
    private String deviceId;
    
    /** 属性名称 */
    private String propertyName;
    
    /** 属性值 */
    private String propertyValue;
    
    /** 属性类型：string, number, boolean, json */
    private String propertyType;
    
    /** 单位 */
    private String unit;
    
    /** 时间戳 */
    private LocalDateTime timestamp;
}
