package com.inxaiot.edge.entity.device;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 设备信息实体
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DeviceInfo {
    
    /** 主键ID */
    private Long id;
    
    /** 设备ID */
    private String deviceId;
    
    /** 设备名称 */
    private String deviceName;
    
    /** 设备类型 */
    private String deviceType;
    
    /** 设备型号 */
    private String deviceModel;
    
    /** 制造商 */
    private String manufacturer;
    
    /** 固件版本 */
    private String firmwareVersion;
    
    /** 硬件版本 */
    private String hardwareVersion;
    
    /** 设备位置 */
    private String location;
    
    /** 设备描述 */
    private String description;
    
    /** 设备状态：1在线 0离线 -1故障 */
    private Integer status;
    
    /** 创建时间 */
    private LocalDateTime createdTime;
    
    /** 更新时间 */
    private LocalDateTime updatedTime;
}
