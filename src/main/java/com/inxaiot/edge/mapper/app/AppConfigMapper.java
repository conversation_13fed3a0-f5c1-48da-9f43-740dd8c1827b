package com.inxaiot.edge.mapper.app;

import com.inxaiot.edge.entity.app.AppConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用配置数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface AppConfigMapper {
    
    /**
     * 插入应用配置
     */
    int insert(AppConfig appConfig);
    
    /**
     * 插入或更新应用配置
     */
    int insertOrUpdate(AppConfig appConfig);
    
    /**
     * 根据应用名称和配置键删除配置
     */
    int deleteByAppNameAndKey(@Param("appName") String appName, @Param("configKey") String configKey);
    
    /**
     * 根据应用名称删除所有配置
     */
    int deleteByAppName(String appName);
    
    /**
     * 更新应用配置
     */
    int update(AppConfig appConfig);
    
    /**
     * 根据应用名称和配置键查询配置
     */
    AppConfig selectByAppNameAndKey(@Param("appName") String appName, @Param("configKey") String configKey);
    
    /**
     * 根据应用名称查询所有配置
     */
    List<AppConfig> selectByAppName(String appName);
    
    /**
     * 查询所有应用配置
     */
    List<AppConfig> selectAll();
}
