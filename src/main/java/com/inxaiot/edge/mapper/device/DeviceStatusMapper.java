package com.inxaiot.edge.mapper.device;

import com.inxaiot.edge.entity.device.DeviceStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备状态数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface DeviceStatusMapper {
    
    /**
     * 插入或更新设备状态
     */
    int insertOrUpdate(DeviceStatus deviceStatus);
    
    /**
     * 根据设备ID和属性名删除状态
     */
    int deleteByDeviceIdAndProperty(@Param("deviceId") String deviceId, @Param("propertyName") String propertyName);
    
    /**
     * 根据设备ID删除所有状态
     */
    int deleteByDeviceId(String deviceId);
    
    /**
     * 根据设备ID查询所有状态
     */
    List<DeviceStatus> selectByDeviceId(String deviceId);
    
    /**
     * 根据设备ID和属性名查询状态
     */
    DeviceStatus selectByDeviceIdAndProperty(@Param("deviceId") String deviceId, @Param("propertyName") String propertyName);
    
    /**
     * 批量插入或更新设备状态
     */
    int batchInsertOrUpdate(List<DeviceStatus> deviceStatusList);
}
