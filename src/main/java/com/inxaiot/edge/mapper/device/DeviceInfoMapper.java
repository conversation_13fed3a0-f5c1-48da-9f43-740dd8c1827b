package com.inxaiot.edge.mapper.device;

import com.inxaiot.edge.entity.device.DeviceInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备信息数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface DeviceInfoMapper {
    
    /**
     * 插入设备信息
     */
    int insert(DeviceInfo deviceInfo);
    
    /**
     * 根据ID删除设备信息
     */
    int deleteById(Long id);
    
    /**
     * 根据设备ID删除设备信息
     */
    int deleteByDeviceId(String deviceId);
    
    /**
     * 更新设备信息
     */
    int update(DeviceInfo deviceInfo);
    
    /**
     * 根据ID查询设备信息
     */
    DeviceInfo selectById(Long id);
    
    /**
     * 根据设备ID查询设备信息
     */
    DeviceInfo selectByDeviceId(String deviceId);
    
    /**
     * 查询所有设备信息
     */
    List<DeviceInfo> selectAll();
    
    /**
     * 根据设备类型查询设备信息
     */
    List<DeviceInfo> selectByDeviceType(String deviceType);
    
    /**
     * 根据状态查询设备信息
     */
    List<DeviceInfo> selectByStatus(Integer status);
    
    /**
     * 分页查询设备信息
     */
    List<DeviceInfo> selectByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询设备总数
     */
    int selectCount();
    
    /**
     * 更新设备状态
     */
    int updateStatus(@Param("deviceId") String deviceId, @Param("status") Integer status);
}
