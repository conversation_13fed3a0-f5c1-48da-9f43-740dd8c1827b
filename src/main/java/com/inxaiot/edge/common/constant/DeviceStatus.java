package com.inxaiot.edge.common.constant;

/**
 * 设备状态常量
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class DeviceStatus {
    
    /** 在线 */
    public static final int ONLINE = 1;
    
    /** 离线 */
    public static final int OFFLINE = 0;
    
    /** 故障 */
    public static final int FAULT = -1;
    
    /**
     * 获取状态描述
     */
    public static String getStatusDesc(int status) {
        switch (status) {
            case ONLINE:
                return "在线";
            case OFFLINE:
                return "离线";
            case FAULT:
                return "故障";
            default:
                return "未知";
        }
    }
}
