package com.inxaiot.edge.common.controller;

import com.inxaiot.edge.common.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/health")
public class HealthController {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 健康检查
     */
    @GetMapping
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        
        // 检查数据库连接
        try (Connection connection = dataSource.getConnection()) {
            status.put("database", "UP");
        } catch (Exception e) {
            log.error("数据库连接检查失败", e);
            status.put("database", "DOWN");
        }
        
        // 检查Redis连接
        try {
            redisTemplate.opsForValue().set("health:check", "OK");
            String result = (String) redisTemplate.opsForValue().get("health:check");
            if ("OK".equals(result)) {
                status.put("redis", "UP");
            } else {
                status.put("redis", "DOWN");
            }
        } catch (Exception e) {
            log.error("Redis连接检查失败", e);
            status.put("redis", "DOWN");
        }
        
        status.put("service", "UP");
        status.put("timestamp", System.currentTimeMillis());
        
        return ApiResponse.success(status);
    }
    
    /**
     * 服务信息
     */
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        info.put("service", "device-edge");
        info.put("version", "1.0.0");
        info.put("description", "Edge device management service for IoT building automation");
        info.put("timestamp", System.currentTimeMillis());
        
        return ApiResponse.success(info);
    }
}
