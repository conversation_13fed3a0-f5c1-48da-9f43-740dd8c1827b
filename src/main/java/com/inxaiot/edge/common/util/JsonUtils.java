package com.inxaiot.edge.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * JSON工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class JsonUtils {
    
    /**
     * 对象转JSON字符串
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            log.error("对象转JSON字符串失败", e);
            return null;
        }
    }
    
    /**
     * JSON字符串转对象
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (Exception e) {
            log.error("JSON字符串转对象失败: {}", jsonString, e);
            return null;
        }
    }
    
    /**
     * JSON字符串转JSONObject
     */
    public static JSONObject parseObject(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString);
        } catch (Exception e) {
            log.error("JSON字符串转JSONObject失败: {}", jsonString, e);
            return null;
        }
    }
    
    /**
     * 判断字符串是否为有效JSON
     */
    public static boolean isValidJson(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return false;
        }
        try {
            JSON.parse(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
