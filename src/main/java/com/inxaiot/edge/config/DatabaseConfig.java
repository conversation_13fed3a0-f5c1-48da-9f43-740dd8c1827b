package com.inxaiot.edge.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 数据库初始化配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
public class DatabaseConfig implements CommandLineRunner {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public void run(String... args) throws Exception {
        try {
            // 执行数据库初始化脚本
            executeSqlScript("schema.sql");
            log.info("数据库表结构初始化完成");
            
            // 执行测试数据脚本（仅在开发环境）
            String activeProfile = System.getProperty("spring.profiles.active", "dev");
            if ("dev".equals(activeProfile)) {
                executeSqlScript("data.sql");
                log.info("测试数据初始化完成");
            }
            
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
        }
    }
    
    private void executeSqlScript(String scriptPath) {
        try {
            ClassPathResource resource = new ClassPathResource(scriptPath);
            if (!resource.exists()) {
                log.warn("SQL脚本文件不存在: {}", scriptPath);
                return;
            }
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                
                StringBuilder sqlBuilder = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    line = line.trim();

                    // 跳过注释和空行
                    if (line.isEmpty() || line.startsWith("--")) {
                        continue;
                    }

                    // 处理行内注释
                    int commentIndex = line.indexOf("--");
                    if (commentIndex >= 0) {
                        line = line.substring(0, commentIndex).trim();
                    }

                    // 跳过处理后的空行
                    if (line.isEmpty()) {
                        continue;
                    }

                    sqlBuilder.append(line).append(" ");

                    // 如果行以分号结尾，执行SQL语句
                    if (line.endsWith(";")) {
                        String sql = sqlBuilder.toString().trim();
                        if (!sql.isEmpty()) {
                            try {
                                jdbcTemplate.execute(sql);
                                log.debug("执行SQL: {}", sql.substring(0, Math.min(sql.length(), 50)) + "...");
                            } catch (Exception e) {
                                log.warn("执行SQL失败: {}", sql, e);
                            }
                        }
                        sqlBuilder.setLength(0);
                    }
                }
                
                // 执行最后一条SQL（如果没有分号结尾）
                String remainingSql = sqlBuilder.toString().trim();
                if (!remainingSql.isEmpty()) {
                    try {
                        jdbcTemplate.execute(remainingSql);
                        log.debug("执行SQL: {}", remainingSql.substring(0, Math.min(remainingSql.length(), 50)) + "...");
                    } catch (Exception e) {
                        log.warn("执行SQL失败: {}", remainingSql, e);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("读取SQL脚本失败: {}", scriptPath, e);
        }
    }
}
