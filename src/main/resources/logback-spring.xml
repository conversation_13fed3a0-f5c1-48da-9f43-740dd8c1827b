<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 定义日志路径变量 -->
    <springProperty scope="context" name="LOG_PATH" source="device.edge.log.path" defaultValue="logs"/>

    <!-- 主日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/device-edge.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/device-edge.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 设备操作专用日志文件 -->
    <appender name="DEVICE_OPERATION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/device-operation.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/device-operation.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 设备操作日志记录器 -->
    <logger name="com.inxaiot.edge.device.service.DeviceOperationLogger" level="INFO" additivity="false">
        <appender-ref ref="DEVICE_OPERATION_FILE"/>
    </logger>

    <!-- 设备边缘服务相关日志 -->
    <logger name="com.inxaiot.edge" level="INFO"/>

    <!-- 设备管理相关日志 -->
    <logger name="com.inxaiot.edge.device" level="INFO"/>

    <!-- 应用功能相关日志 -->
    <logger name="com.inxaiot.edge.app" level="INFO"/>

    <!-- MQTT相关日志 -->
    <logger name="com.inxaiot.edge.common.mqtt" level="INFO"/>

    <!-- Redis相关日志 -->
    <logger name="org.springframework.data.redis" level="INFO"/>

    <!-- Spring框架日志 -->
    <logger name="org.springframework" level="INFO"/>

    <!-- 数据库相关日志 -->
    <logger name="org.mybatis" level="INFO"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>

    <!-- 根日志记录器 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
    
</configuration>
