# 开发环境配置
server:
  port: 6002

spring:
    
  # Redis配置 - 开发环境可选
  redis:
    host: ***********
    port: 6379
    password: inxvision
    database: 0
    timeout: 5000ms
    jedis:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: 3000ms

# 设备边缘服务配置
device:
  edge:
    # 数据存储路径
    data:
      path: D:/code/work/invision-edge/device-edge/data
    # 日志路径
    log:
      path: D:/code/work/invision-edge/device-edge/logs
    
    debug: true

# 日志配置
logging:
  level:
    com.inxaiot.edge: DEBUG
    org.springframework: INFO
    org.mybatis: DEBUG
