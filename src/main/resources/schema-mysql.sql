-- MySQL数据库初始化脚本

-- 设备信息表
CREATE TABLE IF NOT EXISTS device_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(64) NOT NULL UNIQUE COMMENT '设备ID',
    device_name VARCHAR(128) NOT NULL COMMENT '设备名称',
    device_type VARCHAR(32) NOT NULL COMMENT '设备类型',
    device_model VARCHAR(64) COMMENT '设备型号',
    manufacturer VARCHAR(64) COMMENT '制造商',
    firmware_version VARCHAR(32) COMMENT '固件版本',
    hardware_version VARCHAR(32) COMMENT '硬件版本',
    location VARCHAR(128) COMMENT '设备位置',
    description TEXT COMMENT '设备描述',
    status TINYINT DEFAULT 1 COMMENT '设备状态：1在线 0离线 -1故障',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';

-- 设备状态表
CREATE TABLE IF NOT EXISTS device_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    property_name VARCHAR(64) NOT NULL COMMENT '属性名称',
    property_value TEXT COMMENT '属性值',
    property_type VARCHAR(16) DEFAULT 'string' COMMENT '属性类型：string, number, boolean, json',
    unit VARCHAR(16) COMMENT '单位',
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    UNIQUE KEY uk_device_property (device_id, property_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备状态表';

-- 设备配置表
CREATE TABLE IF NOT EXISTS device_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    config_key VARCHAR(64) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(16) DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_device_config (device_id, config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备配置表';

-- 设备日志表
CREATE TABLE IF NOT EXISTS device_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(64) NOT NULL COMMENT '设备ID',
    log_level VARCHAR(16) NOT NULL COMMENT '日志级别：DEBUG, INFO, WARN, ERROR',
    log_message TEXT NOT NULL COMMENT '日志消息',
    log_data TEXT COMMENT '日志数据(JSON格式)',
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备日志表';

-- 应用配置表
CREATE TABLE IF NOT EXISTS app_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    app_name VARCHAR(64) NOT NULL COMMENT '应用名称',
    config_key VARCHAR(64) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(16) DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_app_config (app_name, config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用配置表';

-- 应用日志表
CREATE TABLE IF NOT EXISTS app_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    app_name VARCHAR(64) NOT NULL COMMENT '应用名称',
    log_level VARCHAR(16) NOT NULL COMMENT '日志级别',
    log_message TEXT NOT NULL COMMENT '日志消息',
    log_data TEXT COMMENT '日志数据',
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用日志表';

-- 创建索引
CREATE INDEX idx_device_info_device_id ON device_info(device_id);
CREATE INDEX idx_device_info_status ON device_info(status);
CREATE INDEX idx_device_status_device_id ON device_status(device_id);
CREATE INDEX idx_device_status_timestamp ON device_status(timestamp);
CREATE INDEX idx_device_config_device_id ON device_config(device_id);
CREATE INDEX idx_device_log_device_id ON device_log(device_id);
CREATE INDEX idx_device_log_timestamp ON device_log(timestamp);
CREATE INDEX idx_app_config_app_name ON app_config(app_name);
CREATE INDEX idx_app_log_app_name ON app_log(app_name);
CREATE INDEX idx_app_log_timestamp ON app_log(timestamp);
