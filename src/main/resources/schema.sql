-- SQLite数据库初始化脚本

-- 设备信息表
CREATE TABLE IF NOT EXISTS device_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id VARCHAR(64) NOT NULL UNIQUE,
    device_name VARCHAR(128) NOT NULL,
    device_type VARCHAR(32) NOT NULL,
    device_model VARCHAR(64),
    manufacturer VA<PERSON><PERSON>R(64),
    firmware_version VA<PERSON>HAR(32),
    hardware_version VARCHAR(32),
    location VARCHAR(128),
    description TEXT,
    status INTEGER DEFAULT 1,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 设备状态表
CREATE TABLE IF NOT EXISTS device_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id VARCHAR(64) NOT NULL,
    property_name VARCHAR(64) NOT NULL,
    property_value TEXT,
    property_type VARCHAR(16) DEFAULT 'string',
    unit VARCHAR(16),
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(device_id, property_name)
);

-- 设备配置表
CREATE TABLE IF NOT EXISTS device_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id VARCHAR(64) NOT NULL,
    config_key VARCHAR(64) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(16) DEFAULT 'string',
    description TEXT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(device_id, config_key)
);

-- 设备日志表
CREATE TABLE IF NOT EXISTS device_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id VARCHAR(64) NOT NULL,
    log_level VARCHAR(16) NOT NULL,
    log_message TEXT NOT NULL,
    log_data TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 应用配置表
CREATE TABLE IF NOT EXISTS app_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app_name VARCHAR(64) NOT NULL,
    config_key VARCHAR(64) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(16) DEFAULT 'string',
    description TEXT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(app_name, config_key)
);

-- 应用日志表
CREATE TABLE IF NOT EXISTS app_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app_name VARCHAR(64) NOT NULL,
    log_level VARCHAR(16) NOT NULL,
    log_message TEXT NOT NULL,
    log_data TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_device_info_device_id ON device_info(device_id);
CREATE INDEX IF NOT EXISTS idx_device_info_status ON device_info(status);
CREATE INDEX IF NOT EXISTS idx_device_status_device_id ON device_status(device_id);
CREATE INDEX IF NOT EXISTS idx_device_status_timestamp ON device_status(timestamp);
CREATE INDEX IF NOT EXISTS idx_device_config_device_id ON device_config(device_id);
CREATE INDEX IF NOT EXISTS idx_device_log_device_id ON device_log(device_id);
CREATE INDEX IF NOT EXISTS idx_device_log_timestamp ON device_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_app_config_app_name ON app_config(app_name);
CREATE INDEX IF NOT EXISTS idx_app_log_app_name ON app_log(app_name);
CREATE INDEX IF NOT EXISTS idx_app_log_timestamp ON app_log(timestamp);
