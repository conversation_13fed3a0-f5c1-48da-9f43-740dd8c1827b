<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inxaiot.edge.mapper.app.AppConfigMapper">

    <resultMap id="BaseResultMap" type="com.inxaiot.edge.entity.app.AppConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="config_key" property="configKey" jdbcType="VARCHAR"/>
        <result column="config_value" property="configValue" jdbcType="VARCHAR"/>
        <result column="config_type" property="configType" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app_name, config_key, config_value, config_type, description, created_time, updated_time
    </sql>

    <insert id="insert" parameterType="com.inxaiot.edge.entity.app.AppConfig">
        INSERT INTO app_config (
            app_name, config_key, config_value, config_type, description, created_time, updated_time
        ) VALUES (
            #{appName}, #{configKey}, #{configValue}, #{configType}, #{description}, #{createdTime}, #{updatedTime}
        )
    </insert>

    <insert id="insertOrUpdate" parameterType="com.inxaiot.edge.entity.app.AppConfig">
        INSERT OR REPLACE INTO app_config (
            app_name, config_key, config_value, config_type, description, created_time, updated_time
        ) VALUES (
            #{appName}, #{configKey}, #{configValue}, #{configType}, #{description}, #{createdTime}, #{updatedTime}
        )
    </insert>

    <delete id="deleteByAppNameAndKey">
        DELETE FROM app_config 
        WHERE app_name = #{appName} AND config_key = #{configKey}
    </delete>

    <delete id="deleteByAppName" parameterType="java.lang.String">
        DELETE FROM app_config WHERE app_name = #{appName}
    </delete>

    <update id="update" parameterType="com.inxaiot.edge.entity.app.AppConfig">
        UPDATE app_config
        <set>
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="configType != null">config_type = #{configType},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </set>
        WHERE app_name = #{appName} AND config_key = #{configKey}
    </update>

    <select id="selectByAppNameAndKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM app_config
        WHERE app_name = #{appName} AND config_key = #{configKey}
    </select>

    <select id="selectByAppName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM app_config
        WHERE app_name = #{appName}
        ORDER BY created_time DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM app_config
        ORDER BY app_name, created_time DESC
    </select>

</mapper>
