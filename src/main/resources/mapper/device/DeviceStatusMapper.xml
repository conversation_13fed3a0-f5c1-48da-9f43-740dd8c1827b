<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inxaiot.edge.mapper.device.DeviceStatusMapper">

    <resultMap id="BaseResultMap" type="com.inxaiot.edge.entity.device.DeviceStatus">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="property_name" property="propertyName" jdbcType="VARCHAR"/>
        <result column="property_value" property="propertyValue" jdbcType="VARCHAR"/>
        <result column="property_type" property="propertyType" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="timestamp" property="timestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, device_id, property_name, property_value, property_type, unit, timestamp
    </sql>

    <insert id="insertOrUpdate" parameterType="com.inxaiot.edge.entity.device.DeviceStatus">
        INSERT OR REPLACE INTO device_status (
            device_id, property_name, property_value, property_type, unit, timestamp
        ) VALUES (
            #{deviceId}, #{propertyName}, #{propertyValue}, #{propertyType}, #{unit}, #{timestamp}
        )
    </insert>

    <delete id="deleteByDeviceIdAndProperty">
        DELETE FROM device_status 
        WHERE device_id = #{deviceId} AND property_name = #{propertyName}
    </delete>

    <delete id="deleteByDeviceId" parameterType="java.lang.String">
        DELETE FROM device_status WHERE device_id = #{deviceId}
    </delete>

    <select id="selectByDeviceId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_status
        WHERE device_id = #{deviceId}
        ORDER BY timestamp DESC
    </select>

    <select id="selectByDeviceIdAndProperty" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_status
        WHERE device_id = #{deviceId} AND property_name = #{propertyName}
    </select>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            INSERT OR REPLACE INTO device_status (
                device_id, property_name, property_value, property_type, unit, timestamp
            ) VALUES (
                #{item.deviceId}, #{item.propertyName}, #{item.propertyValue},
                #{item.propertyType}, #{item.unit}, #{item.timestamp}
            )
        </foreach>
    </insert>

</mapper>
