<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inxaiot.edge.mapper.device.DeviceInfoMapper">

    <resultMap id="BaseResultMap" type="com.inxaiot.edge.entity.device.DeviceInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="device_type" property="deviceType" jdbcType="VARCHAR"/>
        <result column="device_model" property="deviceModel" jdbcType="VARCHAR"/>
        <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR"/>
        <result column="firmware_version" property="firmwareVersion" jdbcType="VARCHAR"/>
        <result column="hardware_version" property="hardwareVersion" jdbcType="VARCHAR"/>
        <result column="location" property="location" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, device_id, device_name, device_type, device_model, manufacturer,
        firmware_version, hardware_version, location, description, status,
        created_time, updated_time
    </sql>

    <insert id="insert" parameterType="com.inxaiot.edge.entity.device.DeviceInfo">
        INSERT INTO device_info (
            device_id, device_name, device_type, device_model, manufacturer,
            firmware_version, hardware_version, location, description, status,
            created_time, updated_time
        ) VALUES (
            #{deviceId}, #{deviceName}, #{deviceType}, #{deviceModel}, #{manufacturer},
            #{firmwareVersion}, #{hardwareVersion}, #{location}, #{description}, #{status},
            #{createdTime}, #{updatedTime}
        )
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM device_info WHERE id = #{id}
    </delete>

    <delete id="deleteByDeviceId" parameterType="java.lang.String">
        DELETE FROM device_info WHERE device_id = #{deviceId}
    </delete>

    <update id="update" parameterType="com.inxaiot.edge.entity.device.DeviceInfo">
        UPDATE device_info
        <set>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceModel != null">device_model = #{deviceModel},</if>
            <if test="manufacturer != null">manufacturer = #{manufacturer},</if>
            <if test="firmwareVersion != null">firmware_version = #{firmwareVersion},</if>
            <if test="hardwareVersion != null">hardware_version = #{hardwareVersion},</if>
            <if test="location != null">location = #{location},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </set>
        WHERE device_id = #{deviceId}
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_info
        WHERE id = #{id}
    </select>

    <select id="selectByDeviceId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_info
        WHERE device_id = #{deviceId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_info
        ORDER BY created_time DESC
    </select>

    <select id="selectByDeviceType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_info
        WHERE device_type = #{deviceType}
        ORDER BY created_time DESC
    </select>

    <select id="selectByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_info
        WHERE status = #{status}
        ORDER BY created_time DESC
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM device_info
        ORDER BY created_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM device_info
    </select>

    <update id="updateStatus">
        UPDATE device_info
        SET status = #{status}, updated_time = CURRENT_TIMESTAMP
        WHERE device_id = #{deviceId}
    </update>

</mapper>
