server:
  port: 6002
  servlet:
    context-path: /

spring:
  application:
    name: iot-device-edge

  # 配置文件导入
  profiles:
    active: dev
    include: mqtt
  
  # 数据源配置 - 支持MySQL和SQLite切换
  datasource:
    # mysql采用：DB_DRIVER_CLASS=com.mysql.cj.jdbc.Driver
    driver-class-name: ${DB_DRIVER_CLASS:org.sqlite.JDBC}
    # mysql采用：DB_URL=************************************************************************************************************************
    url: ${DB_URL:jdbc:sqlite:${device.edge.data.path}/device_edge.db}
    username: ${DB_USERNAME:}
    password: ${DB_PASSWORD:}
    
  # Redis配置
  redis:
    host: ${REDIS_HOST:***********}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 5000ms
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000  # 10分钟
      cache-null-values: false

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.inxaiot.edge.entity.device,com.inxaiot.edge.entity.app
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000

# 设备边缘服务配置
device:
  edge:
    # 基础标识配置
    id: device-edge-service
    version: 1.0.0
    timezone: Asia/Shanghai
    locale: zh_CN
    debug: false

    # 数据存储路径
    data:
      path: ${DATA_PATH:./data}
    # 日志路径
    log:
      path: ${LOG_PATH:/data/device-edge/logs}
    
    # 设备管理配置
    device:
      # 设备状态缓存大小
      cache-size: 10000
      # 状态清理间隔(分钟)
      cleanup-interval: 60
      # 设备心跳超时时间(秒)
      heartbeat-timeout: 300
      # 设备离线检测间隔(秒)
      offline-check-interval: 60
    
    # 应用功能配置
    app:
      # 应用缓存大小
      cache-size: 1000
      # 应用数据刷新间隔(分钟)
      refresh-interval: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,redis
  endpoint:
    health:
      show-details: when-authorized
