-- 测试数据初始化脚本

-- 插入测试设备信息
INSERT OR IGNORE INTO device_info (device_id, device_name, device_type, device_model, manufacturer, location, description, status, created_time, updated_time) VALUES
('device001', '温度传感器01', 'sensor', 'TH-001', 'InxaIoT', '会议室A', '温湿度传感器', 1, datetime('now'), datetime('now')),
('device002', '智能开关01', 'switch', 'SW-001', 'InxaIoT', '办公室B', '智能照明开关', 1, datetime('now'), datetime('now')),
('device003', '空调控制器01', 'controller', 'AC-001', 'InxaIoT', '大厅', '中央空调控制器', 0, datetime('now'), datetime('now'));

-- 插入测试设备状态
INSERT OR REPLACE INTO device_status (device_id, property_name, property_value, property_type, unit, timestamp) VALUES
('device001', 'temperature', '23.5', 'number', '°C', datetime('now')),
('device001', 'humidity', '65.2', 'number', '%', datetime('now')),
('device002', 'switch_status', 'on', 'string', '', datetime('now')),
('device002', 'brightness', '80', 'number', '%', datetime('now')),
('device003', 'power_status', 'off', 'string', '', datetime('now')),
('device003', 'target_temperature', '26', 'number', '°C', datetime('now'));

-- 插入测试设备配置
INSERT OR REPLACE INTO device_config (device_id, config_key, config_value, config_type, description, created_time, updated_time) VALUES
('device001', 'report_interval', '30', 'number', '数据上报间隔(秒)', datetime('now'), datetime('now')),
('device001', 'temp_threshold_high', '30', 'number', '温度高阈值', datetime('now'), datetime('now')),
('device001', 'temp_threshold_low', '10', 'number', '温度低阈值', datetime('now'), datetime('now')),
('device002', 'auto_mode', 'true', 'boolean', '自动模式开关', datetime('now'), datetime('now')),
('device003', 'eco_mode', 'false', 'boolean', '节能模式', datetime('now'), datetime('now'));

-- 插入测试应用配置
INSERT OR REPLACE INTO app_config (app_name, config_key, config_value, config_type, description, created_time, updated_time) VALUES
('system', 'max_devices', '1000', 'number', '最大设备数量', datetime('now'), datetime('now')),
('system', 'log_level', 'INFO', 'string', '日志级别', datetime('now'), datetime('now')),
('system', 'enable_mqtt', 'true', 'boolean', '启用MQTT', datetime('now'), datetime('now')),
('monitoring', 'check_interval', '60', 'number', '监控检查间隔(秒)', datetime('now'), datetime('now')),
('monitoring', 'alert_email', '<EMAIL>', 'string', '告警邮箱', datetime('now'), datetime('now'));
