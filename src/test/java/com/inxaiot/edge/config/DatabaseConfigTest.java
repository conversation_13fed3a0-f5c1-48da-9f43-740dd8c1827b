package com.inxaiot.edge.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 数据库配置测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootTest
public class DatabaseConfigTest {
    
    @Test
    public void testSqlParsing() {
        // 测试SQL解析逻辑
        String testLine = "status INTEGER DEFAULT 1, -- 1:在线 0:离线 -1:故障";
        
        // 处理行内注释
        int commentIndex = testLine.indexOf("--");
        if (commentIndex >= 0) {
            testLine = testLine.substring(0, commentIndex).trim();
        }
        
        System.out.println("处理后的SQL: " + testLine);
        
        // 应该输出: status INTEGER DEFAULT 1,
        assert testLine.equals("status INTEGER DEFAULT 1,");
    }
}
