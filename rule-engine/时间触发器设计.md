

### **边缘规则引擎时间与事件触发器**

#### 1. 最终方案概述

本方案采用一种**混合模式（Hybrid Approach）** 来处理边缘规则引擎中的时间与事件触发逻辑。它围绕轻量级的**Easy Rules**库，通过自定义Java组件，精确且高效地处理所有业务场景。方案的核心是将规则明确区分为“事件驱动”和“时间驱动”两种类型，并对“时间驱动”的规则进一步智能推断其为“时间点”或“时间段”模式，分别采用无状态和有状态的逻辑进行处理，最终通过一个专用的线程池异步执行所有规则动作，确保系统的高响应性和健壮性。

#### 2. 核心设计原则

1. **职责分离 (Separation of Concerns):**
   
   - **主动调度 vs. 被动评估:** `TimeTriggerService`作为主动调度器，负责周期性地“唤醒”时间驱动的规则。`TimeConditionEvaluator`作为被动评估器，按需对任何规则进行复杂的时间条件“合规”检查。
   - **规则逻辑 vs. 执行逻辑:** Easy Rules及其适配器专注于表达和判断规则的“条件-动作”逻辑。`RuleEngineTriggerService`和`ActionExecutor`则负责编排和执行这些逻辑，并处理异步化。

2. **双层触发模式区分 (Dual-level Trigger Distinction):**
   
   - **第一层（显式定义）:** 在`EdgeRuleDefinition`中通过`triggerType`枚举，明确区分规则是由**设备事件驱动 (`EVENT_DRIVEN`)** 还是由**时间驱动 (`TIME_DRIVEN`)**。
   - **第二层（隐式推断）:** 对于`TIME_DRIVEN`的规则，系统会根据其`TimeConditions`（主要是Cron表达式的结构）**动态推断**它是**时间点 (`POINT`)** 模式还是**时间段 (`RANGE`)** 模式。

3. **状态化与无状态结合:**
   
   - 对推断为`POINT`模式的规则，采用**无状态**处理，满足时间点即触发。
   - 对推断为`RANGE`模式的规则，采用**状态化**处理，通过内部状态机精确捕捉时间段的“进入”和“离开”瞬间，分别执行激活和失活动作。

4. **异步化与解耦:**
   
   - 所有规则的动作执行都将被提交到专用线程池中异步处理，避免阻塞核心调度和事件监听线程。

#### 3. 关键组件与最终职责

- **`TimeTriggerService`:** **核心调度器**。每分钟轮询一次，仅处理`TIME_DRIVEN`的规则，内部推断其为`POINT`或`RANGE`模式，并应用相应的（无状态或有状态）逻辑来触发规则。
- **`TimeConditionEvaluator`:** **时间条件专家**。按需被调用，用于评估任何规则的复杂“分层日历”时间条件是否满足。
- **`RuleEngineTriggerService`:** **规则触发服务**。提供统一的接口（如`processDeviceEvent`, `triggerRuleActivation`, `triggerRuleDeactivation`），负责接收来自不同源头（设备事件、时间触发器）的触发请求，并提交到动作执行线程池。
- **`ActionExecutor` (线程池):** **动作执行器**。一个`ExecutorService`实例，专门用于异步执行所有规则的动作。
- **`RuleAdapterService`:** **规则适配器**。将`EdgeRuleDefinition`转换为Easy Rules的`Rule`对象。其生成的`@Condition`逻辑会根据`triggerType`采用不同的判断策略。

#### 4. 最终数据模型

`EdgeRuleDefinition.java` 及其内部类的最终结构：

Java

```
// 核心规则定义POJO
public class EdgeRuleDefinition {
    public enum TriggerType {
        EVENT_DRIVEN, // 由设备事件驱动，时间作为守卫条件
        TIME_DRIVEN   // 由时间直接驱动
    }

    private String ruleId;
    private TriggerType triggerType = TriggerType.EVENT_DRIVEN; // 默认为事件驱动
    private String ruleName;
    private String targetDeviceCode;
    private int priority = 1;
    private boolean enabled = true;

    private TimeConditions timeConditions;
    private TriggerConditions triggerCondition;
    private List<ActionDefinition> actions;           // 激活时执行的动作
    private List<ActionDefinition> deactivationActions; // 失活时执行的动作 (仅用于RANGE模式)

    // Getters and Setters...
}

// 时间条件 (无需triggerMode字段)
class TimeConditions {
    private List<String> cronExpressions;
    private LocalDate seasonStartDate;
    private LocalDate seasonEndDate;
    private List<LocalDate> includeDates;
    private List<LocalDate> excludeDates;
    private boolean applyWorkdayLogic = true;
    // Getters and Setters...
}

// 设备触发条件 (无变化)
class TriggerConditions { /* ... */ }
class DeviceCondition { /* ... */ }

// 动作定义 (无变化)
class ActionDefinition { /* ... */ }
```

#### 5. 详细实现方案

**5.1. `TimeTriggerService` (时间触发器)**

Java

```
@Service
public class TimeTriggerService {
    private final Map<String, Boolean> previousTimeConditionState = new ConcurrentHashMap<>();
    private ScheduledExecutorService scheduler;
    private enum InferredTriggerMode { RANGE, POINT }
    // ... 其他依赖注入 ...

    @PostConstruct
    private void initialize() {
        // ... 启动一个每分钟执行一次的调度器，调用 this::checkForTimeTriggers ...
        logger.info("Initializing TimeTriggerService...");
        scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.scheduleAtFixedRate(this::checkForTimeTriggers, 1, 1, TimeUnit.MINUTES); // 每分钟执行
    }

    private void checkForTimeTriggers() {
        List<EdgeRuleDefinition> timeDrivenRules = ruleStorageService.findAllEnabledRules().stream()
                .filter(rule -> rule.getTriggerType() == EdgeRuleDefinition.TriggerType.TIME_DRIVEN)
                .collect(Collectors.toList());
        if (timeDrivenRules.isEmpty()) return;

        LocalDateTime now = LocalDateTime.now();

        for (EdgeRuleDefinition rule : timeDrivenRules) {
            String ruleId = rule.getRuleId();
            TimeConditions timeConditions = rule.getTimeConditions();
            if (timeConditions == null) continue;

            InferredTriggerMode mode = inferTriggerMode(timeConditions);
            boolean isNowActive = timeConditionEvaluator.isTimeConditionMet(timeConditions, now);

            if (mode == InferredTriggerMode.POINT) {
                if (isNowActive) {
                    ruleEngineTriggerService.triggerRuleActivation(ruleId);
                }
            } else { // RANGE
                boolean wasPreviouslyActive = previousTimeConditionState.getOrDefault(ruleId, false);
                if (isNowActive && !wasPreviouslyActive) {
                    ruleEngineTriggerService.triggerRuleActivation(ruleId);
                } else if (!isNowActive && wasPreviouslyActive) {
                    ruleEngineTriggerService.triggerRuleDeactivation(ruleId);
                }
                previousTimeConditionState.put(ruleId, isNowActive);
            }
        }
    }

    private InferredTriggerMode inferTriggerMode(TimeConditions conditions) {
        // ... 实现基于Cron表达式结构的推断逻辑 ...
        // 包含'-', '/', '*'在分钟或小时字段，则为RANGE，否则为POINT
    }
    // ... evictStateForRule() 和 shutdown() 方法 ...
}
```

**5.2. `RuleAdapterService` (最终版)**

适配器的`when`条件逻辑需要根据`triggerType`进行区分。

Java

```
.when(facts -> {
    if (!ruleDefinition.isEnabled()) return false;

    // 根据规则的触发类型，应用不同的条件判断逻辑
    if (ruleDefinition.getTriggerType() == EdgeRuleDefinition.TriggerType.TIME_DRIVEN) {
        // 对于时间驱动的规则，它的触发由TimeTriggerService发起，
        // 这里只需检查是否存在特定的“时间触发事实”即可。
        return Boolean.TRUE.equals(facts.get("timeTriggerEvent")) &&
               ruleDefinition.getRuleId().equals(facts.get("triggeredRuleId"));
    } else { // EVENT_DRIVEN
        // 对于事件驱动的规则，它的触发由设备事件发起，
        // 需要在这里同时检查“时间守卫条件”和“设备触发条件”。
        boolean timeMet = timeConditionEvaluator.isTimeConditionMet(ruleDefinition.getTimeConditions(), LocalDateTime.now());
        if (!timeMet) return false;

        return evaluateDeviceTriggerConditions(ruleDefinition, facts);
    }
})
```

**5.3. `RuleEngineTriggerService` (最终版)**

提供清晰的激活和失活方法，并提交到线程池。

Java

```
@Service
public class RuleEngineTriggerService {
    private final ExecutorService ruleActionExecutor;
    // ... 其他依赖 ...

    // 用于时间点触发 或 时间段开始
    public void triggerRuleActivation(String ruleId) {
        ruleActionExecutor.submit(() -> {
            EdgeRuleDefinition definition = ruleStorageService.findRuleById(ruleId);
            if (definition == null || !definition.isEnabled()) return;

            Facts facts = new Facts();
            facts.put("timeTriggerEvent", true);
            facts.put("triggeredRuleId", ruleId);
            addGlobalContextToFacts(facts);

            Rule easyRule = ruleAdapterService.adapt(definition);
            Rules rules = new Rules();
            rules.register(easyRule);
            easyRulesEngine.fire(rules, facts);
        });
    }

    // 仅用于时间段结束
    public void triggerRuleDeactivation(String ruleId) {
        ruleActionExecutor.submit(() -> {
            EdgeRuleDefinition definition = ruleStorageService.findRuleById(ruleId);
            if (definition == null || !definition.isEnabled() || definition.getDeactivationActions() == null) return;

            Facts facts = new Facts();
            addGlobalContextToFacts(facts);
            // 直接执行失活动作，不经过Easy Rules条件判断
            definition.getDeactivationActions().forEach(actionDef -> 
                actionExecutorService.executeAction(definition.getTargetDeviceCode(), actionDef, facts)
            );
        });
    }

    // 用于设备事件
    public void processDeviceEvent(String deviceCode, String pointId, Object value) {
        ruleActionExecutor.submit(() -> {
            Facts facts = new Facts();
            facts.put("triggeringDeviceCode", deviceCode);
            facts.put(pointId, value);
            addGlobalContextToFacts(facts);

            List<EdgeRuleDefinition> relevantDefinitions = ruleStorageService.findAllEnabledRulesRelevantToDevice(deviceCode)
                    .stream()
                    .filter(r -> r.getTriggerType() == EdgeRuleDefinition.TriggerType.EVENT_DRIVEN)
                    .collect(Collectors.toList());

            Rules easyRules = new Rules();
            relevantDefinitions.stream().map(ruleAdapterService::adapt).forEach(easyRules::register);

            if (!easyRules.isEmpty()) {
                easyRulesEngine.fire(easyRules, facts);
            }
        });
    }
    // ...
}
```

#### 6. 交互流程时序图

**场景A: 事件驱动规则 (Event-Driven)**

代码段

```
sequenceDiagram
    participant Device
    participant EdgeTransport as 数据传输模块
    participant TriggerSvc as RuleEngineTriggerService
    participant RuleAdapter as 规则适配器
    participant Evaluator as 时间条件评估服务

    Device->>EdgeTransport: 上报传感器数据
    EdgeTransport->>TriggerSvc: processDeviceEvent(facts)
    Note over TriggerSvc: 异步提交执行任务
    TriggerSvc->>RuleAdapter: adapt(EVENT_DRIVEN规则)
    RuleAdapter->>Evaluator: isTimeConditionMet()? (作为守卫)
    Evaluator-->>RuleAdapter: true/false
    Note over RuleAdapter: @Condition 返回 (时间条件 && 设备条件)
    RuleAdapter-->>TriggerSvc: 构建好的Easy Rules对象
    TriggerSvc->>TriggerSvc: easyRulesEngine.fire()
```

**场景B: 时间驱动规则 - 时间点模式 (Time-Driven - Point)**

代码段

```
sequenceDiagram
    participant Scheduler as TimeTriggerService
    participant Evaluator as 时间条件评估服务
    participant TriggerSvc as RuleEngineTriggerService

    Scheduler->>Scheduler: 每分钟执行 checkForTimeTriggers()
    Scheduler->>Evaluator: isTimeConditionMet(POINT规则)?
    Evaluator-->>Scheduler: true (当前时刻匹配)
    Note over Scheduler: 推断为POINT模式, 无状态, 直接触发
    Scheduler->>TriggerSvc: triggerRuleActivation(ruleId)
    Note over TriggerSvc: 异步执行激活任务...
```

**场景C: 时间驱动规则 - 时间段模式 (Time-Driven - Range)**

代码段

```
sequenceDiagram
    participant Scheduler as TimeTriggerService
    participant Evaluator as 时间条件评估服务
    participant TriggerSvc as RuleEngineTriggerService

    Note over Scheduler: 9:00, 检查...
    Scheduler->>Evaluator: isTimeConditionMet(RANGE规则)?
    Evaluator-->>Scheduler: true
    Note over Scheduler: isNowActive=true, wasActive=false. 触发激活!
    Scheduler->>TriggerSvc: triggerRuleActivation(ruleId)
    Note over Scheduler: 更新状态: previousState=true

    Note over Scheduler: 17:01, 检查...
    Scheduler->>Evaluator: isTimeConditionMet(RANGE规则)?
    Evaluator-->>Scheduler: false
    Note over Scheduler: isNowActive=false, wasActive=true. 触发失活!
    Scheduler->>TriggerSvc: triggerRuleDeactivation(ruleId)
    Note over Scheduler: 更新状态: previousState=false
```

---

#### 7. 总结

这份混合方案设计采纳了我们所有讨论的精华，最终形成了一个强大而灵活的架构。它通过明确的职责划分和智能的模式选择，既解决了“时间点”触发的精确性问题，又保留了“时间段”触发的上下文状态和生命周期管理能力，同时兼顾了性能、可维护性和团队的开发效率。此方案已足够详细，可以作为您后续开发工作的坚实基础。
