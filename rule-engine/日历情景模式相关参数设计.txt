参数值定义：
工作日值：MON、TUE、WED、THU、FRI、SAT、SUN
时令值：SUMMER、WINTER、ALL

##通用日历：

夏令间： 开始，结束
冬令时间： 开始，结束
工作日： 周一，周二、周三、周四、周五
假期：5.1/5.2/5.3/...



给前端/规则引擎

{
“summer”:
	{"start":"xxx",
	 "end":"xxx"
	},
 "winter":
 
 "workdays":
 "holidays":
}


##空间日志

日历编辑页面保存日历信息
空间ID，

summer:{
 exclude:月/日
 inlucde:月/日

  workday{}
}
winter{
 exclude:月/日
 inlucde:月/日

  workday{}
}
all{
 exclude:月/日
 inlucde:月/日
  workday{}
}

返回日历ID


日历列表保存空间和日历关联数据：
保存模式设置数据：
空间ID
模式ID
区域ID
情景点ID
日历ID列表
summer/winter/all
时间段列表



模式ID
情景ID
规则列表









