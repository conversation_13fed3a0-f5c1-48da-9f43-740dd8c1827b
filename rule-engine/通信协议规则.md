## Bacnet

- 读status：
  
  ```json
  { 
  "RPC1005": { //控制器
  
      "1_1_AHU_5_SWD": -21.565639 
  
  }, 
  
      "clientId": "inxvisionW7MGFZRcB4-bacnet", 
      "time": "1692256941228" 
  
  } 
  ```

- 写：

```json
  [ 
      {
      "operate": "write", 
      "seq":"1537690350375858123", 
      "deviceCode": "RPC1005", //-网络控制名称
      "tagCode": "1_1_AHU_1_ptm", //设备标识符和属性标识符拼接
      "val": "1" 值
      } 
  ]
```

## Modbus

- 读

```json
  {  

      " V_1F_DB_14": //平台拼接的电表名称
          "APwh": "198357*150* 0.001", //设备点位属性
          "CT": "150", 
          "Ia": "351*150* 0.0002", 
          "Ib": "269*150* 0.0002", 
          "Ic": "211*150* 0.0002", 
          "Pfav": "7700* 0.0001", 
          "Psum": "147*150* 0.2* 0.001", 
          "Ua": "22835* 0.01", 
          "Ub": "22959* 0.01", 
          "Uc": "22949* 0.01" 
      }, 
      "clientId": "inxvisionW7MGFZRcB4-modbus", 
      "time": "1692320863544" 

  }
```

- 写
  
  ```json
  [ 
  
      { 
          operate": "write", 
          "seq":"1537690350375858123", 
          "deviceCode": " L5_14F_vrf_34", //设备标识符
          "tagCode": "qt", //执行写入的标识符（属性标识符）
          "val": "1"
      } 
  
  ]
  ```

## KNX

- 读

```json
  { 
   "MTN647593-1": { // KNX照明模块标识符

        "2_1_WL_1_qt": 1, 
        "2_1_WL_1_zt": 1, 
        "2_1_WL_2_qt": 0, 
        "2_1_WL_2_zt": 0, 
        "2_1_WL_3_qt": 0, 
        "2_1_WL_3_zt": 0, 
        "2_1_WL_4_qt": 0, 
        "2_1_WL_4_zt": 0, 

   }, 
   "clientId": "inxvisionW7MGFZRcB4-knx", 
   "system": { 
    "MTN647593-1_status": 1 
   }, 
   "time": "1692328498843" 
  }
```

- 写

```json
  [ 

      { 
       "operate": "write", 
       "seq":"1537690350375858124", 
       "deviceCode": " MTN647593-1",// KNX照明模块标识符 
       "tagCode": "2_1_WL_1_qt ", //设备标识符和属性标识符拼接
       "val": "1" 
      } 

  ] 
```
