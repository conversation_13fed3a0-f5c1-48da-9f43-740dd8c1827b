## 背景情况：

1. **核心业务场景：** 
   
   物联网场景，配置边缘侧物联智能场景的联动、场景等控制，当前需求主要是楼宇区域内的，包括不仅限于医院楼宇、办公宇楼宇等应用场景的智能化控制，未来不排除设备告警、业务联动等需求；
   - 实际的业务使用方式是：
     
     某个楼层空间会有一个10寸屏幕，用户可以在屏幕上设定各种模式场景的控制（如日常模式、情景模式），每一种模式里面进入设置面板，里面有两个坐标：
     
     - 纵坐标是时间点（可增加/删除），如白班节点、查房节点、午休节点、夜班节点，都是由业务来定义的
     
     - 横坐标是末节点区域，如护士站、探视区、电梯厅、医生办公室，根据楼宇功能区业务来定义的，办公楼可能就是办公区、会议室等这样的
     
     由横纵坐标交接点的一个地方，决定了一个规则生效时间段和区域，时间段可以选择日历时间（比如工作日、夏季等，也可以自己编辑），时间段可以选择几点至几点，主要确定某年月日的某时间段生效，可以多个（或关系）；
     
     然后在这个横纵坐标交接点，配置规则，目前的需求配置是，按设备类进行建规则配置，如：
     
     - 照明设备：
       
       - 灯A, 匹配条件(全部/任意)，定时开/关，感应开关(无人15分钟关),智能开关（照度>xxx关，照度<xxxx开）
       
       - 灯B ...
     
     - 空调设备
       
       - 空调A，匹配条件(全部/任意)，定时开/关，感应开关(无人15分钟关),,智能开关（温度度>xxx开，温度<xxxx关），空调设置（温度xxx，风速xxx，模式xx等）
       
       - 盘管B ....
     
     - 设备N...
     
     这些设置保存后即生效。
   
   - 设想的系统架构分成三层
     
     屏端(N个屏)—>业务服务端—>执行引擎(N个引擎)
     
     当前项目：实施部署是业务服务端只有一个（单机或集群）、屏端会有几十个，预计可能50~100个管理不同区域，执行引擎在边缘侧一体箱服务器里面，当前按区域分有50个一体箱（也就是50个区域规则引擎）。
     
     我是想在屏端和业务服务端查看和配置区域业务规则，屏端配置完后，由业务服务产保存规则，并且拆解成简单的规则，同步到对应的区域规则引擎，由规则引擎执行细颗度的原子化规则。
   
   - 关于规则的组成和执行扩展
     
     - 目前按已有需求10寸屏里面配置的规则基本上是一个设备一条规则（条件满足后，是对单个设备执行），未来不排除可能对多个设备执行（由业务服务端拆解成具体设备？比如空间范围内的设备或者某一类设备）。
     
     - 单条规则除了事件条件、设备属性条件，横丛坐标有一个统一的生效时间，是否拆解加到每一条前面作为时间条件？这个时间条件会比较多，比如工作日，一年有250来天，每天可有多个时间段，要怎么实现会比较简单的？
     
     - 未来条件可能会扩展，满足条件后，执行的动作不仅限于设备指令，包括发消息/调用接口，可以预留实现。
   
   
2. **规则的复杂度：**
   - 规则的逻辑有多复杂？
     - 有简单的 "IF...THEN..."，也有多个 AND/OR/NOT，也可能基于模式匹配、持续时间，见背业务背景举例说明
   - 规则之间是否存在依赖关系
     - 目前需求上好像没有，规则之间只有AND/OR/NOT关系
   - 是否需要决策表、决策树这样的高级表达方式？
     - 不太理解这个定义，如果需要提供信息，可以换个容易理解的方式问题我
3. **规则的数量和变化频率：**
   - 大概有多少条规则？未来会增长到多少？
     - 当前项目总设备数预计5000~10000万个设备点位，未来不排除有10万个点位级别，总规则估计有5000条以上（如果按时间节点配置不同规则，即前面提的纵坐标不同时间点配置不同规则话，估计要乘以4~6倍，即2万多条规则？这个看规则引擎怎么定义最细粒度规则）；
     - 当前项目区域屏管理设备估计就几十个，比如灯几十个，空调几十个，幕布十多个等，所以区域引擎执行的规则可能数量级为1000以内，针对不同项目，应该不会超过5000条规则。
   - 规则是相对固定，还是需要经常修改和更新？
     - 基本上相对固定的，一般是项目实施阶段会配置，运行阶段很少改动，但肯定有改动；
4. **数据量和处理速度要求：**
   - 规则引擎需要处理的数据量有多大？
     - 大部分是物联网传感器数据和时间条件，区域规则引擎只监听区域内相关设备点位数据；
   - 对规则执行的响应时间（延迟）有什么要求？
     - 少部分需要及时响应（比如3秒以内），部分可以稍有延时，比如10多秒内；
5. **规则管理和维护：**
   - 谁来创建和维护这些规则？
     - 规则大部分由实施人员来配置，少量可能业务人员终端用户在10寸屏上修改配置；
   - 是否需要一个用户友好的界面来管理规则？
     - 前面需求背景已经说了，10寸屏配置，业务系统来转译规则，下发至规则引擎；
   - 规则的测试、版本控制、审计等功能是否重要？
     - 测试需要，版本和审计目前不需要；
   - 规则是否需要动态更新（不重启服务）？
     - 支持动态更新
6. **现有技术栈和集成需求：**
   - 您主要的开发语言是什么？（例如 Java, Python, Node.js 等）
     - JAVA为主，但如果涉及动态语言，不是很复杂也可以考虑，但非必须情况不建议
   - 规则引擎需要和哪些现有系统或服务集成（例如数据库、消息队列、API接口等）？
     - 核心业务场景“设想的系统架构分成三层”章节讲地比较清楚了，需要补充下，希望规则引擎本地也能存储推送下来的规则，这样规则引擎重启不需要去拉取规则，可以离线运行。希望提供API给业务服务端推送规则
     - 某些情况下，可能需要查看目前运行的规则，希望有查询接口
     - 为了运维方便，可能要了解运行日志，方便排查问题
   - 技术参数要求
     
       区域规则引擎设计部署为边缘侧，服务器初步配置为16内存，8核16线程CPU（某些情况下可能会8G内存，4核8线程）,里面还要部署边缘侧设备管理平台、消息服务等，所以资源不是特别充足，也不是特别差，希望相对比较轻量。
     
       另外，有些项目如果没有边缘服务器，只有一台服务器，规则引擎也可能和业务服务器部署一起。
     
     
7. **团队的技术能力和资源：**
   - 团队对规则引擎技术的熟悉程度如何？
     - 基本不太了解，目前没有开发成员了解和研究过规则引擎
   - 项目的时间和预算大致是怎样的？
     - 时间比较紧张，希望短期内能实现基本的功能，满足10寸屏的项目需求
8. **未来的扩展性考虑：**
   - 目前的需求是初步的，还是已经比较成熟？未来规则的复杂度和数量是否会有较大增长？
     - 项目的需求比较明确了，未来复杂的规则可能会增加些，但无非是在满足物联网场景的某些条件下，进行设备联动、控制、以及业务通知（如告警）
