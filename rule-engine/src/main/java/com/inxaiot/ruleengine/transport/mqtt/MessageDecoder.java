package com.inxaiot.ruleengine.transport.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.inxaiot.ruleengine.transport.mqtt.protocol.Bacnet;
import com.inxaiot.ruleengine.transport.mqtt.protocol.Knx;
import com.inxaiot.ruleengine.transport.mqtt.protocol.Modbus;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class MessageDecoder {
    public static final String PROTOCOL_BACNET = "bacnet";
    public static final String PROTOCOL_MODBUS = "modbus";
    public static final String PROTOCOL_KNX = "knx";

    public static Map<String, Object> decodeResponse(String topic, String payload) {
        String protocol = StringUtils.substringAfterLast(topic, "/");
        JSONObject jsonObject = JSON.parseObject(payload);
        jsonObject.put("protocol", protocol);
        return jsonObject;
    }

    public static Map<String, Object> decodeStatus(String topic, String payload) {
        String protocol = StringUtils.substringAfterLast(topic, "/");
        JSONObject jsonObject = JSON.parseObject(payload);

        switch (protocol) {
            case PROTOCOL_BACNET:
                return Bacnet.decodeStatus(jsonObject);
            case PROTOCOL_MODBUS:
                return Modbus.decodeStatus(jsonObject);
            case PROTOCOL_KNX:
                return Knx.decodeStatus(jsonObject);
            default:
                throw new IllegalArgumentException("Unsupported protocol: " + protocol);
        }
    }
}
