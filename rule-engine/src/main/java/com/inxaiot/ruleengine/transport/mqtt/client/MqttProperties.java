package com.inxaiot.ruleengine.transport.mqtt.client;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class MqttProperties {

	@Value("${mqtt.server-uris}")
	private String[] serverURIs;

	@Value("${mqtt.username}")
	private String username;

	@Value("${mqtt.password}")
	private char[] password;

	@Value("${mqtt.keep-alive-interval}")
	private int keepAliveInterval;

	@Value("${mqtt.completion-timeout}")
	private int completionTimeout;

	@Value("${mqtt.sub-topics}")
	private String[] subTopics;

	@Value("${mqtt.client-id-prefix}")
	private String clientIdPrefix;

	@Value("${mqtt.automatic-reconnect}")
	private Boolean automaticReconnect;

	@Value("${mqtt.clean-session}")
	private Boolean cleanSession;

	// 主题前缀常量
	public static final String STATUS_TOPIC_PREFIX = "inx/gw/status/";
	public static final String RESPONSE_TOPIC_PREFIX = "inx/gw/response/";

	/**
	 * 检查是否为状态主题
	 * @param topic 主题
	 * @return 是否为状态主题
	 */
	public static boolean isStatusTopic(String topic) {
		return topic != null && topic.startsWith(STATUS_TOPIC_PREFIX);
	}

	/**
	 * 检查是否为响应主题
	 * @param topic 主题
	 * @return 是否为响应主题
	 */
	public static boolean isResponseTopic(String topic) {
		return topic != null && topic.startsWith(RESPONSE_TOPIC_PREFIX);
	}

}
