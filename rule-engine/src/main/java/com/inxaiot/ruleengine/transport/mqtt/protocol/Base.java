package com.inxaiot.ruleengine.transport.mqtt.protocol;

import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class Base {
    private static final Logger logger = LoggerFactory.getLogger(Base.class);

    /**
     * 解析设备属性点位数据, Bacnet协议和 Knx协议解析用到的共用方法
     * @param jsonObject
     * @return
     */
    protected static Map<String, Object> decodeDeviceAttr(JSONObject jsonObject){
        //找到控制器点位数据
        Map<String, Object> controllersCollect = jsonObject.entrySet().stream().filter(e->!e.getKey().equals("clientId"))
                .filter(e->!e.getKey().equals("time"))
                .filter(e->!e.getKey().equals("system")).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (CollectionUtils.isEmpty(controllersCollect)) {
            logger.warn("No controllers found in status message:{}.",jsonObject.toJSONString());
            return new HashMap<>();
        }

        // 找到system对象
        JSONObject systemObj = (JSONObject) jsonObject.get("system");

        JSONObject deviceAllObj = new JSONObject(); //设备和属性点值结合
        controllersCollect.entrySet().stream().forEach(controllerOne->{
            String controllerName = controllerOne.getKey();
            Map<String, JSONObject> deviceWithControllerObj = new HashMap<>(); //设备和属性点值结合
            // 找到状态数据
            String statusKey = controllerName + "_" + "status";
            Object statusVal = null;
            if (systemObj != null) {
                statusVal = systemObj.get(statusKey);
            }

            JSONObject deviceAttrPointObj = (JSONObject) controllerOne.getValue();

            // 遍历所有设备点位
            deviceAttrPointObj.entrySet().stream().forEach(point -> {

                String pointTag = point.getKey();
                Object value = point.getValue();

                String deviceName = StringUtils.substringBeforeLast(pointTag,"_");
                String attrTag = StringUtils.substringAfterLast(pointTag,"_");

                JSONObject deviceObj = deviceWithControllerObj.get(deviceName);
                if (deviceObj == null) {
                    deviceObj = new JSONObject();
                    deviceWithControllerObj.put(deviceName, deviceObj);
                    JSONObject attrTagValObj = new JSONObject(); //属性点和值
                    attrTagValObj.put(attrTag, value);
                    deviceObj.put(deviceName, attrTagValObj);
                } else {
                    JSONObject attrTagValObj = (JSONObject) deviceObj.get(deviceName);
                    attrTagValObj.put(attrTag, value);
                }

            });

            // 遍历控制器下的设备，放置状态
            Object finalStatusVal = statusVal;
            deviceWithControllerObj.entrySet().stream().forEach(deviceOne -> {

                String deviceName = deviceOne.getKey();
                JSONObject deviceObj = deviceOne.getValue();
                JSONObject deviceAttrObj = (JSONObject) deviceObj.get(deviceName);

                deviceAllObj.put(deviceName, deviceAttrObj);
                // 检查是否存在状态值
                if (deviceAttrObj.containsKey("status")) return;
                // 把状态放进去
                if (finalStatusVal != null) {
                    deviceAttrObj.put("status", finalStatusVal);
                }

            });

        });

        Map<String, Object> devicesCollect = deviceAllObj.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        logger.info("mqtt status decode result: {}", devicesCollect);

        return devicesCollect;
    }

    /**
     *
     * 设备状态处理, Moudbus协议和Knx协议解析用到的共用方法
     *
     */
    protected static Map<String, Object> decodeStatusAttr(JSONObject jsonObject) {
        // 找到system对象
        JSONObject devicesStatusCollect = (JSONObject) jsonObject.get("system");

        if (CollectionUtils.isEmpty(devicesStatusCollect)) {
            logger.warn("No devices found in status message:{}.",jsonObject.toJSONString());
            return null;
        }

        JSONObject deviceAllObj = new JSONObject(); //设备和属性点值结合
        devicesStatusCollect.entrySet().stream().forEach(deviceOne->{
            String deviceName = StringUtils.substringBeforeLast(deviceOne.getKey(),"_");
            JSONObject attrTagValObj = new JSONObject(); //属性点和值
            attrTagValObj.put("status", deviceOne.getValue());
            deviceAllObj.put(deviceName, attrTagValObj);
        });

        Map<String, Object> devicesCollect = deviceAllObj.entrySet().stream().collect(Collectors.toMap(e->e.getKey(), e-> e.getValue()));
        logger.info("decodeStatusAttr result: {}", devicesCollect);
        return devicesCollect;
    }

    protected static Map<String, Object> calculateExpress(Map<String, Object> attrs) {
        attrs.entrySet().forEach(attr -> {
            String deviceName = attr.getKey();
            JSONObject deviceAttrTag = (JSONObject) attr.getValue();

            deviceAttrTag.entrySet().forEach(attrTag -> {
                String attrTagKey = attrTag.getKey();
                String attrTagValue = attrTag.getValue().toString();

                // 如果包含"*"，则计算表达式并替换原值
                if (attrTagValue.contains("*")) {
                    String calculatedValue = executeResultExpress(attrTagValue, true);
                    if (calculatedValue != null) {
                        // 将计算结果设置回原属性
                        deviceAttrTag.put(attrTagKey, calculatedValue);
                        //logger.debug("Calculated expression for device: {}, attr: {}, original: {}, result: {}", deviceName, attrTagKey, attrTagValue, calculatedValue);
                    } else {
                        logger.warn("Failed to calculate expression for device: {}, attr: {}, expression: {}", deviceName, attrTagKey, attrTagValue);
                    }
                }
                // 如果不含"*"，保持原值不变（无需额外操作）
            });
        });
        return attrs;
    }

    private static String executeResultExpress (String express, boolean aIsPrecise) {
        if (StringUtils.isEmpty(express)) {
            return null;
        }
        ExpressRunner runner = new ExpressRunner(aIsPrecise, false);
        DefaultContext<String, Object> context = new DefaultContext<String, Object>();
        try {
            Object r = runner.execute(express, context, null, true, false);
            if (r == null) {
                return null;
            }
            return r.toString();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("ql execute result express, exception {}", e.getMessage(), e);
        }
        return null;
    }


}
