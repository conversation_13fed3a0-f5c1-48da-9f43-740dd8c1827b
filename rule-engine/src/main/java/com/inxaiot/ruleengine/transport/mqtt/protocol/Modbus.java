package com.inxaiot.ruleengine.transport.mqtt.protocol;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class Modbus extends Base {
    public static void main(String[] args) {
        /**
         {
             " V_1F_DB_14": {
             "APwh": "198357*150* 0.001",
             "CT": "150",
             "Ia": "351*150* 0.0002",
             "Ib": "269*150* 0.0002",
             "Ic": "211*150* 0.0002",
             "Pfav": "7700* 0.0001",
             "Psum": "147*150* 0.2* 0.001",
             "Ua": "22835* 0.01",
             "Ub": "22959* 0.01",
             "Uc": "22949* 0.01"
             },
             "clientId": "inxvisionW7MGFZRcB4-modbus",
             "time": "1692320863544"
         }
         *
         */
        String json = "{\n" +
                "  \" V_1F_DB_14\": {\n" +
                "    \"APwh\": \"198357*150* 0.001\",\n" +
                "    \"CT\": \"150\",\n" +
                "    \"Ia\": \"351*150* 0.0002\",\n" +
                "    \"Ib\": \"269*150* 0.0002\",\n" +
                "    \"Ic\": \"211*150* 0.0002\",\n" +
                "    \"Pfav\": \"7700* 0.0001\",\n" +
                "    \"Psum\": \"147*150* 0.2* 0.001\",\n" +
                "    \"Ua\": \"22835* 0.01\",\n" +
                "    \"Ub\": \"22959* 0.01\",\n" +
                "    \"Uc\": \"22949* 0.01\"\n" +
                "  },\n" +
                "  \"clientId\": \"inxvisionW7MGFZRcB4-modbus\",\n" +
                "  \"time\": \"1692320863544\"\n" +
                "}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        System.out.println(decodeStatus(jsonObject));
        //{ V_1F_DB_14={"CT":"150","Ia":"351*150* 0.0002","Ib":"269*150* 0.0002","Ic":"211*150* 0.0002","Ua":"22835* 0.01","Psum":"147*150* 0.2* 0.001","Ub":"22959* 0.01","APwh":"198357*150* 0.001","Pfav":"7700* 0.0001","Uc":"22949* 0.01"}}

        /**
         * 设备只上报状态， 只处理设备状态。
         * {
         *     "clientId":"inxvisionBcc4A5E52B-modbus-pz",
         *     "system":{
         *         "L3_9F_db_10_status":"0",
         *         "L3_9F_db_11_status":"0",
         *         "L3_9F_db_1_status":"0",
         *         "L3_9F_db_2_status":"0",
         *         "L3_9F_db_3_status":"0",
         *         "L3_9F_db_4_status":"0",
         *         "L3_9F_db_5_status":"0",
         *         "L3_9F_db_6_status":"0",
         *         "L3_9F_db_7_status":"0",
         *         "L3_9F_db_8_status":"0",
         *         "L3_9F_db_9_status":"0"
         *     },
         *     "time":"1699006951992"
         * }
         *
         */
        String json2 = "{\n" +
                "  \"clientId\": \"inxvisionBcc4A5E52B-modbus-pz\",\n" +
                "  \"system\": {\n" +
                "    \"L3_9F_db_10_status\": \"0\",\n" +
                "    \"L3_9F_db_11_status\": \"0\",\n" +
                "    \"L3_9F_db_1_status\": \"0\",\n" +
                "    \"L3_9F_db_2_status\": \"0\",\n" +
                "    \"L3_9F_db_3_status\": \"0\",\n" +
                "    \"L3_9F_db_4_status\": \"0\",\n" +
                "    \"L3_9F_db_5_status\": \"0\",\n" +
                "    \"L3_9F_db_6_status\": \"0\",\n" +
                "    \"L3_9F_db_7_status\": \"0\",\n" +
                "    \"L3_9F_db_8_status\": \"0\",\n" +
                "    \"L3_9F_db_9_status\": \"0\"\n" +
                "  },\n" +
                "  \"time\": \"1699006951992\"\n" +
                "}";
        JSONObject jsonObject2 = JSONObject.parseObject(json2);
        System.out.println(decodeStatus(jsonObject2));
    }
    private static final Logger logger = LoggerFactory.getLogger(Modbus.class);

    /**
     {
     " V_1F_DB_14": {
        "APwh": "198357*150* 0.001",
        "CT": "150",
        "Ia": "351*150* 0.0002",
        "Ib": "269*150* 0.0002",
        "Ic": "211*150* 0.0002",
        "Pfav": "7700* 0.0001",
        "Psum": "147*150* 0.2* 0.001",
        "Ua": "22835* 0.01",
        "Ub": "22959* 0.01",
        "Uc": "22949* 0.01"
     },
     "clientId": "inxvisionW7MGFZRcB4-modbus",
     "time": "1692320863544"
     }
     * @param jsonObject
     * @return
     */
    public static Map<String, Object> decodeStatus(JSONObject jsonObject){
        logger.info("start modbus status decode...");
        // 找到设备点位数据
        Map<String, Object> devicesCollect = jsonObject.entrySet().stream().filter(e->!e.getKey().equals("clientId"))
                .filter(e->!e.getKey().equals("time"))
                .filter(e->!e.getKey().equals("system")).collect(Collectors.toMap(e->e.getKey(), e-> e.getValue()));

        if (CollectionUtils.isEmpty(devicesCollect)) {
            logger.warn("Modbus status topic handle msg, device attr is null:{}.",jsonObject.toJSONString());

            /**
             * 设备只上报状态， 只处理设备状态。
             * {
             *     "clientId":"inxvisionBcc4A5E52B-modbus-pz",
             *     "system":{
             *         "L3_9F_db_10_status":"0",
             *         "L3_9F_db_11_status":"0",
             *         "L3_9F_db_1_status":"0",
             *         "L3_9F_db_2_status":"0",
             *         "L3_9F_db_3_status":"0",
             *         "L3_9F_db_4_status":"0",
             *         "L3_9F_db_5_status":"0",
             *         "L3_9F_db_6_status":"0",
             *         "L3_9F_db_7_status":"0",
             *         "L3_9F_db_8_status":"0",
             *         "L3_9F_db_9_status":"0"
             *     },
             *     "time":"1699006951992"
             * }
             *
             */
            return decodeStatusAttr(jsonObject);
        }
        logger.info("modbus status msg decode,devicesCollect is: {}", devicesCollect);

        //找到状态数据
        Map.Entry<String, Object> statusCollect = jsonObject.entrySet().stream().filter(e->e.getKey().equals("system")).findFirst().orElse(null);
        if (statusCollect != null) {
            logger.info("modbus status msg decode,statusCollect is: {}", statusCollect);
            // 遍历所有的属性
            JSONObject vObj = (JSONObject) statusCollect.getValue();
            Map<String, Object> deviceStatusMap = new HashMap<>();
            vObj.entrySet().stream().forEach(item -> {

                String deviceName = StringUtils.substringBeforeLast(item.getKey(),"_");
                JSONObject deviceAttrObj = (JSONObject) devicesCollect.get(deviceName);
                if (deviceAttrObj != null) {
                    // 找到设备， 把状态放进去
                    deviceAttrObj.put("status", item.getValue());
                } else {
                    // 未找到设备，新建一个设备
                    JSONObject attrObj = new JSONObject();
                    attrObj.put("status", item.getValue());
                    deviceStatusMap.put(deviceName, attrObj);
                }
            });

            if (!CollectionUtils.isEmpty(deviceStatusMap)) {
                devicesCollect.putAll(deviceStatusMap);
            }
        }
        logger.info("modbus status msg decode,after decodeStatus is: {}", devicesCollect);
        return calculateExpress(devicesCollect);
    }
}
