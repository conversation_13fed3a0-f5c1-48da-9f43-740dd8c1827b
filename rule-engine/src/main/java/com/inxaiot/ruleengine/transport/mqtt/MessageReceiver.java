package com.inxaiot.ruleengine.transport.mqtt;

import com.alibaba.fastjson.JSONObject;
import com.inxaiot.ruleengine.core.action.ActionExecutionCoordinator;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.transport.mqtt.client.MqttProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@AllArgsConstructor
@Component
public class MessageReceiver implements MessageHandler {

    @Autowired
    private StateManager stateManager;

    @Autowired
    private ActionExecutionCoordinator actionCoordinator;


    @Override
    public void handleMessage(Message<?> message) throws MessagingException {

        MessageHeaders headers = message.getHeaders();
        //获取消息Topic
        String topic = (String) headers.get(MqttHeaders.RECEIVED_TOPIC);
        //获取消息体
        String payload = (String) message.getPayload();

        if (payload.contains("null")) {
            payload = payload.replace("null", "0");
        }

        if (MqttProperties.isStatusTopic(topic)) {
            Map<String, Object> decodedData = MessageDecoder.decodeStatus(topic, payload);
            handleStatus(decodedData);
            return;
        }
        if(MqttProperties.isResponseTopic(topic)){
            Map<String, Object> decodedData =  MessageDecoder.decodeResponse(topic,payload);
            handleResponse(decodedData);
        }


    }
    /**
     *  下发指令时，网关的响应处理
     * @param deviceAttrMap
     */
    private void handleResponse(Map<String, Object> deviceAttrMap) {
        try {
            // 提取序列号和执行结果
            String seq = (String) deviceAttrMap.get("seq");
            boolean result =  getBooleanValue(deviceAttrMap.get("status")); // success/failed
            String message = (String) deviceAttrMap.get("message");

            if (seq != null) {
                // 根据序列号查找执行记录并更新状态
                actionCoordinator.handleExecutionResponse(seq,result, message);
                log.info("Received action execution response: seq={}, result={}", seq, result);
            } else {
                log.debug("No seq found in response message: {}", deviceAttrMap);
            }

        } catch (Exception e) {
            log.error("Error handling response message: {}", e.getMessage(), e);
        }
    }

    private boolean getBooleanValue(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else {
            return Boolean.parseBoolean(value.toString());
        }
    }

    /**
     * bacnet: {1_1_AHU_5={"SWD":-21.565639,"SWD2":-21.565639,"status":1}, 1_1_AHU_6={"SWD":-21.565639,"SWD2":-21.565639,"status":1}}
     * 1.触发规则评估
     * 2.更新action执行状态
     * @param deviceAttrMap
     */
    private void handleStatus(Map<String, Object> deviceAttrMap) {
        try {
            //1. 触发规则评估
            deviceAttrMap.forEach((k,v)->{
                JSONObject deviceAttrTag = (JSONObject) v;
                deviceAttrTag.forEach((tag, val)->{
                    stateManager.processDevicePointUpdate(k, tag, val);
                });
            });

            // 2. 检查是否有动作执行需要根据状态更新（后续扩展用）
            actionCoordinator.handleDeviceStatusUpdate(deviceAttrMap);

        } catch (Exception e) {
            log.error("Error handling status message: {}", e.getMessage(), e);
        }
    }
}
