package com.inxaiot.ruleengine.transport.mqtt.protocol;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

public class Knx extends Base {

    private static final Logger logger = LoggerFactory.getLogger(Knx.class);

    public static void main(String[] args) {
        /**
         *
         * 	{
         *     "clientId":"inxvisioncaEF48CAce-knx",
         *      "L5_1F_12L_1":{ // 设备模块
         *         "L5_1F_lck_10_qt":0,
         *         "L5_1F_lck_10_zt":0
         *      }，
         *      "L5_6F_12L_1":{
         *         "L5_6F_lck_1_qt":0,
         *         "L5_6F_lck_1_zt":0
         *      },
         *     "system":{
         *         "L5_1F_12L_1_status":0, // 设备模块状态，同时也是一个设备
         *         "L5_1F_8L_1_status":0,
         *         "L5_1F_8L_2_status":0,
         *         "L5_2F_12L_1_status":0,
         *         "L5_2F_12L_2_status":0,
         *         "L5_3F_8L_1_status":0,
         *         "L5_4F_12L_1_status":0,
         *         "L5_4F_8L_1_status":0,
         *         "L5_5F_8L_1_status":0,
         *         "L5_5F_8L_2_status":0,
         *         "L5_6F_12L_1_status":0
         *     },
         *     "time":"1699007171041"
         * }
         */
        String json = "{\n" +
                "  \"L5_1F_12L_1\": {\n" +
                "    \"L5_1F_lck_10_qt\": 0,\n" +
                "    \"L5_1F_lck_10_zt\": 0\n" +
                "  },\n" +
                "  \"L5_6F_12L_1\": {\n" +
                "    \"L5_6F_lck_1_qt\": 0,\n" +
                "    \"L5_6F_lck_1_zt\": 0\n" +
                "  },\n" +
                "  \"clientId\": \"inxvisioncaEF48CAce-knx\",\n" +
                "  \"system\": {\n" +
                "    \"L5_1F_12L_1_status\": 0,\n" +
                "    \"L5_1F_8L_1_status\": 0,\n" +
                "    \"L5_1F_8L_2_status\": 0,\n" +
                "    \"L5_2F_12L_1_status\": 0,\n" +
                "    \"L5_2F_12L_2_status\": 0,\n" +
                "    \"L5_6F_12L_1_status\": 0\n" +
                "  },\n" +
                "  \"time\": \"1699007171041\"\n" +
                "}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        System.out.println(decodeStatus(jsonObject));
        //{L5_6F_lck_1={"qt":0,"zt":0,"status":0}, L5_1F_8L_1={"status":0}, L5_1F_8L_2={"status":0}, L5_1F_12L_1={"status":0}, L5_2F_12L_1={"status":0}, L5_1F_lck_10={"qt":0,"zt":0,"status":0}, L5_2F_12L_2={"status":0}, L5_6F_12L_1={"status":0}}
    }

    /**
     *
     * 	{
     *     "clientId":"inxvisioncaEF48CAce-knx",
     *      "L5_1F_12L_1":{ // 设备模块
     *         "L5_1F_lck_10_qt":0,
     *         "L5_1F_lck_10_zt":0
     *      }，
     *      "L5_6F_12L_1":{
     *         "L5_6F_lck_1_qt":0,
     *         "L5_6F_lck_1_zt":0
     *      },
     *     "system":{
     *         "L5_1F_12L_1_status":0, // 设备模块状态，同时也是一个设备
     *         "L5_1F_8L_1_status":0,
     *         "L5_1F_8L_2_status":0,
     *         "L5_2F_12L_1_status":0,
     *         "L5_2F_12L_2_status":0,
     *         "L5_3F_8L_1_status":0,
     *         "L5_4F_12L_1_status":0,
     *         "L5_4F_8L_1_status":0,
     *         "L5_5F_8L_1_status":0,
     *         "L5_5F_8L_2_status":0,
     *         "L5_6F_12L_1_status":0
     *     },
     *     "time":"1699007171041"
     * }
     */
    public static Map<String, Object> decodeStatus(JSONObject jsonObject){
        logger.info("start knx status decode...");
        // 解析设备属性，及"system"中的 设备模块状态。处理设备属性数据和状态
        Map<String, Object> deviceAttrMap = decodeDeviceAttr(jsonObject);

        // 因为设备模块也是一个设备，所以要解析这些设备的状态。
        Map<String, Object> deviceStatusMap = decodeStatusAttr(jsonObject);

        Map<String, Object> res = new HashMap<>();

        if (!CollectionUtils.isEmpty(deviceAttrMap)) {
            res.putAll(deviceAttrMap);
        }

        if (!CollectionUtils.isEmpty(deviceStatusMap)) {
            res.putAll(deviceStatusMap);
        }
        return calculateExpress(res);
    }
}
