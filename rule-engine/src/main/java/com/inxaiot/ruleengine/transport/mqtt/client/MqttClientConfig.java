package com.inxaiot.ruleengine.transport.mqtt.client;

import com.inxaiot.ruleengine.transport.mqtt.MessageReceiver;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import javax.annotation.Resource;

@Configuration
@EnableIntegration
@IntegrationComponentScan
@Slf4j
public class MqttClientConfig {
	private static final Logger logger = LoggerFactory.getLogger(MqttClientConfig.class);
	@Autowired
	private MqttProperties mqttProperties;

	@Autowired
	private MessageReceiver messageReceiver;

	/**
	 * MQTT连接配置
	 * @return 连接工厂
	 */
	@Bean
	public MqttPahoClientFactory mqttClientFactory() {
		DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
		MqttConnectOptions options = new MqttConnectOptions();
		options.setServerURIs(mqttProperties.getServerURIs());
		options.setUserName(mqttProperties.getUsername());
		options.setPassword(mqttProperties.getPassword());
		options.setKeepAliveInterval(mqttProperties.getKeepAliveInterval());
		options.setAutomaticReconnect(mqttProperties.getAutomaticReconnect());
		options.setConnectionTimeout(mqttProperties.getCompletionTimeout());
		options.setCleanSession(mqttProperties.getCleanSession());
		factory.setConnectionOptions(options);
		logger.info("Mqtt client init, serverURIs is {}, username is {}, password is {}", mqttProperties.getServerURIs(), mqttProperties.getUsername(), mqttProperties.getPassword());
		return factory;
	}


	/**
	 * 配置client,监听的topic
	 * @return
	 */
	@Bean
	public MessageProducer inbound() {
		MqttPahoMessageDrivenChannelAdapter adapter =
				new MqttPahoMessageDrivenChannelAdapter(
						mqttProperties.getClientIdPrefix() + "_inbound", mqttClientFactory(), mqttProperties.getSubTopics());
		adapter.setConverter(new DefaultPahoMessageConverter());
		adapter.setQos(1);
		adapter.setRecoveryInterval(5000);
		adapter.setOutputChannel(mqttInboundChannel());
		logger.info("Mqtt client init, inbound...., subTopics is {}", mqttProperties.getSubTopics());
		return adapter;
	}

	/**
	 * 配置Outbound入站, 发送消息通道
	 * */
	@Bean
	public MessageChannel mqttOutboundChannel() {
		return new DirectChannel();
	}

	/**
	 * 配置Inbound入站, 订阅消息通道
	 * */
	@Bean
	public MessageChannel mqttInboundChannel() {
		return new DirectChannel();
	}

	//通过通道获取数据
	@Bean
	@ServiceActivator(inputChannel = "mqttInboundChannel")
	public MessageHandler handler() {
		return this.messageReceiver;
	}

	@Bean
	@ServiceActivator(inputChannel = "mqttOutboundChannel")
	public MessageHandler mqttOutbound() {
		MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(mqttProperties.getClientIdPrefix() + "_outbound", mqttClientFactory());
		messageHandler.setCompletionTimeout(5000);
		messageHandler.setAsync(true);
		return messageHandler;
	}

}
