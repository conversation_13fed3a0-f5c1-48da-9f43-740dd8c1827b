package com.inxaiot.ruleengine.transport.mqtt.protocol;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;


public class Bacnet extends Base{
    private static final Logger logger = LoggerFactory.getLogger(Bacnet.class);

    public static void main(String[] args) {
        String json = "{\n" +
                "  \"RPC1005\": {\n" +
                "    \"1_1_AHU_5_SWD\": -21.565639,\n" +
                "    \"1_1_AHU_5_SWD2\": -21.565639\n" +
                "  },\n" +
                "  \"RPC1006\": {\n" +
                "    \"1_1_AHU_6_SWD\": -21.565639,\n" +
                "    \"1_1_AHU_6_SWD2\": -21.565639\n" +
                "  },\n" +
                "  \"clientId\": \"inxvisionW7MGFZRcB4-bacnet\",\n" +
                "  \"system\": {\n" +
                "    \"RPC1005_status\": 1,\n" +
                "    \"RPC1006_status\": 1\n" +
                "  },\n" +
                "  \"time\": \"1692256941228\"\n" +
                "}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        System.out.println(decodeStatus(jsonObject));//
        //{1_1_AHU_5={"SWD":-21.565639,"SWD2":-21.565639,"status":1}, 1_1_AHU_6={"SWD":-21.565639,"SWD2":-21.565639,"status":1}}
    }

    public static Map<String, Object> decodeStatus(JSONObject jsonObject){

        // 平台端逻辑如下：
            /**
             * inx/gw/stauts/网关ID/bacnet
             * {
             *     "RPC1005": {
             *         "1_1_AHU_5_SWD": -21.565639,
             *         "1_1_AHU_5_SWD2": -21.565639
             *     },
             *     "RPC1006": {
             *         "1_1_AHU_6_SWD": -21.565639,
             *         "1_1_AHU_6_SWD2": -21.565639
             *     },
             *     "clientId": "inxvisionW7MGFZRcB4-bacnet",
             *     "system": {
             *         "RPC1005_status": 1
             *         "RPC3002_status": 1
             *     },
             *     "time": "1692256941228"
             * }
             * 处理上报了状态，但是没有属性点上报。例如RPC3002_status
             *
             * 设备只上报状态， 只处理设备状态。
             * 			 *   {"clientId":"inxvision3Ea7bE53F8-bacnet-xf","system":{"RPC_1001_status":1},"time":"1705977396089"}
             * 			 *
             * 			 *   找到控制器RPC_1001下所有的设备
             * 			 *
             * // 找到所有system中子串"_status"前面的子字符串，例如RPC3002. 以这个子字符串为key,过滤出其值为空的子字符串key
             */

        // TODO，如果是开关类操作，这里规则引擎，需要平台端下发设置状态条件为控制器的状态，而不是点位的状态
        logger.info("start bacnet status decode...");
        return calculateExpress(decodeDeviceAttr(jsonObject));
    }

}
