package com.inxaiot.ruleengine.transport.mqtt;

import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

@Component
@MessagingGateway(defaultRequestChannel = "mqttOutboundChannel")
public interface MessageSender {
	/**
	 * 使用 自定义 Topic & Default Qos 发送数据
	 *
	 * @param topic 自定义 Topic
	 * @param data  string
	 */
	@Gateway(replyTimeout = 2, requestTimeout = 200)
	void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, String data);
}
