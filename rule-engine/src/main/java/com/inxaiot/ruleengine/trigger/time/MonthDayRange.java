package com.inxaiot.ruleengine.trigger.time;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 月日时间段模型
 * 用于表示不涉及年份的时间段，如季节、节假日等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-07-07
 */
public class MonthDayRange {
    
    /**
     * 开始月日，格式："MM-dd"
     */
    @JsonProperty("startDate")
    private String startDate;
    
    /**
     * 结束月日，格式："MM-dd"
     */
    @JsonProperty("endDate")
    private String endDate;
    
    /**
     * 时间段描述
     */
    @JsonProperty("description")
    private String description;
    
    // 构造函数
    public MonthDayRange() {
    }
    
    public MonthDayRange(String startDate, String endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    public MonthDayRange(String startDate, String endDate, String description) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.description = description;
    }
    
    // Getters and Setters
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    public String getEndDate() {
        return endDate;
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * 检查指定月日是否在时间段内
     *
     * @param monthDay 要检查的月日，格式："MM-dd"
     * @return 如果在范围内返回true，否则返回false
     */
    @JsonIgnore
    public boolean contains(String monthDay) {
        if (monthDay == null || startDate == null || endDate == null) {
            return false;
        }
        
        // 验证格式
        if (!isValidMonthDayFormat(monthDay) || !isValidMonthDayFormat(startDate) || !isValidMonthDayFormat(endDate)) {
            return false;
        }
        
        // 如果开始日期小于等于结束日期，说明不跨年
        if (startDate.compareTo(endDate) <= 0) {
            return monthDay.compareTo(startDate) >= 0 && monthDay.compareTo(endDate) <= 0;
        } else {
            // 跨年情况：如12-01到02-28
            return monthDay.compareTo(startDate) >= 0 || monthDay.compareTo(endDate) <= 0;
        }
    }
    
    /**
     * 检查时间段是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    @JsonIgnore
    public boolean isValid() {
        return isValidMonthDayFormat(startDate) && isValidMonthDayFormat(endDate);
    }
    
    /**
     * 验证月日格式是否正确
     * 格式应为 "MM-dd"，如 "02-14", "12-25"
     */
    @JsonIgnore
    private boolean isValidMonthDayFormat(String monthDay) {
        if (monthDay == null || monthDay.trim().isEmpty()) {
            return false;
        }

        String trimmed = monthDay.trim();
        if (!trimmed.matches("\\d{2}-\\d{2}")) {
            return false;
        }

        try {
            String[] parts = trimmed.split("-");
            int month = Integer.parseInt(parts[0]);
            int day = Integer.parseInt(parts[1]);

            // 检查月份范围
            if (month < 1 || month > 12) {
                return false;
            }

            // 检查日期范围（简单检查，不考虑闰年）
            if (day < 1 || day > 31) {
                return false;
            }

            // 检查特定月份的日期范围
            if ((month == 2 && day > 29) ||
                ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30)) {
                return false;
            }

            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 检查是否跨年
     *
     * @return 如果跨年返回true，否则返回false
     */
    @JsonIgnore
    public boolean isCrossYear() {
        if (!isValid()) {
            return false;
        }
        return startDate.compareTo(endDate) > 0;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        MonthDayRange that = (MonthDayRange) obj;
        
        if (startDate != null ? !startDate.equals(that.startDate) : that.startDate != null) return false;
        if (endDate != null ? !endDate.equals(that.endDate) : that.endDate != null) return false;
        return description != null ? description.equals(that.description) : that.description == null;
    }
    
    @Override
    public int hashCode() {
        int result = startDate != null ? startDate.hashCode() : 0;
        result = 31 * result + (endDate != null ? endDate.hashCode() : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        return result;
    }
    
    @Override
    public String toString() {
        return "MonthDayRange{" +
                "startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", description='" + description + '\'' +
                ", crossYear=" + isCrossYear() +
                '}';
    }
}
