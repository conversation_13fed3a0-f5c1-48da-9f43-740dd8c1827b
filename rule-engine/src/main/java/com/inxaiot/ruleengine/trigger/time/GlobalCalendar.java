package com.inxaiot.ruleengine.trigger.time;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 全局日历模型
 * 支持灵活的时间段定义和月日排除功能
 *
 * 主要功能：
 * 1. excludeMonthDays: 排除的月日列表，格式"MM-dd"，每年重复生效
 * 2. dateRanges: 命名时间段定义，支持跨年时间段
 *
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2025-07-07
 */
@Getter
public class GlobalCalendar {

    // Getters and Setters
    /**
     * 排除的月日列表，格式："MM-dd"
     */
    @JsonProperty("excludeMonthDays")
    private List<String> excludeMonthDays = new ArrayList<>();

    /**
     * 命名时间段定义
     * key: 时间段名称（如"SUMMER", "WINTER", "HOLIDAY"等）
     * value: 时间段列表，支持跨年
     */
    @JsonProperty("dateRanges")
    private Map<String, List<MonthDayRange>> monthDateRanges = new HashMap<>();

    // 构造函数
    public GlobalCalendar() {
    }

    public void setExcludeMonthDays(List<String> excludeMonthDays) {
        this.excludeMonthDays = excludeMonthDays != null ? excludeMonthDays : new ArrayList<>();
    }

    public void setMonthDateRanges(Map<String, List<MonthDayRange>> monthDateRanges) {
        this.monthDateRanges = monthDateRanges != null ? monthDateRanges : new HashMap<>();
    }

    /**
     * 检查指定日期是否为排除月日（原节假日功能）
     */
    @JsonIgnore
    public boolean isHoliday(LocalDate date) {
        if (date == null) {
            return false;
        }
        String monthDay = String.format("%02d-%02d", date.getMonthValue(), date.getDayOfMonth());
        return isExcludedMonthDay(monthDay);
    }

    /**
     * 检查指定月日是否在排除列表中
     */
    @JsonIgnore
    public boolean isExcludedMonthDay(String monthDay) {
        return excludeMonthDays.contains(monthDay);
    }

    /**
     * 检查指定日期是否在指定时间段中
     */
    @JsonIgnore
    public boolean isInDateRange(String rangeName, LocalDate date) {
        if (date == null || rangeName == null) {
            return false;
        }
        String monthDay = String.format("%02d-%02d", date.getMonthValue(), date.getDayOfMonth());
        return isInDateRange(rangeName, monthDay);
    }

    /**
     * 检查指定月日是否在指定时间段中
     */
    @JsonIgnore
    public boolean isInDateRange(String rangeName, String monthDay) {
        List<MonthDayRange> ranges = monthDateRanges.get(rangeName);
        if (ranges == null || ranges.isEmpty()) {
            return false;
        }

        for (MonthDayRange range : ranges) {
            if (range != null && range.contains(monthDay)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据季节名称检查日期（兼容原有API）
     */
    @JsonIgnore
    public boolean isInSeason(LocalDate date, String season) {
        if (season == null) {
            return true; // 如果没有指定季节，则认为满足条件
        }

        String seasonUpper = season.toUpperCase();
        if ("ALL".equals(seasonUpper)) {
            return true;
        }

        return isInDateRange(seasonUpper, date);
    }

    /**
     * 获取所有可用的时间段名称
     */
    @JsonIgnore
    public Set<String> getAvailableDateRangeNames() {
        return monthDateRanges.keySet();
    }

    /**
     * 添加排除月日
     */
    @JsonIgnore
    public void addExcludeMonthDay(String monthDay) {
        if (monthDay != null && !excludeMonthDays.contains(monthDay)) {
            excludeMonthDays.add(monthDay);
        }
    }

    /**
     * 移除排除月日
     */
    @JsonIgnore
    public void removeExcludeMonthDay(String monthDay) {
        excludeMonthDays.remove(monthDay);
    }

    /**
     * 添加时间段
     */
    @JsonIgnore
    public void addDateRange(String rangeName, MonthDayRange range) {
        if (rangeName != null && range != null) {
            monthDateRanges.computeIfAbsent(rangeName, k -> new ArrayList<>()).add(range);
        }
    }

    /**
     * 设置时间段列表
     */
    @JsonIgnore
    public void setDateRange(String rangeName, List<MonthDayRange> ranges) {
        if (rangeName != null) {
            if (ranges != null) {
                monthDateRanges.put(rangeName, new ArrayList<>(ranges));
            } else {
                monthDateRanges.remove(rangeName);
            }
        }
    }

    /**
     * 移除时间段
     */
    @JsonIgnore
    public void removeDateRange(String rangeName) {
        monthDateRanges.remove(rangeName);
    }



    /**
     * 获取配置的项目数量
     */
    @JsonIgnore
    public int getConfigCount() {
        int count = excludeMonthDays.size();
        for (List<MonthDayRange> ranges : monthDateRanges.values()) {
            if (ranges != null) {
                count += ranges.size();
            }
        }
        return count;
    }

    /**
     * 检查日历是否为空
     */
    @JsonIgnore
    public boolean isEmpty() {
        return excludeMonthDays.isEmpty() && monthDateRanges.isEmpty();
    }

    @Override
    public String toString() {
        return "GlobalCalendar{" +
                "excludeMonthDays=" + excludeMonthDays.size() +
                ", dateRanges=" + monthDateRanges.size() +
                ", configCount=" + getConfigCount() +
                '}';
    }
}
