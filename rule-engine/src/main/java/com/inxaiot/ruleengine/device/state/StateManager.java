package com.inxaiot.ruleengine.device.state;

import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import com.inxaiot.ruleengine.device.event.DeviceEventPublisher;
import com.inxaiot.ruleengine.RuleEngineConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.List;
import java.util.ArrayList;

/**
 * 通用设备状态管理器
 * 管理设备点位的状态变化和持续时间判断
 * 支持任意条件的持续时间监控，如：
 * - 温度 > 30度 持续 10分钟
 * - 占用状态 = "UNOCCUPIED" 持续 15分钟
 * - 照度 < 100 持续 5分钟
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class StateManager {

    private static final Logger logger = LoggerFactory.getLogger(StateManager.class);
    
    // K: deviceCode_pointId, V: 点位状态对象
    private final Map<String, DevicePointState> devicePointStates = new ConcurrentHashMap<>();
    
    // K: conditionId, V: 状态条件监控
    private final Map<String, StateConditionMonitor> stateConditionMonitors = new ConcurrentHashMap<>();

    private final ScheduledExecutorService scheduler;

    @Autowired
    private DeviceEventPublisher eventPublisher;

    @Autowired
    private RuleEngineConfig.RuleEngineProperties ruleEngineProperties;

    @Autowired
    public StateManager(@Qualifier("deviceStateScheduler") ScheduledExecutorService scheduler,DeviceEventPublisher eventPublisher) {
        this.scheduler = scheduler;
        this.eventPublisher = eventPublisher;
    }
    /**
     * 处理设备点位数据更新
     *
     * @param deviceCode 设备Code
     * @param pointId 点位ID
     * @param value 点位值
     */
    public void processDevicePointUpdate(String deviceCode, String pointId, Object value) {
        if(deviceCode == null || pointId == null || value == null) {
            logger.warn("Missing required fields. DeviceCode: {}, PointId: {}, Value: {}", deviceCode, pointId, value);
            return;
        }

        String stateKey = generateStateKey(deviceCode, pointId);

        // 优化：只处理已注册的设备点位
        DevicePointState pointState = devicePointStates.get(stateKey);
        if (pointState == null) {
            logger.debug("Ignoring update for unregistered device point: {}.{}", deviceCode, pointId);
            return;
        }

        // 记录旧值
        Object oldValue = pointState.getCurrentValue();

        // 更新点位状态
        pointState.updateValue(value);

        // 创建状态变化事件
        StateChangeEvent changeEvent = StateChangeEvent.createValueChangeEvent(deviceCode, pointId, oldValue, value);

        logger.debug("Device point updated: {}.{} = {} (was: {})", deviceCode, pointId, value, oldValue);

        // 检查所有相关的状态条件
        checkStateConditions(deviceCode, pointId, pointState, changeEvent);

        // 发布事件而不是直接调用规则引擎
        eventPublisher.publishDeviceStateChange(deviceCode, pointId, oldValue,value);
    }

    public StateCondition getStateCondition(String conditionId) {
        StateConditionMonitor monitor = stateConditionMonitors.get(conditionId);
        if (monitor != null) {
            return monitor.condition;
        }
        return null;
    }


    /**
     * 注册状态条件监控
     * 
     * @param condition 状态条件
     */
    public void registerStateCondition(StateCondition condition) {
        if (condition == null || condition.getDeviceCode()==null || condition.getPointId() == null) {
            logger.warn("Missing required fields. DeviceCode: {}, PointId: {}", condition==null?null:condition.getDeviceCode(), condition==null?null:condition.getPointId());
            return;
        }
        if (!condition.isEnabled()) {
            logger.debug("Skipping disabled condition: {}", condition.getConditionId());
            return;
        }
        
        StateConditionMonitor monitor = new StateConditionMonitor(condition, scheduler, this);
        stateConditionMonitors.put(condition.getConditionId(), monitor);
        
        logger.info("Registered state condition: {}", condition.getConditionExpression());
        
        // 检查当前状态是否已经满足条件
        String stateKey = generateStateKey(condition.getDeviceCode(), condition.getPointId());
        DevicePointState pointState = devicePointStates.get(stateKey);
        if (pointState != null) {
            monitor.checkCondition(pointState);
        }
    }

    /**
     * 注销状态条件监控
     * 
     * @param conditionId 条件ID
     */
    public void unregisterStateCondition(String conditionId) {
        StateConditionMonitor monitor = stateConditionMonitors.remove(conditionId);
        if (monitor != null) {
            monitor.cancel();
            logger.info("Unregistered state condition: {}", conditionId);
        }
    }

    /**
     * 检查状态条件
     */
    private void checkStateConditions(String deviceCode, String pointId, DevicePointState pointState, StateChangeEvent changeEvent) {
        // 查找所有相关的状态条件监控
        stateConditionMonitors.values().stream().filter(monitor -> monitor.isRelevantTo(deviceCode, pointId)).forEach(monitor -> monitor.checkCondition(pointState));
    }

    /**
     * 处理条件超时事件
     * 由StateConditionMonitor调用
     */
    public void handleConditionTimeout(StateCondition condition) {
        DevicePointState pointState = getDevicePointState(condition.getDeviceCode(), condition.getPointId());
        if (pointState == null) {
            logger.warn("Device point state not found for condition: {}, refer ruleId:{}", condition.getConditionId(), condition.getRuleId());
            return;
        }
        logger.info("Condition timeout,refer ruleId: {}, expression: {} for device {}.{}, actual duration: {}s", condition.getRuleId(),condition.getConditionExpression(), condition.getDeviceCode(), condition.getPointId(), condition.getCurrentDurationSeconds());
        // 发布事件而不是直接调用规则引擎
        eventPublisher.publishDeviceTimeout(condition.getConditionId(),condition.getDeviceCode(), condition.getPointId(), pointState.getPreviousValue(), pointState.getCurrentValue());
    }

    /**
     * 获取设备点位状态
     */
    public DevicePointState getDevicePointState(String deviceCode, String pointId) {
        String stateKey = generateStateKey(deviceCode, pointId);
        return devicePointStates.get(stateKey);
    }

    /**
     * 获取所有设备状态统计
     */
    public Map<String, Object> getStateStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("totalDevicePoints", devicePointStates.size());
        stats.put("activeConditionMonitors", stateConditionMonitors.size());
        
        long enabledMonitors = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.isEnabled() ? 1 : 0)
            .sum();
        stats.put("enabledConditionMonitors", enabledMonitors);
        
        long activeTimeoutTasks = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.hasActiveTimeoutTask() ? 1 : 0)
            .sum();
        stats.put("activeTimeoutTasks", activeTimeoutTasks);
        
        return stats;
    }

    /**
     * 获取设备状态摘要
     */
    public Map<String, DevicePointState> getDeviceStates() {
        return devicePointStates;
    }

    /**
     * 获取持续时长设备状态
     * @return
     */
    public List<StateCondition> getStateConditions() {
        List<StateCondition> conditions = new ArrayList<>();
        stateConditionMonitors.forEach((k,v)->{
            StateConditionMonitor monitor = stateConditionMonitors.get(k);
            if (monitor != null) {
                conditions.add(monitor.condition);
            }
        });
        return conditions;
    }
    /**
     * 注册设备点位状态（添加规则引用）
     */
    public void registerDevicePointState(String deviceCode, String pointId, long ruleId) {
        if (deviceCode == null || pointId == null) {
            logger.warn("Missing required fields. DeviceCode: {}, PointId: {}", deviceCode, pointId);
            return;
        }
        String stateKey = generateStateKey(deviceCode, pointId);
        DevicePointState pointState = devicePointStates.computeIfAbsent(stateKey, k -> new DevicePointState(deviceCode, pointId));
        pointState.addRuleReference(ruleId);

        logger.debug("Registered device point state: {}.{} for rule {}", deviceCode, pointId, ruleId);
    }

    /**
     * 注销设备点位状态（移除规则引用）
     */
    public void unregisterDevicePointState(String deviceCode, String pointId, long ruleId) {
        String stateKey = generateStateKey(deviceCode, pointId);
        DevicePointState pointState = devicePointStates.get(stateKey);

        if (pointState != null) {
            pointState.removeRuleReference(ruleId);

            // 如果没有规则引用了，则移除设备状态
            if (!pointState.hasRuleReferences()) {
                devicePointStates.remove(stateKey);
                logger.debug("Removed device point state: {}.{} (no more rule references)", deviceCode, pointId);
            } else {
                logger.debug("Unregistered device point state: {}.{} from rule {}", deviceCode, pointId, ruleId);
            }
        }
    }

    /**
     * 清理指定规则的所有设备点位引用
     */
    public void unregisterDevicePointStateByRuleId(long ruleId) {
        int cleanedCount = 0;
        Set<String> keysToRemove = new HashSet<>();

        for (Map.Entry<String, DevicePointState> entry : devicePointStates.entrySet()) {
            DevicePointState pointState = entry.getValue();
            pointState.removeRuleReference(ruleId);

            if (!pointState.hasRuleReferences()) {
                keysToRemove.add(entry.getKey());
            }
            cleanedCount++;
        }

        // 移除没有引用的设备状态
        keysToRemove.forEach(devicePointStates::remove);

        logger.info("Cleaned up {} device point references for rule {}, removed {} unused states", cleanedCount, ruleId, keysToRemove.size());
    }


    /**
     * 定时清理历史数据
     */
    @Scheduled(fixedRateString = "#{${rule.engine.state.cleanup-interval-minutes:10} * 60 * 1000}")
    public void scheduledCleanupValueHistory() {
        try {
            int retentionHours = ruleEngineProperties.getState().getHistoryRetentionHours();
            int cleanedCount = 0;

            for (DevicePointState state : devicePointStates.values()) {
                int beforeSize = state.getValueHistory().size();
                state.cleanupOldHistory(retentionHours);
                int afterSize = state.getValueHistory().size();
                if (beforeSize > afterSize) {
                    cleanedCount += (beforeSize - afterSize);
                }
            }

            if (cleanedCount > 0) {
                logger.info("Scheduled cleanup removed {} old history records from {} device states", cleanedCount, devicePointStates.size());
            }

        } catch (Exception e) {
            logger.error("Error during scheduled value history cleanup", e);
        }
    }

    /**
     * 生成状态键
     */
    private String generateStateKey(String deviceCode, String pointId) {
        return deviceCode + "." + pointId;
    }

    /**
     * 清除所有状态条件监控
     * 用于重新加载规则配置时清理现有条件
     */
    public void clearAllStateConditions() {
        try {
            int cancelledCount = stateConditionMonitors.size();

            // 取消所有状态条件监控任务
            stateConditionMonitors.values().forEach(StateConditionMonitor::cancel);
            stateConditionMonitors.clear();

            logger.info("Cleared {} state condition monitors", cancelledCount);

        } catch (Exception e) {
            logger.error("Error clearing state conditions: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取所有状态条件的统计信息
     *
     * @return 状态条件统计信息
     */
    public Map<String, Object> getStateConditionStatistics() {
        Map<String, Object> stats = new HashMap<>();

        stats.put("totalConditions", stateConditionMonitors.size());

        long enabledConditions = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.isEnabled() ? 1 : 0)
            .sum();
        stats.put("enabledConditions", enabledConditions);

        long activeTimeoutTasks = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.hasActiveTimeoutTask() ? 1 : 0)
            .sum();
        stats.put("activeTimeoutTasks", activeTimeoutTasks);

        // 按规则ID分组统计
        Map<Long, Long> conditionsByRule = new HashMap<>();
        stateConditionMonitors.values().forEach(monitor -> {
            Long ruleId = monitor.condition.getRuleId();
            if (ruleId != null) {
                conditionsByRule.put(ruleId, conditionsByRule.getOrDefault(ruleId, 0L) + 1);
            }
        });
        stats.put("conditionsByRule", conditionsByRule);

        return stats;
    }

    /**
     * 获取指定规则的状态条件列表
     *
     * @param ruleId 规则ID
     * @return 状态条件列表
     */
    public List<StateCondition> getStateConditionsByRule(long ruleId) {

        return stateConditionMonitors.values().stream()
            .map(monitor -> monitor.condition)
            .filter(condition -> ruleId == condition.getRuleId())
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 检查指定的状态条件是否已注册
     *
     * @param conditionId 条件ID
     * @return 是否已注册
     */
    public boolean isStateConditionRegistered(String conditionId) {
        return stateConditionMonitors.containsKey(conditionId);
    }

    /**
     * 关闭时清理资源
     */
    @PreDestroy
    public void shutdown() {
        logger.info("Shutting down StateManager...");

        // 取消所有状态条件监控
        stateConditionMonitors.values().forEach(StateConditionMonitor::cancel);
        stateConditionMonitors.clear();

        // 清理设备状态
        devicePointStates.clear();

        logger.info("StateManager shutdown completed");
    }

    /**
     * 状态条件监控内部类
     * 异步MQTT数据的持续时间判断
     *  - 物联网设备通过MQTT异步发送数据，无法预知数据到达时间
     *  - 需要监控"温度 > 30度 持续 10分钟"这类持续时间条件
     *  - stateConditionMonitors容器管理每个条件的定时器任务，实现精确的持续时间判断
     * 多条件规则的状态协调
     *  - EasyRules本身是无状态的，但多条件规则需要状态管理
     *  - 不同传感器数据异步到达，需要协调多个条件的满足状态
     *  - stateConditionMonitors容器统一管理所有状态条件的监控状态
     * 事件驱动架构的状态持久化
     *  - 规则引擎采用事件驱动架构，状态需要在事件间保持
     *  - 容器提供了状态条件的持久化监控机制
     */
    private static class StateConditionMonitor {
        final StateCondition condition; // 改为包级别访问，供外部类访问
        private final ScheduledExecutorService scheduler;
        private final StateManager stateManager;
        private ScheduledFuture<?> timeoutTask;
        
        public StateConditionMonitor(StateCondition condition, ScheduledExecutorService scheduler, StateManager stateManager) {
            this.condition = condition;
            this.scheduler = scheduler;
            this.stateManager = stateManager;
        }

        /**
         * 设备点位状态变化时检查条件是否满足
         * @param pointState
         */
        public void checkCondition(DevicePointState pointState) {
            boolean conditionMet = condition.matches(pointState.getCurrentValue());

            // 更新StateCondition的状态
            boolean wasMetBefore = condition.isCurrentlyMet();
            condition.updateConditionState(conditionMet);

            if (conditionMet && !wasMetBefore) {
                // 条件开始满足，启动超时任务
                startTimeoutTask();
            } else if (!conditionMet && wasMetBefore) {
                // 条件不再满足，取消超时任务
                cancelTimeoutTask();
            }
            //conditionMet和wasMetBefore都为true时，说明条件持续满足，不需要做任何操作
        }

        /**
         * 启动超时计时
         */
        private void startTimeoutTask() {
            // 先取消之前的任务
            cancelTimeoutTask();
            //开启新的超时任务
            timeoutTask = scheduler.schedule(() -> {
                //再次检查条件是否仍然满足
                if (condition.isDurationSatisfied() && isValueSatisfied()) {
                    // 触发规则评估，不需要传递持续时间参数
                    stateManager.handleConditionTimeout(condition);
                }
            }, condition.getDurationSeconds(), TimeUnit.SECONDS);
        }

        private boolean isValueSatisfied() {
            DevicePointState pointState = stateManager.getDevicePointState(condition.getDeviceCode(), condition.getPointId());
            if (pointState == null) {
                return false;
            }
            return condition.matches(pointState.getCurrentValue());
        }
        
        private void cancelTimeoutTask() {
            if (timeoutTask != null && !timeoutTask.isDone()) {
                timeoutTask.cancel(false);
            }
            timeoutTask = null;
        }
        
        public void cancel() {
            cancelTimeoutTask();
        }
        
        public boolean isRelevantTo(String deviceCode, String pointId) {
            return condition.getDeviceCode().equals(deviceCode) && condition.getPointId().equals(pointId);
        }
        
        public boolean isEnabled() {
            return condition.isEnabled();
        }
        
        public boolean hasActiveTimeoutTask() {
            return timeoutTask != null && !timeoutTask.isDone();
        }
    }
}
