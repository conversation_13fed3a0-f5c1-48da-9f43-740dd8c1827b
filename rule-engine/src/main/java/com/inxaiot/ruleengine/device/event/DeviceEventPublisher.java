package com.inxaiot.ruleengine.device.event;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 设备事件发布器
 * 用于解耦StateManager和RuleEngineService之间的循环依赖
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Component
public class DeviceEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布设备状态变化事件
     * 
     * @param deviceCode 设备Code
     * @param pointId 点位ID
     * @param oldValue 旧值
     * @param newValue 新值
     */
    public void publishDeviceStateChange(String deviceCode, String pointId, Object oldValue, Object newValue) {
        StateChangeEvent event = new StateChangeEvent();
        event.setDeviceCode(deviceCode);
        event.setPointId(pointId);
        event.setEventType(StateChangeEvent.EventType.VALUE_CHANGED);
        event.setOldValue(oldValue);
        event.setCurrentValue(newValue);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 发布设备超时事件
     * 
     * @param deviceCode 设备Code
     * @param pointId 点位ID
     */
    public void publishDeviceTimeout(String conditionId,String deviceCode, String pointId, Object oldValue, Object newValue) {
        StateChangeEvent event = new StateChangeEvent();
        event.setConditionId(conditionId);
        event.setDeviceCode(deviceCode);
        event.setPointId(pointId);
        event.setOldValue(oldValue);
        event.setCurrentValue(newValue);
        event.setEventType(StateChangeEvent.EventType.CONDITION_TIMEOUT);
        eventPublisher.publishEvent(event);
    }
}
