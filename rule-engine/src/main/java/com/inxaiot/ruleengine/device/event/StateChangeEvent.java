package com.inxaiot.ruleengine.device.event;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;

/**
 * 状态变化事件模型
 * 记录设备点位状态的变化事件
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class StateChangeEvent {
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 设备Code
     */
    private String deviceCode;
    
    /**
     * 点位ID
     */
    private String pointId;
    
    /**
     * 事件类型
     */
    private EventType eventType;
    
    /**
     * 旧值
     */
    private Object oldValue;
    
    /**
     * 新值
     */
    private Object currentValue;
    
    /**
     * 事件时间
     */
    private LocalDateTime eventTime;
    
    /**
     * 条件ID（如果是条件满足事件）
     */
    private String conditionId;
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        VALUE_CHANGED,           // 值变化
        CONDITION_MET,           // 条件满足
        CONDITION_TIMEOUT,       // 条件超时
        CONDITION_RESET,         // 条件重置
        STATE_UPDATED           // 状态更新
    }
    
    /**
     * 构造函数
     */
    public StateChangeEvent() {
        this.eventTime = LocalDateTime.now();
        this.eventId = generateEventId();
        this.properties = new HashMap<>();
    }
    
    /**
     * 构造函数
     */
    public StateChangeEvent(String deviceCode, String pointId, EventType eventType) {
        this();
        this.deviceCode = deviceCode;
        this.pointId = pointId;
        this.eventType = eventType;
    }
    
    /**
     * 构造函数（值变化事件）
     */
    public StateChangeEvent(String deviceCode, String pointId, Object oldValue, Object currentValue) {
        this(deviceCode, pointId, EventType.VALUE_CHANGED);
        this.oldValue = oldValue;
        this.currentValue = currentValue;
    }
    
    /**
     * 生成事件ID
     */
    private String generateEventId() {
        return "EVENT_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    /**
     * 创建值变化事件
     */
    public static StateChangeEvent createValueChangeEvent(String deviceCode, String pointId, Object oldValue, Object newValue) {
        return new StateChangeEvent(deviceCode, pointId, oldValue, newValue);
    }


    /**
     * 添加属性
     */
    public void addProperty(String key, Object value) {
        if (this.properties == null) {
            this.properties = new HashMap<>();
        }
        this.properties.put(key, value);
    }

    /**
     * 获取属性
     */
    public Object getProperty(String key) {
        return this.properties != null ? this.properties.get(key) : null;
    }

    
    // Getters and Setters
    public String getEventId() {
        return eventId;
    }
    
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }
    
    public String getDeviceCode() {
        return deviceCode;
    }
    
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    public EventType getEventType() {
        return eventType;
    }
    
    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }
    
    public Object getOldValue() {
        return oldValue;
    }
    
    public void setOldValue(Object oldValue) {
        this.oldValue = oldValue;
    }
    
    public Object getCurrentValue() {
        return currentValue;
    }
    
    public void setCurrentValue(Object currentValue) {
        this.currentValue = currentValue;
    }

    
    public LocalDateTime getEventTime() {
        return eventTime;
    }
    
    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
    }
    
    public String getConditionId() {
        return conditionId;
    }
    
    public void setConditionId(String conditionId) {
        this.conditionId = conditionId;
    }

    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Map<String, Object> getProperties() {
        return properties;
    }
    
    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }
    
    @Override
    public String toString() {
        return "StateChangeEvent{" +
                "eventId='" + eventId + '\'' +
                ", deviceCode='" + deviceCode + '\'' +
                ", pointId='" + pointId + '\'' +
                ", eventType=" + eventType +
                ", eventTime=" + eventTime +
                ", oldValue=" + oldValue +
                ", currentValue=" + currentValue +
                ", conditionId='" + conditionId + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
