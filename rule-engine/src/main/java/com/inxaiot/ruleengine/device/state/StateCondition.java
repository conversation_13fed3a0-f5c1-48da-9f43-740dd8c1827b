package com.inxaiot.ruleengine.device.state;


import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.common.operator.ValueComparator;
import com.inxaiot.ruleengine.core.FactKey;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 状态条件模型
 * 定义需要监控的状态条件，如：
 * - 温度 > 30度 持续 10分钟
 * - 占用状态 = "UNOCCUPIED" 持续 15分钟
 * - 照度 < 100 持续 5分钟
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class StateCondition {
    
    /**
     * 条件ID（唯一标识）
     */
    @Setter
    @Getter
    private String conditionId;
    
    /**
     * 设备Code
     */
    @Setter
    @Getter
    private String deviceCode;
    
    /**
     * 点位ID
     */
    @Setter
    @Getter
    private String pointId;
    
    /**
     * 操作符
     * EQUALS, NOT_EQUALS, GREATER_THAN, GREATER_THAN_OR_EQUAL, 
     * LESS_THAN, LESS_THAN_OR_EQUAL, BETWEEN, IN, NOT_IN, etc.
     */
    @Setter
    @Getter
    private String operator;

    /**
     * 目前仅当 operator=STATES_KEEP_SECONDS 时生效
     */
    private String subOperator = Operators.Basic.EQUALS;
    
    /**
     * 目标值（用于比较）
     */
    @Setter
    @Getter
    private Object value;
    
    /**
     * 第二个目标值（用于BETWEEN操作符）
     */
    @Setter
    private Object value2;
    
    /**
     * 状态需要的持续时间（秒）要求
     */
    @Setter
    @Getter
    private long durationSeconds;
    
    /**
     * 数据类型
     */
    @Setter
    @Getter
    private String dataType;
    
    /**
     * 条件描述
     */
    @Setter
    @Getter
    private String description;
    
    /**
     * 是否启用
     */
    @Setter
    @Getter
    private boolean enabled = true;
    
    /**
     * 关联的规则ID
     */
    @Setter
    @Getter
    private Long ruleId;

    /**
     * 状态满足条件开始时间
     */
    private LocalDateTime conditionStartTime;
    /**
     * 当前状态是否满足条件
     */
    private boolean currentlyMet = false;

    public StateCondition() {}

    public boolean isCurrentlyMet() {
        return currentlyMet;
    }

    /**
     * 判断条件状态是否满足，来更新计时器
     */
    public void updateConditionState(boolean conditionMet) {
        //当前状态满足，之前没有满足过，则记录开始时间
        if (conditionMet && !currentlyMet) {
            conditionStartTime = LocalDateTime.now();
            currentlyMet = true;
        //当前状态不满足，之前满足过，则清空开始时间
        } else if (!conditionMet && currentlyMet) {
            conditionStartTime = null;
            currentlyMet = false;
        }
        //当前状态满足，之前状态满足，计时不处理(计时保持)
        //当前状态不满足，之前状态也不满足，计时不处理（因为之前就是null）
    }

    /**
     * 获取当前条件满足的持续状态时间
     * @return
     */
    public long getCurrentDurationSeconds() {
        return conditionStartTime != null ? Duration.between(conditionStartTime, LocalDateTime.now()).getSeconds() : 0;
    }

    /**
     * 状态条件是否满足，规则评估时会用到
     * @return
     */
    public boolean isDurationSatisfied() {
        return getCurrentDurationSeconds() >= durationSeconds;
    }

    /**
     * 构造函数
     */
    public StateCondition(String conditionId, String deviceCode, String pointId, 
                         String operator, String subOperator,Object targetValue, long durationSeconds) {
        this.conditionId = conditionId;
        this.deviceCode = deviceCode;
        this.pointId = pointId;
        this.operator = operator;
        this.subOperator = subOperator==null?Operators.Basic.EQUALS:subOperator;
        this.value = targetValue;
        this.durationSeconds = durationSeconds;
    }
    
    /**
     * 构造函数（用于BETWEEN操作符）
     */
    public StateCondition(String conditionId, String deviceCode, String pointId, 
                         String operator, String subOperator, Object targetValue, Object targetValue2, long durationSeconds) {
        this(conditionId, deviceCode, pointId, operator, subOperator, targetValue, durationSeconds);
        this.value2 = targetValue2;
    }
    
    /**
     * 生成条件ID
     */
    public static String generateConditionId(String deviceCode, String pointId, String operator, Object targetValue, long durationSeconds) {
        return String.format("%s_%s_%s_%s_%d", deviceCode, pointId, operator, String.valueOf(targetValue), durationSeconds);
    }
    
    /**
     * 检查条件是否匹配指定的设备点位值
     */
    public boolean matches(Object currentValue) {
        return ValueComparator.compare(currentValue, subOperator==null?Operators.Basic.EQUALS:subOperator, value, value2,dataType);
    }
    
    /**
     * 获取条件的字符串表示
     */
    public String getConditionExpression() {
        StringBuilder sb = new StringBuilder();
        sb.append(FactKey.getStateKey(deviceCode,pointId));
        sb.append(" ").append(subOperator).append(" ");
        if (Operators.Range.BETWEEN.equalsIgnoreCase(operator) && value2 != null) {
            sb.append(value).append(" AND ").append(value2);
        } else {
            sb.append(value);
        }
        
        sb.append(" FOR ").append(durationSeconds).append(" SECONDS");
        
        return sb.toString();
    }
    
    // Getters and Setters

    @Override
    public String toString() {
        return "StateCondition{" +
                "conditionId='" + conditionId + '\'' +
                ", expression='" + getConditionExpression() + '\'' +
                ", enabled=" + enabled +
                ", ruleId=" + ruleId +
                ", conditionStartTime=" + conditionStartTime +
                ", currentlyMet=" + currentlyMet +
                '}';
    }
}
