package com.inxaiot.ruleengine.device.state;

import java.time.Duration;
import java.util.Objects;

/**
 * 设备点位引用
 * 用于表示规则中引用的设备点位及其时效性要求
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DevicePointRef {
    
    /**
     * 设备Code
     */
    private String deviceCode;
    
    /**
     * 点位ID
     */
    private String pointId;

    
    public DevicePointRef() {
    }
    
    public DevicePointRef(String deviceCode, String pointId) {
        this.deviceCode = deviceCode;
        this.pointId = pointId;
    }
    /**
     * 创建设备点位引用的便捷方法
     */
    public static DevicePointRef of(String deviceCode, String pointId) {
        return new DevicePointRef(deviceCode, pointId);
    }

    
    // Getters and Setters
    
    public String getDeviceCode() {
        return deviceCode;
    }
    
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DevicePointRef that = (DevicePointRef) o;
        return Objects.equals(deviceCode, that.deviceCode) && Objects.equals(pointId, that.pointId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(deviceCode, pointId);
    }
    
    @Override
    public String toString() {
        return "DevicePointRef{" +
                "deviceCode='" + deviceCode + '\'' +
                ", pointId='" + pointId + '\'' +
                '}';
    }
}
