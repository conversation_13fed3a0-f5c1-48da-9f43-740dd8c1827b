package com.inxaiot.ruleengine.device.state;


import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备点位状态模型
 * 记录设备某个点位的当前状态和历史变化
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class DevicePointState {
    
    /**
     * 设备Code
     */
    @Setter
    @Getter
    private String deviceCode;
    
    /**
     * 点位ID
     */
    @Setter
    @Getter
    private String pointId;
    
    /**
     * 当前值
     */
    @Getter
    private Object currentValue;
    
    /**
     * 上次值
     */
    @Getter
    private Object previousValue;
    
    /**
     * 当前值开始时间
     */
    private LocalDateTime currentValueStartTime;
    
    /**
     * 上次更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 值变化历史（最近N次变化）
     */
    private final Map<LocalDateTime, Object> valueHistory = new ConcurrentHashMap<>();
    
    /**
     * 扩展属性
     */
    private final Map<String, Object> properties = new ConcurrentHashMap<>();

    /**
     * 引用此设备点位的规则ID列表
     * 用于跟踪哪些规则依赖此设备状态
     */
    private final Set<Long> ruleIds = ConcurrentHashMap.newKeySet();
    
    /**
     * 构造函数
     */
    public DevicePointState(String deviceCode, String pointId) {
        this.deviceCode = deviceCode;
        this.pointId = pointId;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 更新点位值
     */
    public synchronized void updateValue(Object newValue) {
        this.previousValue = this.currentValue;
        this.currentValue = newValue;
        
        LocalDateTime now = LocalDateTime.now();
        this.lastUpdateTime = now;
        
        // 如果值发生变化，更新开始时间
        if (!isValueEqual(previousValue, currentValue)) {
            this.currentValueStartTime = now;
            // 记录历史
            this.valueHistory.put(now, newValue);
            
            // 保持历史记录数量限制（最多保留100条）
            if (this.valueHistory.size() > 100) {
                this.valueHistory.keySet().stream().min(LocalDateTime::compareTo).ifPresent(this.valueHistory::remove);
            }
        }
    }
    /**
     * 获取当前值持续时间（秒）
     */
    public long getCurrentValueDurationSeconds() {
        if (currentValueStartTime == null) {
            return 0;
        }
        return java.time.Duration.between(currentValueStartTime, LocalDateTime.now()).getSeconds();
    }

    
    /**
     * 判断值是否相等
     */
    private boolean isValueEqual(Object value1, Object value2) {
        if (value1 == null && value2 == null) {
            return true;
        }
        if (value1 == null || value2 == null) {
            return false;
        }
        return value1.toString().equals(value2.toString());
    }
    
    /**
     * 重置日常计数器（跨天时调用）
     * 保留当前值，但重置时间相关信息
     */
    public void resetDailyCounters() {
        // 重置当前值的开始时间为当前时间
        this.currentValueStartTime = LocalDateTime.now();

        // 清理历史记录（可选，根据业务需要）
        this.valueHistory.clear();

        // 清理扩展属性中的日常统计信息
        this.properties.entrySet().removeIf(entry -> entry.getKey().startsWith("daily_") || entry.getKey().startsWith("count_"));
    }

    /**
     * 添加规则引用
     */
    public synchronized void addRuleReference(Long ruleId) {
        if (ruleId != null) {
            ruleIds.add(ruleId);
        }
    }

    /**
     * 移除规则引用
     */
    public synchronized void removeRuleReference(Long ruleId) {
        if (ruleId != null) {
            ruleIds.remove(ruleId);
        }
    }

    /**
     * 检查是否有规则引用
     */
    public boolean hasRuleReferences() {
        return !ruleIds.isEmpty();
    }

    /**
     * 获取引用的规则ID列表（只读副本）
     */
    public Set<Long> getRuleIds() {
        return new HashSet<>(ruleIds);
    }

    /**
     * 清理历史数据（基于时间窗口）
     */
    public synchronized void cleanupOldHistory(int retentionHours) {
        if (retentionHours <= 0) {
            return;
        }
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(retentionHours);
        valueHistory.entrySet().removeIf(entry -> entry.getKey().isBefore(cutoffTime));
    }

    
    // Getters and Setters

    
    public Map<LocalDateTime, Object> getValueHistory() {
        return new ConcurrentHashMap<>(valueHistory);
    }

    public Object getProperty(String key) {
        return this.properties.get(key);
    }
    
    @Override
    public String toString() {
        return "DevicePointState{" +
                "deviceCode='" + deviceCode + '\'' +
                ", pointId='" + pointId + '\'' +
                ", currentValue=" + currentValue +
                ", currentValueDurationSeconds=" + getCurrentValueDurationSeconds() +
                '}';
    }
}
