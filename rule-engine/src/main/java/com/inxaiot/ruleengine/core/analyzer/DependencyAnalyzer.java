package com.inxaiot.ruleengine.core.analyzer;

import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.device.state.DevicePointRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 依赖分析器
 * 用于分析规则定义中引用的设备点位依赖关系
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DependencyAnalyzer {

    private static final Logger logger = LoggerFactory.getLogger(DependencyAnalyzer.class);
    
    // 匹配设备点位引用的正则表达式，如：device123.temperature, sensor_01.humidity
    private static final Pattern DEVICE_POINT_PATTERN = Pattern.compile("([a-zA-Z0-9_-]+)\\.([a-zA-Z0-9_-]+)");
    
    /**
     * 从规则定义中提取所有需要的设备点位依赖
     * 
     * @param rule 规则定义
     * @return 设备点位引用集合
     */
    public Set<DevicePointRef> extractRequiredDevicePoints(RuleDefinition rule) {
        Set<DevicePointRef> dependencies = new HashSet<>();
        
        try {
            // 分析触发条件中的设备引用
            if (rule.getTriggerCondition() != null) {
                dependencies.addAll(analyzeTriggerCondition(rule.getTriggerCondition()));
            }

            // 分析时间条件中可能的设备引用（某些时间条件可能依赖设备状态），比如：
            //"在设备A启动后的2小时内"
            //"在温度传感器记录的日出时间到日落时间之间"
            //"在设备B的工作时间段内"
            //"在上次维护时间后的30天内"
            // 注：大部分时间条件不依赖设备状态，这里预留扩展，如果用到，需要在TimeCondition中增加动态时间表达式，在这里增加动态依赖分析，然后在TimeConditionEvaluator里增加动态条件评估

            logger.debug("Extracted {} device point dependencies for rule {}", dependencies.size(), rule.getRuleId());

        } catch (Exception e) {
            logger.error("Error extracting dependencies for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }
        
        return dependencies;
    }
    
    /**
     * 分析触发条件中的设备点位依赖
     */
    private Set<DevicePointRef> analyzeTriggerCondition(TriggerCondition triggerCondition) {
        Set<DevicePointRef> dependencies = new HashSet<>();

        if (triggerCondition.getConditions() != null) {
            for (DeviceCondition deviceCondition : triggerCondition.getConditions()) {
                dependencies.addAll(analyzeDeviceCondition(deviceCondition));
            }
        }

        return dependencies;
    }
    
    /**
     * 分析单个设备条件中的设备点位依赖
     */
    private Set<DevicePointRef> analyzeDeviceCondition(DeviceCondition condition) {
        Set<DevicePointRef> dependencies = new HashSet<>();

        try {
            // 从设备Code和点位ID直接提取
            if (condition.getSourceDeviceCode() != null && condition.getPointId() != null) {
                DevicePointRef ref = new DevicePointRef(condition.getSourceDeviceCode(), condition.getPointId());
                dependencies.add(ref);
                logger.trace("Found direct device reference: {}.{}", condition.getSourceDeviceCode(), condition.getPointId());
            }

            //以下动态设备依赖，高级用法，即比较的值是另一个设备的值（动态的，非静态），目前场景基本没用到，但是支持

            // 从目标值中提取可能的设备引用（如果目标值是另一个设备的值），规则引擎支持两个设备值比较条件，比如设备A的功耗>设备B的功耗
            if (condition.getValue() instanceof String) {
                String targetValue = (String) condition.getValue();
                dependencies.addAll(extractFromExpression(targetValue));
            }

            // 从上限值中提取可能的设备引用（用于BETWEEN操作符）
            if (condition.getUpperValue() instanceof String) {
                String upperValue = (String) condition.getUpperValue();
                dependencies.addAll(extractFromExpression(upperValue));
            }

        } catch (Exception e) {
            logger.warn("Error analyzing device condition {} : {}", condition, e.getMessage());
        }

        return dependencies;
    }
    
    /**
     * 从表达式字符串中提取设备点位引用
     * 支持格式：device123.temperature, sensor_01.humidity
     */
    private Set<DevicePointRef> extractFromExpression(String expression) {
        Set<DevicePointRef> dependencies = new HashSet<>();
        
        if (expression == null || expression.trim().isEmpty()) {
            return dependencies;
        }
        
        Matcher matcher = DEVICE_POINT_PATTERN.matcher(expression);
        while (matcher.find()) {
            String deviceCode = matcher.group(1);
            String pointId = matcher.group(2);
            
            DevicePointRef ref = new DevicePointRef(deviceCode, pointId);
            dependencies.add(ref);
            
            logger.trace("Found device reference in expression '{}': {}.{}", expression, deviceCode, pointId);
        }
        
        return dependencies;
    }
    
    /**
     * 检查规则是否依赖特定的设备点位
     */
    public boolean isDependentOn(RuleDefinition rule, String deviceCode, String pointId) {
        Set<DevicePointRef> dependencies = extractRequiredDevicePoints(rule);
        DevicePointRef target = new DevicePointRef(deviceCode, pointId);
        return dependencies.contains(target);
    }
    
    /**
     * 获取与指定设备点位相关的所有规则
     */
    public boolean isRuleRelatedToDevice(RuleDefinition rule, String deviceCode, String pointId) {
        return isDependentOn(rule, deviceCode, pointId);
    }
}
