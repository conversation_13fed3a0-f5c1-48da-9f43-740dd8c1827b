package com.inxaiot.ruleengine.core.definition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.inxaiot.ruleengine.common.operator.Operators;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * 时间条件定义
 * 支持复杂的时间条件组合，包括Cron表达式、工作日、季节、个性化日历等
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Setter
@Getter
public class TimeCondition {
    /**
     * 时间段定义
     * 表示一个具体的起止时间段（月日）
     */
    @Setter
    @Getter
    public static class MonthDayRange {
        /**
         * 开始月日，格式："MM-dd"
         */
        @JsonProperty("startDate")
        private String startDate;

        /**
         * 结束月日，格式："MM-dd"
         */
        @JsonProperty("endDate")
        private String endDate;

        /**
         * 时间段描述
         */
        @JsonProperty("description")
        private String description;

        public MonthDayRange() {
        }

        public MonthDayRange(String startDate, String endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public MonthDayRange(String startDate, String endDate, String description) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.description = description;
        }

        /**
         * 检查指定月日是否在时间段内
         */
        public boolean contains(String monthDay) {
            if (monthDay == null || startDate == null || endDate == null) {
                return false;
            }

            // 验证格式
            if (!isValidMonthDayFormat(monthDay) || !isValidMonthDayFormat(startDate) || !isValidMonthDayFormat(endDate)) {
                return false;
            }

            // 如果开始日期小于等于结束日期，说明不跨年
            if (startDate.compareTo(endDate) <= 0) {
                return monthDay.compareTo(startDate) >= 0 && monthDay.compareTo(endDate) <= 0;
            } else {
                // 跨年情况：如12-01到02-28
                return monthDay.compareTo(startDate) >= 0 || monthDay.compareTo(endDate) <= 0;
            }
        }

        /**
         * 检查时间段是否有效
         */
        @JsonIgnore
        public boolean isValid() {
            return isValidMonthDayFormat(startDate) && isValidMonthDayFormat(endDate);
        }

        /**
         * 验证月日格式是否正确
         */
        private boolean isValidMonthDayFormat(String monthDay) {
            if (monthDay == null || monthDay.trim().isEmpty()) {
                return false;
            }

            String trimmed = monthDay.trim();
            if (!trimmed.matches("\\d{2}-\\d{2}")) {
                return false;
            }

            try {
                String[] parts = trimmed.split("-");
                int month = Integer.parseInt(parts[0]);
                int day = Integer.parseInt(parts[1]);

                // 检查月份范围
                if (month < 1 || month > 12) {
                    return false;
                }

                // 检查日期范围（简单检查，不考虑闰年）
                if (day < 1 || day > 31) {
                    return false;
                }

                // 检查特定月份的日期范围
                if ((month == 2 && day > 29) ||
                    ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30)) {
                    return false;
                }
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }

        @Override
        public String toString() {
            return "MonthDayRange{" +
                    "startDate='" + startDate + '\'' +
                    ", endDate='" + endDate + '\'' +
                    ", description='" + description + '\'' +
                    '}';
        }
    }
    /**
     * 时间段定义
     * 表示一个具体的起止时间段（年月日）
     */
    @Setter
    @Getter
    public static class DateRange {
        /**
         * 起始日期（包含）
         */
        @JsonProperty("startDate")
        private LocalDate startDate;

        /**
         * 结束日期（包含）
         */
        @JsonProperty("endDate")
        private LocalDate endDate;

        /**
         * 时间段描述
         */
        @JsonProperty("description")
        private String description;

        public DateRange() {
        }

        public DateRange(LocalDate startDate, LocalDate endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public DateRange(LocalDate startDate, LocalDate endDate, String description) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.description = description;
        }

        /**
         * 检查指定日期是否在此时间段内（包含边界）
         */
        public boolean contains(LocalDate date) {
            if (startDate == null || endDate == null || date == null) {
                return false;
            }
            return !date.isBefore(startDate) && !date.isAfter(endDate);
        }

        /**
         * 检查时间段是否有效
         */
        @JsonIgnore
        public boolean isValid() {
            return startDate != null && endDate != null && !startDate.isAfter(endDate);
        }

        @Override
        public String toString() {
            return "DateRange{" +
                    "startDate=" + startDate +
                    ", endDate=" + endDate +
                    ", description='" + description + '\'' +
                    '}';
        }
    }

    // Getters and Setters
    /**
     * Cron表达式列表，用于定义具体的时间段
     * 例如: ["0 0 8-10 * * ?", "0 0 14-16 * * ?"]
     * 表示上午8-10点和下午2-4点，具体星期由workDays字段控制
     */
    @JsonProperty("timeCronExpressions")
    private List<String> timeCronExpressions;
    
    /**
     * 工作日列表
     * 例如：["MON", "TUE", "WED", "THU", "FRI"]
     * 或者：["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]
     */
    @JsonProperty("workDays")
    private List<String> workDays;
    
    /**
     * 时令标识
     * 如 "SUMMER", "WINTER", "ALL"
     * 由全局日历表定义起止时间
     */
    @JsonProperty("season")
    private String season;
    
    /**
     * 个性化日历: 强制生效的日期列表
     * 即使其他条件不满足，这些日期也会使规则生效
     */
    @JsonProperty("includeDates")
    private List<LocalDate> includeDates;
    
    /**
     * 个性化日历: 强制不生效的日期列表
     * 即使其他条件满足，这些日期也不会使规则生效
     */
    @JsonProperty("excludeDates")
    private List<LocalDate> excludeDates;

    /**
     * 年度重复包含月日列表
     * 格式为"MM-dd"，如"02-14", "12-25"
     * 这些月日每年都会生效，不考虑年份
     * 即使其他条件不满足，这些月日也会使规则生效
     */
    @JsonProperty("includeMonthDays")
    private List<String> includeMonthDays;

    /**
     * 年度重复排除月日列表
     * 格式为"MM-dd"，如"02-14", "12-25"
     * 这些月日每年都会被排除，不考虑年份
     * 即使其他条件满足，这些月日也不会使规则生效
     */
    @JsonProperty("excludeMonthDays")
    private List<String> excludeMonthDays;

    /**
     * 起止时间列表
     * 定义多个时间段，每个时间段包含起始日期和结束日期
     * 列表中的时间段之间是OR关系，即满足任意一个时间段即可
     * 例如：[{startDate: "2024-01-01", endDate: "2024-01-31"}, {startDate: "2024-06-01", endDate: "2024-06-30"}]
     * 表示1月份或6月份生效
     */
    @JsonProperty("dateRanges")
    private List<DateRange> dateRanges;


    @JsonProperty("monthDayRanges")
    private List<MonthDayRange> monthDayRanges;
    
    /**
     * 时间条件的逻辑关系
     * AND: 所有时间条件都必须满足
     * OR: 任意一个时间条件满足即可
     */
    @JsonProperty("logic")
    private Operators.Logic logic = Operators.Logic.AND;
    
    /**
     * 时间条件是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 时间条件描述
     */
    @JsonProperty("description")
    private String description;

    // 构造函数
    public TimeCondition() {
    }

    public TimeCondition(List<String> timeCronExpressions) {
        this.timeCronExpressions = timeCronExpressions;
    }

    /**
     * 检查时间条件是否为空
     */
    @JsonIgnore
    public boolean isEmpty() {
        return (timeCronExpressions == null || timeCronExpressions.isEmpty()) &&
               (workDays == null || workDays.isEmpty()) &&
               (season == null || season.trim().isEmpty()) &&
               (includeDates == null || includeDates.isEmpty()) &&
               (excludeDates == null || excludeDates.isEmpty()) &&
               (includeMonthDays == null || includeMonthDays.isEmpty()) &&
               (excludeMonthDays == null || excludeMonthDays.isEmpty()) &&
               (dateRanges == null || dateRanges.isEmpty()) &&
               (monthDayRanges == null || monthDayRanges.isEmpty());
    }

    @Override
    public String toString() {
        return "TimeCondition{" +
                "timeCronExpressions=" + timeCronExpressions +
                ", workDays=" + workDays +
                ", season='" + season + '\'' +
                ", includeDates=" + includeDates +
                ", excludeDates=" + excludeDates +
                ", includeMonthDays=" + includeMonthDays +
                ", excludeMonthDays=" + excludeMonthDays +
                ", dateRanges=" + dateRanges +
                ", monthDayRanges=" + monthDayRanges +
                ", logic=" + logic +
                ", enabled=" + enabled +
                '}';
    }
}
