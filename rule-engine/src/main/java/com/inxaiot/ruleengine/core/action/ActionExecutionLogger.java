package com.inxaiot.ruleengine.core.action;

import com.inxaiot.ruleengine.RuleEngineConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 动作执行日志记录器
 * 专门用于记录动作执行相关的日志，独立于规则引擎主日志
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Component
public class ActionExecutionLogger {
    
    private static final Logger logger = LoggerFactory.getLogger(ActionExecutionLogger.class);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    @Autowired
    private RuleEngineConfig.RuleEngineProperties ruleEngineProperties;

    private boolean logEnabled = true;

    @PostConstruct
    public void init() {
        this.logEnabled = ruleEngineProperties.getLog().isActionEnable();

        if (logEnabled) {
            logger.info("Action execution logging initialized - enabled: {}", logEnabled);
        } else {
            logger.info("Action execution logging is disabled");
        }
    }
    
    /**
     * 记录动作开始执行
     */
    public void logActionStart(ActionExecutionRecord record) {
        if (!logEnabled) return;
        
        logger.info("[START] ExecutionId={} | RuleId={} | ActionType={} | Priority={} | Target={} | Time={}",
                record.getExecutionId(),
                record.getRuleId(),
                record.getActionDef().getActionType(),
                record.getActionDef().getPriority(),
                record.getTargetIdentifier(),
                formatTime(record.getCreateTime()));
    }
    
    /**
     * 记录动作执行成功
     */
    public void logActionSuccess(ActionExecutionRecord record) {
        if (!logEnabled) return;
        
        long duration = record.getLastExecuteTime() - record.getCreateTime();
        logger.info("[SUCCESS] ExecutionId={} | RuleId={} | ActionType={} | Target={} | Duration={}ms | RetryCount={} | Time={}",
                record.getExecutionId(),
                record.getRuleId(),
                record.getActionDef().getActionType(),
                record.getTargetIdentifier(),
                duration,
                record.getRetryCount(),
                formatTime(record.getLastExecuteTime()));
    }
    
    /**
     * 记录动作执行失败
     */
    public void logActionFailure(ActionExecutionRecord record) {
        if (!logEnabled) return;

        long duration = record.getLastExecuteTime() - record.getCreateTime();
        logger.error("[FAILED] ExecutionId={} | RuleId={} | ActionType={} | Target={} | Duration={}ms | RetryCount={} | Error={} | Time={}",
                record.getExecutionId(),
                record.getRuleId(),
                record.getActionDef().getActionType(),
                record.getTargetIdentifier(),
                duration,
                record.getRetryCount(),
                record.getErrorMessage(),
                formatTime(record.getLastExecuteTime()));
    }
    
    /**
     * 记录动作执行超时
     */
    public void logActionTimeout(ActionExecutionRecord record) {
        if (!logEnabled) return;

        long duration = System.currentTimeMillis() - record.getCreateTime();
        logger.warn("[TIMEOUT] ExecutionId={} | RuleId={} | ActionType={} | Target={} | Duration={}ms | RetryCount={} | Time={}",
                record.getExecutionId(),
                record.getRuleId(),
                record.getActionDef().getActionType(),
                record.getTargetIdentifier(),
                duration,
                record.getRetryCount(),
                formatTime(System.currentTimeMillis()));
    }
    
    /**
     * 记录动作重试
     */
    public void logActionRetry(ActionExecutionRecord record) {
        logger.warn("[RETRY] ExecutionId={} | RuleId={} | ActionType={} | Target={} | RetryCount={} | Time={}",
                record.getExecutionId(),
                record.getRuleId(),
                record.getActionDef().getActionType(),
                record.getTargetIdentifier(),
                record.getRetryCount(),
                formatTime(System.currentTimeMillis()));
    }
    
    /**
     * 记录关键动作执行结果
     */
    public void logCriticalActionResult(long ruleId, boolean success, int totalCriticalActions) {
        if (!logEnabled) return;

        if (success) {
            logger.info("[CRITICAL_SUCCESS] RuleId={} | CriticalActionsCount={} | Result=ALL_SUCCESS | Time={}",
                    ruleId, totalCriticalActions, formatCurrentTime());
        } else {
            logger.error("[CRITICAL_FAILED] RuleId={} | CriticalActionsCount={} | Result=HAS_FAILURE | Time={}",
                    ruleId, totalCriticalActions, formatCurrentTime());
        }
    }
    
    /**
     * 记录规则动作执行开始
     */
    public void logRuleActionStart(long ruleId, int totalActions, int criticalActions, int normalActions) {
        if (!logEnabled) return;
        
        logger.info("[RULE_START] RuleId={} | TotalActions={} | CriticalActions={} | NormalActions={} | Time={}",
                ruleId, totalActions, criticalActions, normalActions, formatCurrentTime());
    }
    
    /**
     * 记录规则动作执行完成
     */
    public void logRuleActionComplete(long ruleId, int successCount, int failedCount, long totalDuration) {
        if (!logEnabled) return;
        
        logger.info("[RULE_COMPLETE] RuleId={} | SuccessCount={} | FailedCount={} | TotalDuration={}ms | Time={}",
                ruleId, successCount, failedCount, totalDuration, formatCurrentTime());
    }
    
    /**
     * 记录MQTT反馈处理
     */
    public void logMqttFeedback(String seq, boolean success, String executionId) {
        if (!logEnabled) return;
        
        logger.info("[MQTT_FEEDBACK] Seq={} | Result={} | ExecutionId={} | Time={}",
                seq, success, executionId, formatCurrentTime());
    }
    
    /**
     * 记录队列清理
     */
    public void logQueueCleanup(int removedCount, int remainingCount) {
        if (!logEnabled) return;
        
        logger.info("[QUEUE_CLEANUP] RemovedCount={} | RemainingCount={} | Time={}",
                removedCount, remainingCount, formatCurrentTime());
    }
    
    /**
     * 记录重复操作跳过
     */
    public void logDuplicateSkip(ActionExecutionRecord record) {
        if (!logEnabled) return;
        
        logger.info("[DUPLICATE_SKIP] ExecutionId={} | RuleId={} | ActionType={} | Target={} | Time={}",
                record.getExecutionId(),
                record.getRuleId(),
                record.getActionDef().getActionType(),
                record.getTargetIdentifier(),
                formatCurrentTime());
    }
    
    /**
     * 格式化时间戳
     */
    private String formatTime(long timestamp) {
        return LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                java.time.ZoneId.systemDefault()
        ).format(TIME_FORMATTER);
    }
    
    /**
     * 格式化当前时间
     */
    private String formatCurrentTime() {
        return LocalDateTime.now().format(TIME_FORMATTER);
    }
    
    /**
     * 检查日志是否启用
     */
    public boolean isLoggingEnabled() {
        return logEnabled;
    }
}
