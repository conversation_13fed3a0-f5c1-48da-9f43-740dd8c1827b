package com.inxaiot.ruleengine.core.definition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.inxaiot.ruleengine.common.operator.Operators;
import lombok.Getter;
import lombok.Setter;

/**
 * 设备条件定义
 * 定义单个设备的触发条件，包括设备Code、点位、操作符、比较值等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Setter
@Getter
public class DeviceCondition {

    // Getters and Setters
    /**
     * 条件数据来源设备Code (可能与targetDeviceCode不同)
     */
    @JsonProperty("sourceDeviceCode")
    private String sourceDeviceCode;
    
    /**
     * 监控的设备点位ID
     * 如 "illuminance"(照度), "occupancy_status"(占用状态), "temperature"(温度)
     */
    @JsonProperty("pointId")
    private String pointId;
    
    /**
     * 操作符，详见Operators类
     * 支持的操作符：
     * - EQUALS: 等于
     * - NOT_EQUALS: 不等于
     * - GREATER_THAN: 大于
     * - GREATER_THAN_OR_EQUAL: 大于等于
     * - LESS_THAN: 小于
     * - LESS_THAN_OR_EQUAL: 小于等于
     * - CONTAINS: 包含
     * - NOT_CONTAINS: 不包含
     * - BETWEEN: 介于两个值之间
     * - NOT_BETWEEN: 不在两个值之间
     * - STATES_KEEP_SECONDS: 状态持续指定分钟数
     */
    @JsonProperty("operator")
    private String operator;

    /**
     * 目前仅当 operator=STATES_KEEP_SECONDS生效，默认为 EQUALS
     */
    @JsonProperty("subOperator")
    private String subOperator;
    
    /**
     * 比较值
     * 如 "OCCUPIED", 500, true, "ON", "OFF" 等
     * 对于BETWEEN操作符，这是下限值
     */
    @JsonProperty("value")
    private Object value;

    /**
     * 上限值
     * 用于 BETWEEN 和 NOT_BETWEEN 操作符
     */
    @JsonProperty("upperValue")
    private Object upperValue;

    /**
     * 持续时间 (秒单位)
     * 用于 STATES_KEEP_SECONDS 操作符
     */
    @JsonProperty("durationSeconds")
    private long durationSeconds;
    
    /**
     * 条件是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 条件描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 数据类型
     * 用于类型转换和比较
     * 支持：STRING, INTEGER, DOUBLE, BOOLEAN，详见Operators.DataTypes
     */
    @JsonProperty("dataType")
    private String dataType = "STRING";


    // 构造函数
    public DeviceCondition() {
    }

    public DeviceCondition(String sourceDeviceCode, String pointId, String operator, Object value) {
        this.sourceDeviceCode = sourceDeviceCode;
        this.pointId = pointId;
        this.operator = operator;
        this.value = value;
    }

    public DeviceCondition(String sourceDeviceCode, String pointId, String operator, Object value, long durationSeconds) {
        this(sourceDeviceCode, pointId, operator, value);
        this.durationSeconds = durationSeconds;
    }

    /**
     * 检查是否为持续时间类型的操作符
     */
    @JsonIgnore
    public boolean isDurationOperator() {
        return Operators.Duration.STATES_KEEP_SECONDS.equals(operator);
    }

    /**
     * 检查是否为范围类型的操作符
     */
    @JsonIgnore
    public boolean isRangeOperator() {
        return Operators.Range.BETWEEN.equals(operator) ||
               Operators.Range.NOT_BETWEEN.equals(operator);
    }

    /**
     * 检查条件是否有效
     */
    @JsonIgnore
    public boolean isValid() {
        return sourceDeviceCode != null && !sourceDeviceCode.trim().isEmpty() &&
               pointId != null && !pointId.trim().isEmpty() &&
               operator != null && !operator.trim().isEmpty() &&
               (value != null || isDurationOperator());
    }

    @Override
    public String toString() {
        return "DeviceCondition{" +
                "sourceDeviceCode='" + sourceDeviceCode + '\'' +
                ", pointId='" + pointId + '\'' +
                ", operator='" + operator + '\'' +
                ", value=" + value +
                ", durationSeconds=" + durationSeconds +
                ", dataType='" + dataType + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
