package com.inxaiot.ruleengine.core.definition;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.inxaiot.ruleengine.common.operator.Operators;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 规则定义核心数据模型
 * 从业务服务端接收并存储在本地的原子化规则的核心数据结构
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Setter
@Getter
public class RuleDefinition {
    /**
     * 规则唯一ID
     */
    @JsonProperty("ruleId")
    private Long ruleId;
    
    /**
     * 规则名称 (可选, 便于理解)
     */
    @JsonProperty("ruleName")
    private String ruleName;
    /**
     * 规则所属外部业务唯一标识符
     */
    @JsonProperty("bizId")
    private String bizId;
    
    /**
     * 规则分组ID，方便业务批量处理规则
     */
    @JsonProperty("groupId")
    private String groupId;
    
    /**
     * 规则优先级 (Easy Rules支持)
     */
    @JsonProperty("priority")
    private int priority = 1;
    
    /**
     * 规则是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 时间条件列表
     */
    @JsonProperty("timeConditions")
    private List<TimeCondition> timeConditions;

    /**
     * 时间条件和设备条件(组)的逻辑关系
     * AND: 所有时间条件和设备条件(组)都满足
     * OR: 时间条件或者设备条件(组)只要其一满足即可
     */
    @JsonProperty("logic")
    private Operators.Logic logic = Operators.Logic.AND;
    /**
     * 设备触发条件
     */
    @JsonProperty("triggerCondition")
    private TriggerCondition triggerCondition;
    
    /**
     * 动作列表（激活时执行的动作）
     */
    @JsonProperty("actions")
    private List<ActionDefinition> actions;

    /**
     * 失活时执行的动作（仅用于TIME_DRIVEN的RANGE模式）
     */
    @JsonProperty("deactivationActions")
    private List<ActionDefinition> deactivationActions;
    
    /**
     * 规则创建时间
     */
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 规则更新时间
     */
    @JsonProperty("updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 规则描述
     */
    @JsonProperty("description")
    private String description;


    /**
     * 判断规则是否包含时间条件
     */
    @JsonIgnore
    public boolean hasTimeConditions() {
        return this.getTimeConditions() != null && !this.getTimeConditions().isEmpty();
    }

    @JsonIgnore
    public boolean hasDeviceConditions() {
        return this.getTriggerCondition() != null && !this.getTriggerCondition().isEmpty();
    }

    // 构造函数
    public RuleDefinition() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    public RuleDefinition(Long ruleId, String ruleName) {
        this();
        this.ruleId = ruleId;
        this.ruleName = ruleName;
    }


    @Override
    public String toString() {
        return "RuleDefinition{" +
                "ruleId='" + ruleId + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", bizId='" + bizId + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                '}';
    }

}
