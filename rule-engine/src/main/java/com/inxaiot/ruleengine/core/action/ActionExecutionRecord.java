package com.inxaiot.ruleengine.core.action;

import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import lombok.Getter;
import lombok.Setter;
import org.jeasy.rules.api.Facts;

import java.util.HashMap;
import java.util.Map;

/**
 * 动作执行记录
 * 用于跟踪动作执行状态、重试次数、执行上下文等信息
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Setter
@Getter
public class ActionExecutionRecord {

    // Getters and Setters
    private Facts facts;
    private String executionId;           // 执行ID（唯一标识）
    private long ruleId;               // 规则ID
    private ActionDefinition actionDef;   // 动作定义
    private ExecutionStatus status;       // 执行状态
    private long createTime;             // 创建时间
    private long lastExecuteTime;        // 最后执行时间
    private int retryCount;              // 重试次数
    private String errorMessage;         // 错误信息
    
    // 使用Map存储执行上下文信息，适用于不同类型的动作
    private Map<String, Object> executionContext = new HashMap<>();
    
    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        PENDING,     // 待执行
        EXECUTING,   // 执行中
        SUCCESS,     // 执行成功
        FAILED,      // 执行失败
        TIMEOUT      // 执行超时
    }
    
    /**
     * 执行上下文常量定义
     * 用于在executionContext Map中存储不同类型动作的相关信息
     */
    public static class ContextKeys {
        // 设备控制相关
        public static final String DEVICE_CODE = "deviceCode";
        public static final String POINT_ID = "pointId";
        public static final String MQTT_TOPIC = "mqttTopic";
        public static final String MQTT_SEQ = "mqttSeq";
        
        // API调用相关
        public static final String API_URL = "apiUrl";
        public static final String API_METHOD = "apiMethod";
        public static final String API_RESPONSE_CODE = "apiResponseCode";
        
        // 消息发送相关
        public static final String MESSAGE_TYPE = "messageType";
        public static final String MESSAGE_RECEIVER = "messageReceiver";
        
        // 通用
        public static final String TARGET_IDENTIFIER = "targetIdentifier"; // 目标标识符（设备码、URL等）
        public static final String OPERATION_TYPE = "operationType";       // 操作类型
    }
    
    // 构造函数
    public ActionExecutionRecord() {
        this.createTime = System.currentTimeMillis();
        this.status = ExecutionStatus.PENDING;
        this.retryCount = 0;
    }
    
    public ActionExecutionRecord(String executionId, long ruleId, ActionDefinition actionDef) {
        this();
        this.executionId = executionId;
        this.ruleId = ruleId;
        this.actionDef = actionDef;
    }
    
    // 便捷方法
    public void putContext(String key, Object value) {
        this.executionContext.put(key, value);
    }
    
    public Object getContext(String key) {
        return this.executionContext.get(key);
    }
    
    public String getStringContext(String key) {
        Object value = this.executionContext.get(key);
        return value != null ? value.toString() : null;
    }
    
    public boolean hasContext(String key) {
        return this.executionContext.containsKey(key);
    }
    
    /**
     * 获取目标标识符（设备码、URL等）
     */
    public String getTargetIdentifier() {
        return getStringContext(ContextKeys.TARGET_IDENTIFIER);
    }
    
    /**
     * 设置目标标识符
     */
    public void setTargetIdentifier(String targetIdentifier) {
        putContext(ContextKeys.TARGET_IDENTIFIER, targetIdentifier);
    }
    
    /**
     * 获取操作类型
     */
    public String getOperationType() {
        return getStringContext(ContextKeys.OPERATION_TYPE);
    }
    
    /**
     * 设置操作类型
     */
    public void setOperationType(String operationType) {
        putContext(ContextKeys.OPERATION_TYPE, operationType);
    }

    @Override
    public String toString() {
        return "ActionExecutionRecord{" +
                "executionId='" + executionId + '\'' +
                ", ruleId='" + ruleId + '\'' +
                ", actionType='" + (actionDef != null ? actionDef.getActionType() : "null") + '\'' +
                ", status=" + status +
                ", retryCount=" + retryCount +
                ", targetIdentifier='" + getTargetIdentifier() + '\'' +
                ", createTime=" + createTime +
                ", lastExecuteTime=" + lastExecuteTime +
                '}';
    }
}
