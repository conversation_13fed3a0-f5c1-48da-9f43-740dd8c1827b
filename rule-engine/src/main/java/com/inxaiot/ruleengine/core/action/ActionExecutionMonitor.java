package com.inxaiot.ruleengine.core.action;

import com.inxaiot.ruleengine.RuleEngineConfig;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Collection;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 动作执行监控器
 * 负责处理超时检查、重试机制和队列清理
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Component
public class ActionExecutionMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(ActionExecutionMonitor.class);
    
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2, r -> {
        Thread thread = new Thread(r);
        thread.setName("ActionExecutionMonitor-" + thread.getId());
        thread.setDaemon(true);
        return thread;
    });
    
    @Autowired
    private ActionExecutionCoordinator coordinator;
    
    @Autowired
    private RuleEngineConfig.RuleEngineProperties ruleEngineProperties;
    
    private long executionTimeoutMs;
    private int maxRetryCount;

    @PostConstruct
    public void init() {
        RuleEngineConfig.RuleEngineProperties.ActionExecution config = ruleEngineProperties.getActionExecution();

        this.executionTimeoutMs = config.getTimeoutSeconds() * 1000L;
        this.maxRetryCount = config.getMaxRetryCount();
        int queueCleanupInterval = config.getQueueCleanupInterval();

        // 每秒检查一次超时的执行记录
        scheduler.scheduleAtFixedRate(this::checkTimeoutExecutions, 1, 1, TimeUnit.SECONDS);

        // 定期清理过期记录
        scheduler.scheduleAtFixedRate(this::cleanupExpiredRecords, queueCleanupInterval, queueCleanupInterval, TimeUnit.SECONDS);

        logger.info("ActionExecutionMonitor initialized - timeout: {}ms, maxRetry: {}, cleanupInterval: {}s", executionTimeoutMs, maxRetryCount, queueCleanupInterval);
    }
    
    @PreDestroy
    public void destroy() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("ActionExecutionMonitor shutdown completed");
    }
    
    /**
     * 检查超时的执行记录
     */
    private void checkTimeoutExecutions() {
        try {
            long now = System.currentTimeMillis();
            
            Collection<ActionExecutionRecord> executingRecords = coordinator.getExecutingRecords();
            
            for (ActionExecutionRecord record : executingRecords) {
                if (record.getStatus() == ActionExecutionRecord.ExecutionStatus.EXECUTING) {
                    long elapsed = now - record.getLastExecuteTime();
                    
                    if (elapsed > executionTimeoutMs) {
                        handleTimeoutExecution(record);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("Error checking timeout executions: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理超时的执行记录
     */
    private void handleTimeoutExecution(ActionExecutionRecord record) {
        if (record.getRetryCount() < maxRetryCount) {
            // 重试
            record.setRetryCount(record.getRetryCount() + 1);
            logger.warn("Action execution timeout, retrying: ruleId={}, executionId={}, retryCount={}", record.getRuleId(), record.getExecutionId(), record.getRetryCount());
            coordinator.retryExecution(record);
        } else {
            // 超过最大重试次数，标记为超时失败
            record.setStatus(ActionExecutionRecord.ExecutionStatus.TIMEOUT);
            record.setErrorMessage("Execution timeout after " + maxRetryCount + " retries");
            logger.error("Action execution failed after {} retries: ruleId={}, executionId={}", maxRetryCount, record.getRuleId(),record.getExecutionId());
            
            coordinator.removeFromQueue(record.getExecutionId());
        }
    }
    
    /**
     * 清理过期记录
     */
    private void cleanupExpiredRecords() {
        try {
            coordinator.cleanupExpiredRecords();
        } catch (Exception e) {
            logger.error("Error cleaning up expired records: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取监控统计信息
     */
    public MonitorStats getMonitorStats() {
        return new MonitorStats(
            coordinator.getQueueStatus(),
            coordinator.getExecutionStats(),
            System.currentTimeMillis()
        );
    }
    
    /**
     * 监控统计信息
     */
    @Getter
    public static class MonitorStats {
        private final Object queueStatus;
        private final Object executionStats;
        private final long timestamp;
        
        public MonitorStats(Object queueStatus, Object executionStats, long timestamp) {
            this.queueStatus = queueStatus;
            this.executionStats = executionStats;
            this.timestamp = timestamp;
        }

    }
}
