package com.inxaiot.ruleengine.core.engine;

import com.inxaiot.ruleengine.core.FactKey;
import com.inxaiot.ruleengine.common.context.SystemContextService;
import com.inxaiot.ruleengine.core.action.ActionExecutor;
import com.inxaiot.ruleengine.core.adapter.RuleAdapterService;
import com.inxaiot.ruleengine.core.analyzer.DependencyAnalyzer;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.device.state.StateCondition;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.storage.RuleService;
import com.inxaiot.ruleengine.core.analyzer.FactsBuilder;
import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 规则引擎服务实现
 * 此类负责编排规则的加载和执行
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class RuleEngineServiceImpl implements RuleEngineService {

    private static final Logger logger = LoggerFactory.getLogger(RuleEngineServiceImpl.class);

    private final DefaultRulesEngine easyRulesEngine;
    private final RuleService ruleService;
    private final RuleAdapterService ruleAdapterService;
    private final SystemContextService systemContextService;
    private final ThreadPoolTaskExecutor ruleEvaluationExecutor;
    private final ActionExecutor actionExecutor;
    private final DependencyAnalyzer dependencyAnalyzer;
    private final FactsBuilder factsBuilder;
    private final StateManager stateManager;

    @Autowired
    public RuleEngineServiceImpl(RuleService ruleService,
                                 RuleAdapterService ruleAdapterService,
                                 SystemContextService systemContextService,
                                 @Qualifier("ruleEvaluationExecutor") ThreadPoolTaskExecutor ruleEvaluationExecutor,
                                 ActionExecutor actionExecutor,
                                 DependencyAnalyzer dependencyAnalyzer,
                                 FactsBuilder factsBuilder,
                                 StateManager stateManager) {
        this.easyRulesEngine = new DefaultRulesEngine();
        // 配置规则引擎参数
        // this.easyRulesEngine.setSkipOnFirstAppliedRule(false); // 根据需要设置是否触发所有适用规则
        // this.easyRulesEngine.setRulePriorityThreshold(Integer.MAX_VALUE); // 根据需要设置优先级阈值

        this.ruleService = ruleService;
        this.ruleAdapterService = ruleAdapterService;
        this.systemContextService = systemContextService;
        this.ruleEvaluationExecutor = ruleEvaluationExecutor;
        this.actionExecutor = actionExecutor;
        this.dependencyAnalyzer = dependencyAnalyzer;
        this.factsBuilder = factsBuilder;
        this.stateManager = stateManager;

        logger.info("RuleEngineService initialized with enhanced multi-condition support");
    }

    /**
     * 监听设备状态变化事件
     * 使用事件驱动架构解耦StateManager和RuleEngineService
     * 根据事件类型分发到不同的处理方法
     */
    @EventListener
    @Async("ruleEvaluationExecutor")
    public void handleStateChangeEvent(StateChangeEvent event) {
        logger.debug("Received StateChangeEvent: {} - {}", event.getEventType(), event);

        switch (event.getEventType()) {
            case VALUE_CHANGED:
            case STATE_UPDATED:
                // 处理设备状态变化事件
                processDeviceEvent(event.getDeviceCode(), event.getPointId(), event.getCurrentValue());
                break;

            case CONDITION_TIMEOUT:
                // 处理设备超时事件
                triggerRulesForDeviceTimeout(event.getConditionId(),event.getDeviceCode(), event.getPointId());
                break;

            case CONDITION_MET:
            case CONDITION_RESET:
                // 可以根据需要添加其他事件类型的处理
                logger.debug("Received condition event: {} for {}.{}",
                           event.getEventType(), event.getDeviceCode(), event.getPointId());
                break;

            default:
                logger.debug("Unhandled event type: {} for {}.{}",
                           event.getEventType(), event.getDeviceCode(), event.getPointId());
                break;
        }
    }

    //接口1入口===============================================================

    /**
     * 处理设备数据更新，模拟mqtt上报状态
     */
    public void processDeviceValue(String deviceCode, String pointId, Object value) {
        // 更新设备状态，会触发设备检查
        stateManager.processDevicePointUpdate(deviceCode, pointId, value);
    }

    private void processDeviceEvent(String deviceCode, String pointId, Object value) {
        // 异步提交规则评估任务，立即返回，不阻塞事件源
        ruleEvaluationExecutor.execute(() -> {
            processDeviceEventInternal(deviceCode, pointId, value);
        });
    }

    /**
     * 内部处理设备事件的方法（增强版：支持多条件规则）
     */
    private void processDeviceEventInternal(String deviceCode, String pointId, Object value) {
        try {
            // 1. 查找与此设备相关的规则
            List<RuleDefinition> relevantRules = ruleService.findAllEnabledRulesRelevantToDevice(deviceCode)
                    .stream()
                    .filter(RuleDefinition::hasDeviceConditions)
                    .filter(rule -> dependencyAnalyzer.isRuleRelatedToDevice(rule, deviceCode, pointId))
                    .collect(java.util.stream.Collectors.toList());

            if (relevantRules.isEmpty()) {
                logger.trace("No relevant rules found for device event: {}.{}", deviceCode, pointId);
                return;
            }

            logger.debug("Found {} relevant rules for device event: {}.{}", relevantRules.size(), deviceCode, pointId);

            // 2. 为每个规则构建完整的Facts并评估
            for (RuleDefinition rule : relevantRules) {
                try {
                    //使用增强的Facts构建器，聚合规则所需要的所有设备状态数据
                    Facts facts = factsBuilder.buildCompleteFactsForRule(rule, deviceCode, pointId, value);
                    // 添加事件类型标识
                    facts.put(FactKey.TRIGGERED_EVENT_TYPE, FactKey.TRIGGERED_EVENT_TYPE_DEVICE);
                    facts.put(FactKey.TRIGGERED_TIME, System.currentTimeMillis());
                    //逐个评估单个规则
                    evaluateSingleRule(rule, facts);

                } catch (Exception e) {
                    logger.error("Error processing rule {} for device event {}.{}: {}", rule.getRuleId(), deviceCode, pointId, e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            logger.error("Error processing device event {}.{}: {}", deviceCode, pointId, e.getMessage(), e);
        }
    }

    /**
     * 评估单个规则
     */
    private void evaluateSingleRule(RuleDefinition ruleDefinition, Facts facts) {
        try {
            Rule easyRule = ruleAdapterService.adapt(ruleDefinition);
            Rules rules = new Rules();
            rules.register(easyRule);

            logger.debug("Evaluating rule {} with {} facts", ruleDefinition.getRuleId(), facts.asMap().size());
            easyRulesEngine.fire(rules, facts);
        } catch (Exception e) {
            logger.error("Error evaluating rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
        }
    }



    //接口2入口===============================================================

    /**
     * 处理设备超时事件检查，只触发指定包含conditionId的规则，由StateManager状态监控器触发或者其他调用(比直接调用触发下检查)
     * 由DeviceStateManager在确认设置某一状态超时后调用（如15分钟无人）
     */
    @Override
    public void triggerRulesForDeviceTimeout(String conditionId,String deviceCode, String pointId) {
        // 异步提交超时处理任务
        ruleEvaluationExecutor.execute(() -> {
            triggerRulesForDeviceTimeoutInternal(conditionId,deviceCode, pointId);
        });
    }

    /**
     * 内部处理设备超时事件的方法
     */
    private void triggerRulesForDeviceTimeoutInternal(String conditionId,String deviceCode, String pointId) {
        //1. 查找与conditionId相关的规则，正常为一个规则，但不排除可能多个规则里面设定同样的设备点位同样的超时时间
        List<RuleDefinition> relevantRules = ruleService.findAllEnabledRulesRelevantToDevice(deviceCode).stream()
                .filter(RuleDefinition::hasDeviceConditions)
                .filter(rule -> rule.getTriggerCondition().getConditions().stream().anyMatch(condition -> StateCondition.generateConditionId(
                        deviceCode,
                        condition.getPointId(),
                        condition.getOperator(),
                        condition.getValue(),
                        condition.getDurationSeconds()
                ).equals(conditionId)))
                .collect(java.util.stream.Collectors.toList());

        // 2. 为每个规则构建完整的Facts并评估
        for (RuleDefinition rule : relevantRules) {
            try {
                //使用增强的Facts构建器，聚合规则所需要的所有设备状态数据
                Facts facts = factsBuilder.buildCompleteFactsForRule(rule, deviceCode, pointId, null);
                // 添加事件类型标识
                facts.put(FactKey.TRIGGERED_EVENT_TYPE, FactKey.TRIGGERED_EVENT_TYPE_DEVICE);
                facts.put(FactKey.TRIGGERED_TIME, System.currentTimeMillis());

                addGlobalContextToFacts(facts);

                //逐个评估单个规则
                evaluateSingleRule(rule, facts);

            } catch (Exception e) {
                logger.error("Error processing rule {} for device event {}.{}: {}", rule.getRuleId(), deviceCode, pointId, e.getMessage(), e);
            }
        }

    }

    //接口3入口===============================================================

    /**
     * 触发规则激活，比如由时间程序TimeSchedulerService调用触发
     * 注意：如果没有设备条件，调用则会直接激活，相当于时间触发满足了，调用这里触发执行;如果有设备条件，还会评估规则里的设备条件
     *
     * @param eventType 触发类型，目前由时间激活（eventType=FactKey.TRIGGERED_EVENT_TYPE_TIME），后续可能有其他事件激活满足, 字符见 @FactKey
     */
    @Override
    public void triggerRuleActivation(long ruleId,String eventType) {
        ruleEvaluationExecutor.execute(() -> {
            triggerRuleActivationInternal(ruleId,eventType);
        });
    }

    /**
     * 内部处理规则激活的方法
     */
    private void triggerRuleActivationInternal(long ruleId, String eventType) {
        try {
            RuleDefinition definition = ruleService.findRuleById(ruleId);
            if (definition == null || !definition.isEnabled()) {
                logger.debug("Rule not found or disabled: {}", ruleId);
                return;
            }
            //构建与规则相关的所有facts
            Facts facts =  factsBuilder.buildCompleteFactsForRule(definition, null, null, null);
            facts.put(FactKey.TRIGGERED_RULE_ID, ruleId);
            facts.put(FactKey.TRIGGERED_EVENT_TYPE, eventType);
            facts.put(FactKey.TRIGGERED_TIME, System.currentTimeMillis());
            addGlobalContextToFacts(facts);

            Rule easyRule = ruleAdapterService.adapt(definition);
            Rules rules = new Rules();
            rules.register(easyRule);

            logger.info("Triggering rule activation for ruleId: {}", ruleId);
            easyRulesEngine.fire(rules, facts);

        } catch (Exception e) {
            logger.error("Error triggering rule activation for ruleId: {}", ruleId, e);
        }
    }


    //接口3入口===============================================================
    /**
     * 触发规则失活，由时间程序直接调用
     * @param eventType 触发类型，目前由时间激活，后续可能有其他事件激活满足, 字符见ConextKey
     */
    @Override
    public void triggerRuleDeactivation(long ruleId,String eventType) {
        ruleEvaluationExecutor.execute(() -> {
            triggerRuleDeactivationInternal(ruleId,eventType);
        });
    }

    /**
     * 内部处理规则失活的方法
     */
    private void triggerRuleDeactivationInternal(long ruleId, String eventType) {
        try {
            RuleDefinition definition = ruleService.findRuleById(ruleId);
            if (definition == null || !definition.isEnabled()) {
                logger.debug("Rule not found or disabled for deactivation: {}", ruleId);
                return;
            }

            List<ActionDefinition> deactivationActions = definition.getDeactivationActions();
            if (deactivationActions == null || deactivationActions.isEmpty()) {
                logger.debug("No deactivation actions defined for rule: {}", ruleId);
                return;
            }

            Facts facts = new Facts();
            facts.put(FactKey.TRIGGERED_RULE_ID, ruleId);
            facts.put(FactKey.TRIGGERED_EVENT_TYPE, eventType);
            facts.put(FactKey.TRIGGERED_TIME, System.currentTimeMillis());
            addGlobalContextToFacts(facts);

            // 直接执行失活动作，不经过Easy Rules条件判断
            for (ActionDefinition actionDef : deactivationActions) {
                try {
                    actionExecutor.executeAction(actionDef, facts);
                } catch (Exception e) {
                    logger.error("Error executing deactivation action for rule {}: {}", ruleId, e.getMessage(), e);
                }
            }

            logger.info("Rule deactivation completed for ruleId: {}, executed {} actions",
                       ruleId, deactivationActions.size());

        } catch (Exception e) {
            logger.error("Error triggering rule deactivation for ruleId: {}", ruleId, e);
        }
    }

    /**
     * 添加全局上下文到事实中
     */
    private void addGlobalContextToFacts(Facts facts) {
        try {
            // 从SystemContextService获取所有上下文信息
            Map<String, Object> context = systemContextService.getAllContext();

            // 将上下文信息添加到Facts中
            context.forEach(facts::put);

            logger.trace("Added global context to facts: {}", context.keySet());

        } catch (Exception e) {
            logger.error("Error adding global context to facts", e);

            // 如果SystemContextService出错，添加基础信息作为fallback
            facts.put("currentTime", System.currentTimeMillis());
            facts.put("engineId", "rule-engine-fallback");
        }
    }

    /**
     * 获取规则引擎统计信息
     */
    public RuleEngineStatistics getStatistics() {
        RuleEngineStatistics stats = new RuleEngineStatistics();
        stats.setTotalRules(ruleService.countAllRules());
        stats.setEnabledRules(ruleService.countEnabledRules());
        // 可以添加更多统计信息
        return stats;
    }

    /**
     * 规则引擎统计信息
     */
    public static class RuleEngineStatistics {
        private int totalRules;
        private int enabledRules;
        private long lastRefreshTime;

        // Getters and Setters
        public int getTotalRules() { return totalRules; }
        public void setTotalRules(int totalRules) { this.totalRules = totalRules; }

        public int getEnabledRules() { return enabledRules; }
        public void setEnabledRules(int enabledRules) { this.enabledRules = enabledRules; }

        public long getLastRefreshTime() { return lastRefreshTime; }
        public void setLastRefreshTime(long lastRefreshTime) { this.lastRefreshTime = lastRefreshTime; }

        @Override
        public String toString() {
            return "RuleEngineStatistics{" +
                    "totalRules=" + totalRules +
                    ", enabledRules=" + enabledRules +
                    ", lastRefreshTime=" + lastRefreshTime +
                    '}';
        }
    }
}
