package com.inxaiot.ruleengine.core.action;

import com.alibaba.fastjson.JSONObject;
import com.inxaiot.ruleengine.common.IDGenerator;
import com.inxaiot.ruleengine.core.FactKey;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.transport.mqtt.MessageSender;
import lombok.Getter;
import lombok.Setter;
import org.jeasy.rules.api.Facts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 动作执行器
 * 执行规则定义的各种动作，包括设备控制、消息发送、API调用等
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class ActionExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ActionExecutor.class);

    private final RestTemplate restTemplate = new RestTemplate();

    // 操作记忆缓存：防止重复执行相同操作
    // Key: ruleId_deviceCode_pointId, Value: LastOperationRecord
    private final Map<String, LastOperationRecord> operationMemory = new ConcurrentHashMap<>();

    // 重复操作检查时间窗口：30分钟内相同操作视为重复
    private static final long DUPLICATE_CHECK_WINDOW_MS = 30 * 60 * 1000; // 30分钟

    // 定期清理过期记录的调度器
    private final ScheduledExecutorService cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r);
        thread.setName("ActionMemoryCleanup");
        thread.setDaemon(true);
        return thread;
    });

    @Autowired
    @Qualifier("actionExecutionExecutor")
    private ThreadPoolTaskExecutor actionExecutor;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private ActionExecutionLogger executionLogger;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 初始化方法：启动定期清理任务
     */
    @javax.annotation.PostConstruct
    public void init() {
        // 每小时清理一次过期的操作记录
        cleanupScheduler.scheduleAtFixedRate(this::cleanupExpiredRecords, 1, 1, TimeUnit.HOURS);
        logger.info("ActionExecutor initialized with memory cleanup scheduler");
    }

    /**
     * 销毁方法：关闭清理调度器
     */
    @javax.annotation.PreDestroy
    public void destroy() {
        cleanupScheduler.shutdown();
        logger.info("ActionExecutor cleanup scheduler shutdown");
    }

    /**
     * 执行动作
     */
    public void executeAction(ActionDefinition actionDef, Facts facts) {
        if (actionDef == null || !actionDef.isEnabled()) {
            logger.debug("Action is null or disabled, skipping execution");
            return;
        }
        logger.info("Executing action: {}, facts: {}", actionDef, facts.toString());

        try {
            // 使用配置的动作执行线程池，避免阻塞规则评估线程
            actionExecutor.execute(() -> {executeActionInternal(actionDef, facts);});

        } catch (Exception e) {
            logger.error("Error submitting action execution: {}, facts: {}", actionDef,facts.toString(), e);
        }
    }

    /**
     * 执行单个动作并返回执行记录
     */
    public ActionExecutionRecord executeActionWithRecord(ActionExecutionRecord record) {
        record.setStatus(ActionExecutionRecord.ExecutionStatus.EXECUTING);
        record.setLastExecuteTime(System.currentTimeMillis());

        try {
            // 根据动作类型执行
            switch (record.getActionDef().getActionType()) {
                case ActionDefinition.ActionTypes.DEVICE_CONTROL:
                    executeDeviceControlWithRecord(record);
                    break;
                case ActionDefinition.ActionTypes.SEND_MESSAGE:
                    executeSendMessageWithRecord(record);
                    break;
                case ActionDefinition.ActionTypes.CALL_API:
                    executeCallApiWithRecord(record);
                    break;
                case ActionDefinition.ActionTypes.LOG_EVENT:
                    executeLogEventWithRecord(record);
                    break;
                default:
                    record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
                    record.setErrorMessage("Unsupported action type: " + record.getActionDef().getActionType());
            }

        } catch (Exception e) {
            record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            logger.error("Error executing action {}: {}", record.getExecutionId(), e.getMessage(), e);
        }

        return record;
    }

    /**
     * 检查是否应该跳过重复操作
     * 基于规则ID + 设备点位 + 操作内容 + 同一天的去重逻辑
     */
    private boolean shouldSkipDuplicateOperation(long ruleId, String targetDeviceCode, String pointId, ActionDefinition actionDef) {
        if (targetDeviceCode == null) {
            return false; // 无法判断，不跳过
        }

        // 生成操作键：规则ID + 设备Code + 点位ID
        String operationKey = getOperationKey(ruleId, targetDeviceCode, pointId);

        // 获取上次操作记录
        LastOperationRecord lastRecord = operationMemory.get(operationKey);
        if (lastRecord == null) {
            return false; // 没有历史记录，不跳过
        }

        long now = System.currentTimeMillis();

        // 检查是否在时间窗口内、同一操作类型、同一操作内容
        boolean withinTimeWindow = (now - lastRecord.getTimestamp()) <= DUPLICATE_CHECK_WINDOW_MS;
        boolean isSameActionType = Objects.equals(actionDef.getActionType(), lastRecord.getActionType());

        //移除执行过程记录的参数，这样才好对比是不是重复参数了
        Map<String, Object> lastParams = new HashMap<>();
        for(String key : lastRecord.getParams().keySet()) {
            lastParams.put(key, lastRecord.getParams().get(key));
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>)lastParams.get(ActionDefinition.DEVICE_KEY_DATA);
        data.remove(ActionDefinition.DEVICE_KEY_SEQ);
        data.remove(ActionDefinition.DEVICE_KEY_OPERATE);
        boolean isSameParams = Objects.equals(actionDef.getParams(), lastParams);

        if (withinTimeWindow && isSameActionType && isSameParams) {
            long minutesAgo = (now - lastRecord.getTimestamp()) / (60 * 1000);
            logger.info("Skipping duplicate operation ({}min ago): rule={}, device={}, point={}, action={}", minutesAgo, ruleId, targetDeviceCode, pointId, actionDef.getActionType());
            return true;
        }

        return false;
    }

    /**
     * 检查是否应该跳过重复操作（带记录版本）
     */
    private boolean shouldSkipDuplicateOperationWithRecord(ActionExecutionRecord record) {
        String deviceCode = record.getStringContext(ActionExecutionRecord.ContextKeys.DEVICE_CODE);
        String pointId = record.getStringContext(ActionExecutionRecord.ContextKeys.POINT_ID);

        if (deviceCode == null) {
            // 尝试从动作定义中获取
            @SuppressWarnings("unchecked")
            Map<String, String> data = (Map<String, String>) record.getActionDef().getParams().get(ActionDefinition.DEVICE_KEY_DATA);
            if (data != null) {
                deviceCode = data.get(ActionDefinition.DEVICE_KEY_CODE);
                pointId = data.get(ActionDefinition.DEVICE_KEY_POINT);
            }
        }

        if (deviceCode == null) {
            return false; // 无法判断，不跳过
        }

        return shouldSkipDuplicateOperation(record.getRuleId(), deviceCode, pointId, record.getActionDef());
    }

    /**
     * 生成操作键，用于检查是否重复操作过了
     * @param ruleId
     * @param targetDeviceCode
     * @param pointId
     * @return
     */
    private String getOperationKey(long ruleId, String targetDeviceCode, String pointId) {
        return ruleId + "." + targetDeviceCode + "." + (pointId != null ? pointId : "default");
    }

    /**
     * 记录操作执行
     */
    private void recordOperation(long ruleId, String targetDeviceCode, String pointId, ActionDefinition actionDef) {
        if (targetDeviceCode == null) {
            return;
        }

        String operationKey = getOperationKey(ruleId, targetDeviceCode, pointId);

        LastOperationRecord record = new LastOperationRecord();
        record.setRuleId(ruleId);
        record.setDeviceCode(targetDeviceCode);
        record.setPointId(pointId);
        record.setActionType(actionDef.getActionType());
        record.setParams(actionDef.getParams());
        record.setExecuteDate(LocalDate.now());
        record.setTimestamp(System.currentTimeMillis());

        operationMemory.put(operationKey, record);

        logger.debug("Recorded operation: rule={}, device={}, point={}, action={}", ruleId, targetDeviceCode, pointId, actionDef.getActionType());
    }

    /**
     * 内部执行动作的方法
     */
    private void executeActionInternal(ActionDefinition actionDef, Facts facts) {
        try {

            // 执行具体动作
            switch (actionDef.getActionType()) {
                case ActionDefinition.ActionTypes.DEVICE_CONTROL:
                    executeDeviceControl(actionDef, facts);
                    break;

                case ActionDefinition.ActionTypes.SEND_MESSAGE:
                    executeSendMessage(actionDef, facts);
                    break;

                case ActionDefinition.ActionTypes.CALL_API:
                    executeCallApi(actionDef, facts);
                    break;

                case ActionDefinition.ActionTypes.LOG_EVENT:
                    executeLogEvent(actionDef, facts);
                    break;

                default:
                    logger.warn("Unsupported for action: {},facts:{}", actionDef, facts);
                    return; // 不支持的动作类型，不记录
            }

        } catch (Exception e) {
            logger.error("Error executing action {} ,facts {}: {}", actionDef,facts, e.getMessage(), e);
        }
    }

    /**
     * 清理过期的操作记录
     * 定期清理超过时间窗口的记录，防止内存泄漏
     */
    private void cleanupExpiredRecords() {
        try {
            long now = System.currentTimeMillis();
            int removedCount = 0;

            Iterator<Map.Entry<String, LastOperationRecord>> iterator = operationMemory.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, LastOperationRecord> entry = iterator.next();
                LastOperationRecord record = entry.getValue();

                // 如果记录超过时间窗口，则删除
                if ((now - record.getTimestamp()) > DUPLICATE_CHECK_WINDOW_MS) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                logger.debug("Cleaned up {} expired operation records, remaining: {}", removedCount, operationMemory.size());
            }

        } catch (Exception e) {
            logger.error("Error during operation memory cleanup: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行设备控制动作（带记录）
     */
    private void executeDeviceControlWithRecord(ActionExecutionRecord record) {
        ActionDefinition actionDef = record.getActionDef();
        //TODO 　后续边缘设备管理实现后，在这判断设备实际状态，如果已经达到目标状态，则不执行

        // 检查重复操作(重试的不算重复操作)
        if (record.getRetryCount()==0 && shouldSkipDuplicateOperationWithRecord(record)) {
            record.setStatus(ActionExecutionRecord.ExecutionStatus.SUCCESS);
            record.setErrorMessage("Skipped duplicate operation");
            executionLogger.logDuplicateSkip(record);
            return;
        }
        logger.info("Executing device control with Facts: {}", JSONObject.toJSONString(record.getFacts()));
        try {
            // 构建MQTT消息
            String topic = actionDef.getStringParam(ActionDefinition.DEVICE_KEY_MQTT_TOPIC);
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) actionDef.getParams().get(ActionDefinition.DEVICE_KEY_DATA);

            if (topic == null || data == null) {
                record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
                record.setErrorMessage("Missing topic or data in action definition");
                return;
            }

            String deviceCode = (String) data.get(ActionDefinition.DEVICE_KEY_CODE);
            String pointId = (String) data.get(ActionDefinition.DEVICE_KEY_POINT);

            // 生成序列号
            String seq = IDGenerator.generateId() + "";
            data.put(ActionDefinition.DEVICE_KEY_SEQ, seq);
            data.put(ActionDefinition.DEVICE_KEY_OPERATE, ActionDefinition.DEVICE_KEY_OPERATE_WRITE);

            // 更新记录信息
            record.putContext(ActionExecutionRecord.ContextKeys.DEVICE_CODE, deviceCode);
            record.putContext(ActionExecutionRecord.ContextKeys.POINT_ID, pointId);
            record.putContext(ActionExecutionRecord.ContextKeys.MQTT_TOPIC, topic);
            record.putContext(ActionExecutionRecord.ContextKeys.MQTT_SEQ, seq);
            record.setTargetIdentifier(deviceCode + ":" + pointId);

            // 注册序列号映射（避免循环依赖，通过ApplicationContext获取）
            try {
                ActionExecutionCoordinator coordinator = applicationContext.getBean(ActionExecutionCoordinator.class);
                coordinator.registerSeqMapping(seq, record.getExecutionId());
            } catch (Exception e) {
                logger.warn("Failed to register seq mapping: {}", e.getMessage());
            }

            // 发送MQTT消息
            messageSender.sendToMqtt(topic, JSONObject.toJSONString(data));

            // 记录操作到内存（防重复）
            recordOperation(record.getRuleId(), deviceCode, pointId, actionDef);

            // 对于MQTT类型的动作，状态设为执行中，等待反馈
            record.setStatus(ActionExecutionRecord.ExecutionStatus.EXECUTING);

            logger.info("Device control action sent: executionId={}, seq={}, device={}, point={}", record.getExecutionId(), seq, deviceCode, pointId);

        } catch (Exception e) {
            record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            throw e;
        }
    }

    /**
     * 执行设备控制动作
     */
    private void executeDeviceControl(ActionDefinition actionDef, Facts facts) {
        // 从Facts中获取规则ID和点位ID（用于去重判断）
        Long ruleId = facts.get(FactKey.TRIGGERED_RULE_ID);
        if (facts.get(FactKey.TRIGGERED_RULE_ID) == null || actionDef.getParams().isEmpty()) {
            logger.warn("Action params is null or Rule ID  found in facts: {} for act: {}, and the action will not be executed.", facts, actionDef);
            return;
        }

        String topic = actionDef.getStringParam(ActionDefinition.DEVICE_KEY_MQTT_TOPIC);
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) actionDef.getParams().get(ActionDefinition.DEVICE_KEY_DATA);
        String deviceCode = (String) data.get(ActionDefinition.DEVICE_KEY_CODE);
        String pointId = (String) data.get(ActionDefinition.DEVICE_KEY_POINT);
        Object value = data.get(ActionDefinition.DEVICE_KEY_VALUE);

        if(deviceCode == null || deviceCode.trim().isEmpty() || pointId == null || pointId.trim().isEmpty()){
            logger.warn("Action not executed. Device code or point id is empty for action: {},ruleId: {}", actionDef,ruleId);
            return;
        }

        // 检查是否应该跳过重复操作
        if (shouldSkipDuplicateOperation(ruleId, deviceCode, pointId, actionDef)) {
            logger.info("Skipping duplicate device control action: topic={},rule={}, device={}, point={}, value={}", ruleId, topic,deviceCode, pointId, value);
            return; // 跳过重复操作
        }
        
        try {

            // 通过MQTT发送控制指令到设备
            data.put(ActionDefinition.DEVICE_KEY_OPERATE, ActionDefinition.DEVICE_KEY_OPERATE_WRITE);
            data.put(ActionDefinition.DEVICE_KEY_SEQ, IDGenerator.generateId()+"");
            messageSender.sendToMqtt(topic, JSONObject.toJSONString(data));
            //记录执行动作，防止多次重复执行
            recordOperation(ruleId, deviceCode, pointId, actionDef);
        } catch (Exception e) {
            logger.error("Failed to execute device control for device {}: {}", deviceCode, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行发送消息动作（带记录）
     */
    private void executeSendMessageWithRecord(ActionExecutionRecord record) {
        ActionDefinition actionDef = record.getActionDef();

        try {
            // 获取消息参数
            String messageType = actionDef.getStringParam(ActionDefinition.TypeParamKeys.MESSAGE_TYPE);
            String messageContent = actionDef.getStringParam(ActionDefinition.TypeParamKeys.MESSAGE_CONTENT);
            String receiver = actionDef.getStringParam(ActionDefinition.TypeParamKeys.MESSAGE_RECEIVER);

            // 更新记录信息
            record.putContext(ActionExecutionRecord.ContextKeys.MESSAGE_TYPE, messageType);
            record.putContext(ActionExecutionRecord.ContextKeys.MESSAGE_RECEIVER, receiver);
            record.setTargetIdentifier(receiver);
            record.setOperationType("SEND_MESSAGE");

            // 模拟发送消息（实际实现根据具体需求）
            logger.info("Sending message: type={}, receiver={}, content={}", messageType, receiver, messageContent);

            // 发送消息的具体实现...
            // messageSender.sendMessage(messageType, receiver, messageContent);

            // 消息发送通常是立即完成的
            record.setStatus(ActionExecutionRecord.ExecutionStatus.SUCCESS);

        } catch (Exception e) {
            record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            logger.error("Error sending message: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行发送消息动作
     */
    private void executeSendMessage(ActionDefinition actionDef, Facts facts) {
        logger.info("Executing send message for action: {}, facts: {}", actionDef,facts);

        try {
            // 模拟发送消息
            //String messageContent = actionDef.getStringParam(ActionDefinition.ParamKeys.MESSAGE_CONTENT);
            //String messageReceiver = actionDef.getStringParam(ActionDefinition.ParamKeys.MESSAGE_RECEIVER);
            //String messageType = actionDef.getStringParam(ActionDefinition.ParamKeys.MESSAGE_TYPE);

            //if (messageContent == null || messageContent.trim().isEmpty()) {
            //     logger.warn("Message content is empty for device: {}", targetDeviceCode);
            //    return;
            //}

            // 替换消息内容中的变量
            //messageContent = replaceMessageVariables(messageContent, targetDeviceCode, facts);
            
            // 这里应该调用消息服务发送消息
            // messageService.sendMessage(messageReceiver, messageContent, messageType);
            
            // 模拟发送消息
            logger.info("Message sent - : TypeParams: {}, Params: {}",actionDef.getEnvParams(),actionDef.getParams());
            
        } catch (Exception e) {
            logger.error("Failed to send message for action {}: {}", actionDef, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行API调用动作（带记录）
     */
    private void executeCallApiWithRecord(ActionExecutionRecord record) {
        ActionDefinition actionDef = record.getActionDef();

        try {
            String apiUrl = (String) actionDef.getEnvParam(ActionDefinition.TypeParamKeys.API_URL);
            String apiMethod = (String) actionDef.getEnvParam(ActionDefinition.TypeParamKeys.API_METHOD);

            if (apiUrl == null || apiMethod == null) {
                record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
                record.setErrorMessage("Missing API URL or method");
                return;
            }

            // 更新记录信息
            record.putContext(ActionExecutionRecord.ContextKeys.API_URL, apiUrl);
            record.putContext(ActionExecutionRecord.ContextKeys.API_METHOD, apiMethod);
            record.setTargetIdentifier(apiUrl);
            record.setOperationType("CALL_API");

            // 构建请求
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");

            HttpEntity<String> entity = new HttpEntity<>(JSONObject.toJSONString(actionDef.getParams()), headers);

            // 执行API调用
            HttpMethod method = HttpMethod.valueOf(apiMethod.toUpperCase());
            ResponseEntity<String> response = restTemplate.exchange(apiUrl, method, entity, String.class);

            // 更新响应信息
            record.putContext(ActionExecutionRecord.ContextKeys.API_RESPONSE_CODE, response.getStatusCodeValue());

            if (response.getStatusCode().is2xxSuccessful()) {
                record.setStatus(ActionExecutionRecord.ExecutionStatus.SUCCESS);
                logger.info("API call successful: {} {} - {}", apiMethod, apiUrl, response.getStatusCodeValue());
            } else {
                record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
                record.setErrorMessage("API call failed with status: " + response.getStatusCodeValue());
            }

        } catch (Exception e) {
            record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            logger.error("Error calling API: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行API调用动作
     */
    private void executeCallApi(ActionDefinition actionDef, Facts facts) {
        logger.info("Executing API call : {}", actionDef);
        
        try {
            String apiUrl = (String)actionDef.getEnvParam(ActionDefinition.TypeParamKeys.API_URL);
            String apiMethod = (String)actionDef.getEnvParam(ActionDefinition.TypeParamKeys.API_METHOD);
            Object apiBody = actionDef.getParam(ActionDefinition.TypeParamKeys.API_BODY);
            Object apiHeaders = actionDef.getParam(ActionDefinition.TypeParamKeys.API_HEADERS);
            
            if (apiUrl == null || apiUrl.trim().isEmpty()) {
                logger.warn("API URL is empty for action: {}", actionDef);
                return;
            }
            
            HttpMethod method = HttpMethod.valueOf(apiMethod != null ? apiMethod.toUpperCase() : "POST");
            
            // 构造请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            if (apiHeaders instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, String> headerMap = (Map<String, String>) apiHeaders;
                headerMap.forEach(headers::set);
            }
            
            // 构造请求体
            HttpEntity<Object> requestEntity = new HttpEntity<>(apiBody, headers);
            
            // 发送HTTP请求
            ResponseEntity<String> response = restTemplate.exchange(apiUrl, method, requestEntity, String.class);
            
            logger.info("API call completed - URL: {}, Method: {}, Status: {}, Response: {}", apiUrl, method, response.getStatusCode(), response.getBody());
            
        } catch (Exception e) {
            logger.error("Failed to call API for action {}: {}", actionDef, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行日志记录动作（带记录）
     */
    private void executeLogEventWithRecord(ActionExecutionRecord record) {
        ActionDefinition actionDef = record.getActionDef();

        try {
            String logLevel = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_LEVEL);
            String logMessage = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_MESSAGE);
            String logCategory = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_CATEGORY);

            // 更新记录信息
            record.setTargetIdentifier(logCategory);
            record.setOperationType("LOG_EVENT");

            // 根据日志级别记录日志
            switch (logLevel != null ? logLevel.toUpperCase() : "INFO") {
                case "ERROR":
                    logger.error("[{}] {}", logCategory, logMessage);
                    break;
                case "WARN":
                    logger.warn("[{}] {}", logCategory, logMessage);
                    break;
                case "DEBUG":
                    logger.debug("[{}] {}", logCategory, logMessage);
                    break;
                case "INFO":
                default:
                    logger.info("[{}] {}", logCategory, logMessage);
                    break;
            }

            // 日志记录通常是立即完成的
            record.setStatus(ActionExecutionRecord.ExecutionStatus.SUCCESS);

        } catch (Exception e) {
            record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            logger.error("Error logging event: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行日志记录动作
     */
    private void executeLogEvent(ActionDefinition actionDef, Facts facts) {
        try {
            String logLevel = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_LEVEL);
            String logMessage = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_MESSAGE);
            String logCategory = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_CATEGORY);
            
            if (logMessage == null || logMessage.trim().isEmpty()) {
                logMessage = "Rule action executed for action: " + actionDef;
            }
            
            // 根据日志级别记录日志
            switch (logLevel != null ? logLevel.toUpperCase() : "INFO") {
                case "ERROR":
                    logger.error("[{}] {}", logCategory, logMessage);
                    break;
                case "WARN":
                    logger.warn("[{}] {}", logCategory, logMessage);
                    break;
                case "DEBUG":
                    logger.debug("[{}] {}", logCategory, logMessage);
                    break;
                case "INFO":
                default:
                    logger.info("[{}] {}", logCategory, logMessage);
                    break;
            }
            
        } catch (Exception e) {
            logger.error("Failed to log event for action {}: {}", actionDef, e.getMessage(), e);
        }
    }

    /**
     * 设备控制指令模型
     */
    public static class DeviceControlCommand {
        private String deviceCode;
        private long timestamp;
        /** 点位 和值 **/
        private Map<String, Object> params = new java.util.HashMap<>();

        // Getters and Setters
        public String getDeviceCode() { return deviceCode; }
        public void setDeviceCode(String deviceCode) { this.deviceCode = deviceCode; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

        public Map<String, Object> getParams() { return params; }
        public void setParams(Map<String, Object> params) { this.params = params; }

        public void addParam(String key, Object value) { this.params.put(key, value); }

        @Override
        public String toString() {
            return "DeviceControlCommand{" +
                    "deviceCode='" + deviceCode + '\'' +
                    ", timestamp=" + timestamp +
                    ", params=" + params +
                    '}';
        }
    }

    /**
     * 最后操作记录
     * 用于防止重复执行相同操作
     */
    @Setter
    @Getter
    public static class LastOperationRecord {
        // Getters and Setters
        private Long ruleId;
        private String deviceCode;
        private String pointId;
        private String actionType;
        private Map<String, Object> params;
        private LocalDate executeDate;
        private long timestamp;

        @Override
        public String toString() {
            return "LastOperationRecord{" +
                    "ruleId='" + ruleId + '\'' +
                    ", deviceCode='" + deviceCode + '\'' +
                    ", pointId='" + pointId + '\'' +
                    ", actionType='" + actionType + '\'' +
                    ", executeDate=" + executeDate +
                    ", timestamp=" + timestamp +
                    '}';
        }
    }
}
