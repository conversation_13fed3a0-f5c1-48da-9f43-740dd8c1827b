package com.inxaiot.ruleengine.core.action;

import com.inxaiot.ruleengine.RuleEngineConfig;
import com.inxaiot.ruleengine.common.IDGenerator;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import org.jeasy.rules.api.Facts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 动作执行协调器
 * 负责优先级控制、状态管理、超时重试等核心功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class ActionExecutionCoordinator {
    
    private static final Logger logger = LoggerFactory.getLogger(ActionExecutionCoordinator.class);
    private static final int CRITICAL_PRIORITY = 1;
    
    // 执行状态队列：Key=executionId, Value=ActionExecutionRecord
    private final Map<String, ActionExecutionRecord> executionQueue = new ConcurrentHashMap<>();
    
    // 序列号到执行ID的映射：用于MQTT反馈匹配
    private final Map<String, String> seqToExecutionIdMap = new ConcurrentHashMap<>();

    // 执行完成通知：Key=executionId, Value=CompletableFuture
    private final Map<String, CompletableFuture<Boolean>> completionFutures = new ConcurrentHashMap<>();
    
    @Autowired
    private RuleEngineConfig.RuleEngineProperties ruleEngineProperties;
    
    @Autowired
    private ActionExecutor actionExecutor;
    
    @Autowired
    private ActionExecutionLogger executionLogger;
    
    @Autowired
    @Qualifier("actionExecutionExecutor")
    private ThreadPoolTaskExecutor actionExecutionExecutor;
    
    private long executionTimeoutMs;
    private int maxRetryCount;
    private long criticalActionTimeoutMs;
    private boolean enablePriorityControl;
    
    @PostConstruct
    public void init() {
        RuleEngineConfig.RuleEngineProperties.ActionExecution config = ruleEngineProperties.getActionExecution();

        this.executionTimeoutMs = config.getTimeoutSeconds() * 1000L;
        this.maxRetryCount = config.getMaxRetryCount();
        this.criticalActionTimeoutMs = config.getCriticalActionTimeout() * 1000L;
        this.enablePriorityControl = config.isEnablePriorityControl();

        logger.info("ActionExecutionCoordinator initialized - timeout: {}ms, maxRetry: {}, priorityControl: {}", executionTimeoutMs, maxRetryCount, enablePriorityControl);
    }
    
    @PreDestroy
    public void destroy() {
        logger.info("ActionExecutionCoordinator shutting down...");
    }
    
    /**
     * 执行动作列表（带优先级控制）
     */
    public void executeActions(long ruleId, List<ActionDefinition> actions, Facts facts) {
        if (actions == null || actions.isEmpty()) {
            return;
        }
        
        // 过滤启用的动作并按优先级排序
        List<ActionDefinition> enabledActions = actions.stream()
                .filter(ActionDefinition::isEnabled)
                .sorted(Comparator.comparingInt(ActionDefinition::getPriority))
                .collect(Collectors.toList());
        
        if (enabledActions.isEmpty()) {
            logger.debug("No enabled actions found for rule {}", ruleId);
            return;
        }
        
        // 创建执行上下文
        ActionExecutionContext context = new ActionExecutionContext(ruleId, facts, enabledActions);
        
        // 分离关键动作和普通动作
        List<ActionDefinition> criticalActions = enabledActions.stream()
                .filter(action -> action.getPriority() == CRITICAL_PRIORITY)
                .collect(Collectors.toList());
                
        List<ActionDefinition> normalActions = enabledActions.stream()
                .filter(action -> action.getPriority() != CRITICAL_PRIORITY)
                .collect(Collectors.toList());
        
        // 记录规则动作执行开始
        executionLogger.logRuleActionStart(ruleId, enabledActions.size(), criticalActions.size(), normalActions.size());
        
        // 异步执行
        actionExecutionExecutor.execute(() -> {
            executeActionsInternal(context, criticalActions, normalActions);
        });
    }
    
    /**
     * 内部执行逻辑
     */
    private void executeActionsInternal(ActionExecutionContext context, List<ActionDefinition> criticalActions, List<ActionDefinition> normalActions) {
        try {

            // 1. 先执行关键动作（priority=1）
            if (!criticalActions.isEmpty() && enablePriorityControl) {
                boolean criticalSuccess = executeCriticalActions(context, criticalActions);
                context.setCriticalActionSuccess(criticalSuccess);

                // 记录关键动作执行结果
                executionLogger.logCriticalActionResult(context.getRuleId(), criticalSuccess, criticalActions.size());
                
                if (!criticalSuccess) {
                    logger.warn("Critical actions failed for rule {}, skipping normal actions", context.getRuleId());
                    return; // 关键动作失败，停止执行
                }
            } else {
                context.setCriticalActionSuccess(true); // 没有关键动作或未启用优先级控制，视为成功
            }
            
            // 2. 执行普通动作（按priority顺序）
            executeNormalActions(context, normalActions);
            
            // 3. 记录规则动作执行完成
            logRuleExecutionComplete(context);
            
        } catch (Exception e) {
            logger.error("Error executing actions for rule {}: {}", context.getRuleId(), e.getMessage(), e);
        }
    }
    
    /**
     * 执行关键动作（priority=1）- 同步等待结果
     */
    private boolean executeCriticalActions(ActionExecutionContext context, List<ActionDefinition> criticalActions) {
        for (ActionDefinition action : criticalActions) {
            ActionExecutionRecord record = createExecutionRecord(context.getRuleId(), action, context.getFacts());
            context.addExecutionRecord(record);
            
            // 同步执行关键动作并等待结果
            boolean success = executeActionAndWait(record);
            if (!success) {
                return false; // 任一关键动作失败，返回失败
            }
        }
        return true;
    }
    
    /**
     * 执行普通动作 - 异步执行
     */
    private void executeNormalActions(ActionExecutionContext context, List<ActionDefinition> normalActions) {
        for (ActionDefinition action : normalActions) {
            ActionExecutionRecord record = createExecutionRecord(context.getRuleId(), action, context.getFacts());
            context.addExecutionRecord(record);
            
            // 异步执行普通动作
            executeActionAsync(record);
        }
    }
    
    /**
     * 创建执行记录
     */
    private ActionExecutionRecord createExecutionRecord(long ruleId, ActionDefinition actionDef, Facts facts) {
        String executionId = ruleId+":"+actionDef.getId()+":"+IDGenerator.getHMS();
        ActionExecutionRecord record = new ActionExecutionRecord(executionId, ruleId, actionDef);
        //记录事件，用于跟踪
        record.setFacts(facts);
        // 设置操作类型
        record.setOperationType(actionDef.getActionType());

        return record;
    }
    
    /**
     * 同步执行动作并等待结果
     */
    private boolean executeActionAndWait(ActionExecutionRecord record) {
        try {
            // 添加到执行队列
            executionQueue.put(record.getExecutionId(), record);
            
            // 记录开始执行
            executionLogger.logActionStart(record);
            
            // 执行动作
            ActionExecutionRecord result = actionExecutor.executeActionWithRecord(record);
            
            // 对于需要MQTT反馈的动作，等待反馈
            if (needsWaitForFeedback(result)) {
                return waitForActionCompletion(result, criticalActionTimeoutMs);
            } else {
                // 立即完成的动作
                handleActionCompletion(result);
                return result.getStatus() == ActionExecutionRecord.ExecutionStatus.SUCCESS;
            }
            
        } catch (Exception e) {
            logger.error("Error executing critical action {}: {}", record.getExecutionId(), e.getMessage(), e);
            record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            executionLogger.logActionFailure(record);
            return false;
        }
    }
    
    /**
     * 异步执行动作
     */
    private void executeActionAsync(ActionExecutionRecord record) {
        try {
            // 添加到执行队列
            executionQueue.put(record.getExecutionId(), record);
            
            // 记录开始执行
            executionLogger.logActionStart(record);
            
            // 异步执行
            actionExecutionExecutor.execute(() -> {
                try {
                    ActionExecutionRecord result = actionExecutor.executeActionWithRecord(record);
                    // 对于不需要等待反馈的动作，立即处理完成
                    if (!needsWaitForFeedback(result)) {
                        handleActionCompletion(result);
                    }
                    // 需要等待反馈的动作由超时监控器和反馈处理器处理
                    
                } catch (Exception e) {
                    logger.error("Error in async action execution {}: {}", record.getExecutionId(), e.getMessage(), e);
                    record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
                    record.setErrorMessage(e.getMessage());
                    handleActionCompletion(record);
                }
            });
            
        } catch (Exception e) {
            logger.error("Error submitting async action {}: {}", record.getExecutionId(), e.getMessage(), e);
        }
    }
    
    /**
     * 判断是否需要等待反馈
     */
    private boolean needsWaitForFeedback(ActionExecutionRecord record) {
        return ActionDefinition.ActionTypes.DEVICE_CONTROL.equals(record.getActionDef().getActionType()) &&
               record.getStatus() == ActionExecutionRecord.ExecutionStatus.EXECUTING;
    }
    
    /**
     * 等待动作完成（用于关键动作）- 使用CompletableFuture避免忙等待
     */
    private boolean waitForActionCompletion(ActionExecutionRecord record, long timeoutMs) {
        String executionId = record.getExecutionId();

        // 创建CompletableFuture用于等待完成通知
        CompletableFuture<Boolean> completionFuture = new CompletableFuture<>();
        completionFutures.put(executionId, completionFuture);

        try {
            // 等待完成或超时
            Boolean result = completionFuture.get(timeoutMs, TimeUnit.MILLISECONDS);
            return result != null && result;

        } catch (TimeoutException e) {
            // 超时处理
            record.setStatus(ActionExecutionRecord.ExecutionStatus.TIMEOUT);
            executionLogger.logActionTimeout(record);
            logger.warn("Rule action execution timeout: ruleId={}, executionId={}, timeout={}ms",record.getRuleId(), executionId, timeoutMs);
            return false;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Rule action execution interrupted: ruleId={},executionId={}", record.getRuleId(), executionId);
            return false;

        } catch (ExecutionException e) {
            logger.error("Rule action  execution error: ruleId={},executionId={}, error={}", record.getRuleId(), executionId, e.getMessage());
            return false;

        } finally {
            // 清理CompletableFuture
            completionFutures.remove(executionId);
        }
    }
    
    /**
     * 处理动作完成
     */
    private void handleActionCompletion(ActionExecutionRecord record) {
        String executionId = record.getExecutionId();
        ActionExecutionRecord.ExecutionStatus status = record.getStatus();

        if (status == ActionExecutionRecord.ExecutionStatus.SUCCESS) {
            executionLogger.logActionSuccess(record);
        } else {
            executionLogger.logActionFailure(record);
        }

        // 通知等待的线程
        CompletableFuture<Boolean> future = completionFutures.get(executionId);
        if (future != null) {
            boolean success = (status == ActionExecutionRecord.ExecutionStatus.SUCCESS);
            future.complete(success);
        }

        // 从队列中移除（成功或最终失败的记录）
        if (status == ActionExecutionRecord.ExecutionStatus.SUCCESS ||
            status == ActionExecutionRecord.ExecutionStatus.FAILED ||
            status == ActionExecutionRecord.ExecutionStatus.TIMEOUT) {

            removeFromQueue(executionId);
        }
    }
    
    /**
     * 记录规则执行完成
     */
    private void logRuleExecutionComplete(ActionExecutionContext context) {
        List<ActionExecutionRecord> records = context.getExecutionRecords();
        int successCount = (int) records.stream().filter(r -> r.getStatus() == ActionExecutionRecord.ExecutionStatus.SUCCESS).count();
        int failedCount = records.size() - successCount;
        
        executionLogger.logRuleActionComplete(context.getRuleId(), successCount, failedCount, context.getElapsedTime());
    }
    
    /**
     * 处理MQTT执行反馈
     */
    public void handleExecutionResponse(String seq, boolean success, String errorMsg) {
        String executionId = seqToExecutionIdMap.get(seq);
        if (executionId == null) {
            logger.debug("No execution record found for seq: {}", seq);
            return;
        }

        ActionExecutionRecord record = executionQueue.get(executionId);
        if (record == null) {
            logger.debug("Execution record not found in queue: {}", executionId);
            return;
        }

        // 记录MQTT反馈
        executionLogger.logMqttFeedback(seq, success, executionId);

        //设备控制，网关返回失败，则不处理指令队列，让队列继续重试
        if(!success && record.getActionDef().isDeviceControlAction()) {
            return;
        }

        // 更新执行状态
        record.setStatus(ActionExecutionRecord.ExecutionStatus.SUCCESS);


        record.setLastExecuteTime(System.currentTimeMillis());

        // 处理完成
        handleActionCompletion(record);

        // 清理序列号映射
        seqToExecutionIdMap.remove(seq);
    }

    /**
     * 处理设备状态更新（后续扩展用）
     */
    public void handleDeviceStatusUpdate(Map<String, Object> deviceAttrMap) {
        // TODO: 后续可以根据设备状态来判断动作是否真正生效
        // 目前先用handleResponse来判断执行成功
    }

    /**
     * 重试执行
     */
    public void retryExecution(ActionExecutionRecord record) {
        if (record.getRetryCount() >= maxRetryCount) {
            record.setStatus(ActionExecutionRecord.ExecutionStatus.TIMEOUT);
            record.setErrorMessage("Max retry count exceeded");
            handleActionCompletion(record);
            return;
        }

        // 记录重试
        executionLogger.logActionRetry(record);

        // 异步重试
        actionExecutionExecutor.execute(() -> {
            try {
                ActionExecutionRecord result = actionExecutor.executeActionWithRecord(record);

                // 对于不需要等待反馈的动作，立即处理完成
                if (!needsWaitForFeedback(result)) {
                    handleActionCompletion(result);
                }

            } catch (Exception e) {
                logger.error("Error in retry execution {}: {}", record.getExecutionId(), e.getMessage(), e);
                record.setStatus(ActionExecutionRecord.ExecutionStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                handleActionCompletion(record);
            }
        });
    }

    /**
     * 从队列中移除记录
     */
    public void removeFromQueue(String executionId) {
        ActionExecutionRecord removed = executionQueue.remove(executionId);
        if (removed != null) {
            // 清理相关的序列号映射
            String seq = removed.getStringContext(ActionExecutionRecord.ContextKeys.MQTT_SEQ);
            if (seq != null) {
                seqToExecutionIdMap.remove(seq);
            }
        }

        // 清理CompletableFuture（如果存在未完成的等待）
        CompletableFuture<Boolean> future = completionFutures.remove(executionId);
        if (future != null && !future.isDone()) {
            future.complete(false); // 标记为失败
        }
    }

    /**
     * 注册序列号映射
     */
    public void registerSeqMapping(String seq, String executionId) {
        seqToExecutionIdMap.put(seq, executionId);
    }

    /**
     * 获取正在执行的记录
     */
    public Collection<ActionExecutionRecord> getExecutingRecords() {
        return executionQueue.values().stream().filter(record -> record.getStatus() == ActionExecutionRecord.ExecutionStatus.EXECUTING).collect(Collectors.toList());
    }

    /**
     * 获取队列状态
     */
    public Map<String, Object> getQueueStatus() {
        Map<String, Object> status = new HashMap<>();

        Map<ActionExecutionRecord.ExecutionStatus, Long> statusCount = executionQueue.values().stream()
                .collect(Collectors.groupingBy(
                    ActionExecutionRecord::getStatus,
                    Collectors.counting()
                ));

        status.put("totalCount", executionQueue.size());
        status.put("statusCount", statusCount);
        status.put("seqMappingCount", seqToExecutionIdMap.size());

        return status;
    }

    /**
     * 获取执行统计信息
     */
    public Map<String, Object> getExecutionStats() {
        Map<String, Object> stats = new HashMap<>();

        Collection<ActionExecutionRecord> records = executionQueue.values();

        // 按动作类型统计
        Map<String, Long> typeCount = records.stream().collect(Collectors.groupingBy(record -> record.getActionDef().getActionType(), Collectors.counting()));

        // 按规则统计
        Map<Long, Long> ruleCount = records.stream().collect(Collectors.groupingBy(ActionExecutionRecord::getRuleId, Collectors.counting()));

        // 重试统计
        Map<Integer, Long> retryCount = records.stream().collect(Collectors.groupingBy(ActionExecutionRecord::getRetryCount, Collectors.counting()));

        stats.put("actionTypeCount", typeCount);
        stats.put("ruleCount", ruleCount);
        stats.put("retryCount", retryCount);
        stats.put("timestamp", System.currentTimeMillis());

        return stats;
    }

    /**
     * 清理过期记录
     */
    public void cleanupExpiredRecords() {
        long now = System.currentTimeMillis();
        long expireTime = now - (executionTimeoutMs * 10); // 超时时间的10倍作为清理阈值

        List<String> expiredIds = executionQueue.values().stream()
                .filter(record -> record.getCreateTime() < expireTime)
                .filter(record -> record.getStatus() == ActionExecutionRecord.ExecutionStatus.SUCCESS ||
                                record.getStatus() == ActionExecutionRecord.ExecutionStatus.FAILED ||
                                record.getStatus() == ActionExecutionRecord.ExecutionStatus.TIMEOUT)
                .map(ActionExecutionRecord::getExecutionId)
                .collect(Collectors.toList());

        int removedCount = 0;
        for (String executionId : expiredIds) {
            removeFromQueue(executionId);
            removedCount++;
        }

        if (removedCount > 0) {
            executionLogger.logQueueCleanup(removedCount, executionQueue.size());
        }
    }
}
