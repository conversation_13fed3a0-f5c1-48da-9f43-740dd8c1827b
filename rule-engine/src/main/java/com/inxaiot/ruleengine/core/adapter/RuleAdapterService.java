package com.inxaiot.ruleengine.core.adapter;



import com.inxaiot.ruleengine.core.FactKey;
import com.inxaiot.ruleengine.core.action.ActionExecutor;
import com.inxaiot.ruleengine.core.action.ActionExecutionCoordinator;
import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.common.operator.ValueComparator;
import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.trigger.time.TimeConditionEvaluator;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.RuleBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 规则适配器服务
 * 将RuleDefinition转换为Easy Rules的Rule对象
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class RuleAdapterService {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleAdapterService.class);

    private final TimeConditionEvaluator timeConditionEvaluator;
    private final ActionExecutor actionExecutor;

    @Autowired
    private ActionExecutionCoordinator actionCoordinator;

    @Autowired
    public RuleAdapterService(TimeConditionEvaluator timeConditionEvaluator, ActionExecutor actionExecutor) {
        this.timeConditionEvaluator = timeConditionEvaluator;
        this.actionExecutor = actionExecutor;
    }

    /**
     * 将RuleDefinition适配为Easy Rules的Rule对象
     */
    public Rule adapt(RuleDefinition ruleDefinition) {
        return new RuleBuilder()
                .name(ruleDefinition.getRuleName()==null?ruleDefinition.getBizId():ruleDefinition.getRuleName())
                .description(ruleDefinition.getDescription())
                .priority(ruleDefinition.getPriority())
                .when(facts -> {
                    if (!ruleDefinition.isEnabled()) {
                        return false;
                    }
                    try {
                        boolean timeMet;
                        // 1. 评估时间条件
                        //1.1 时间驱动触发
                        boolean isTimeTrigger = FactKey.TRIGGERED_EVENT_TYPE_TIME.equals(facts.get(FactKey.TRIGGERED_EVENT_TYPE));
                        if (isTimeTrigger) {
                            // 对于时间驱动的规则，它的触发由TimeSchedulerService发起，说明触发的规则的时间条件已经满足了,facts传递了触发的规则ID
                            timeMet = ruleDefinition.getRuleId().equals(facts.get(FactKey.TRIGGERED_RULE_ID));
                            logger.debug("Time-driven rule: {} triggered by time event, meet result: {}", ruleDefinition.getRuleId(),timeMet);
                        //1.2 评估时间条件
                        } else {
                            timeMet = timeConditionEvaluator.isTimeConditionMet(ruleDefinition.getTimeConditions(), LocalDateTime.now());
                            if (!timeMet) {
                                logger.trace("Event-driven rule {} time condition not met.", ruleDefinition.getRuleId());
                                return false;
                            }
                            logger.trace("Event-driven rule {} time condition met.", ruleDefinition.getRuleId());
                        }

                        Operators.Logic logic = ruleDefinition.getLogic();


                        // 短路判断：OR逻辑下时间满足即可；AND逻辑下时间不满足即失败，否则继续后面设备条件判断
                        if ((logic == Operators.Logic.OR && timeMet) || (logic == Operators.Logic.AND && !timeMet)) {
                            return timeMet; // OR时返回true，AND时返回false
                        }

                        //2. 评估设备条件
                        boolean deviceConditionsMet = evaluateDeviceTriggerConditions(ruleDefinition, facts);
                        if (!deviceConditionsMet) {
                            logger.trace("Event-driven rule {} device conditions not met.", ruleDefinition.getRuleId());
                            return false;
                        }
                        logger.debug("Event-driven rule {} all conditions met.", ruleDefinition.getRuleId());
                        return true;
                        
                    } catch (Exception e) {
                        logger.error("Error evaluating conditions for rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
                        return false;
                    }
                })
                .then(facts -> {
                    try {
                        logger.info("Executing actions for rule {}.", ruleDefinition.getRuleId());

                        // 确保Facts中包含规则ID（用于动作执行去重），有些是设备事件触发的事件，没有包含ruleId
                        if (!facts.asMap().containsKey(FactKey.TRIGGERED_RULE_ID)) {
                            facts.put(FactKey.TRIGGERED_RULE_ID, ruleDefinition.getRuleId());
                        }

                        // 使用新的动作执行协调器
                        if (ruleDefinition.getActions() != null && !ruleDefinition.getActions().isEmpty()) {
                            actionCoordinator.executeActions(ruleDefinition.getRuleId(), ruleDefinition.getActions(), facts);
                        }

                    } catch (Exception e) {
                        logger.error("Error executing actions for rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
                    }
                })
                .build();
    }

    /**
     * 评估设备触发条件
     */
    private boolean evaluateDeviceTriggerConditions(RuleDefinition ruleDefinition, Facts facts) {
        TriggerCondition triggerCondition = ruleDefinition.getTriggerCondition();
        if (triggerCondition == null || triggerCondition.isEmpty()) {
            return true; // 没有设备条件，视为满足
        }

        // 获取触发此规则评估的源头设备Code (如果存在)
        String triggeringDeviceCode = facts.get("triggeringDeviceCode");

        boolean overallResult;
        if (triggerCondition.getLogic() == Operators.Logic.OR) {
            overallResult = false; // 对于OR逻辑，默认false，只要有一个满足即为true
            for (DeviceCondition condition : triggerCondition.getConditions()) {
                if (!condition.isEnabled()) {
                    continue; // 跳过未启用的条件
                }
                
                // 可以添加设备过滤逻辑
                // String sourceDeviceCode = condition.getSourceDeviceCode() != null ? 
                //     condition.getSourceDeviceCode() : ruleDefinition.getTargetDeviceCode();
                // if (triggeringDeviceCode != null && !triggeringDeviceCode.equals(sourceDeviceCode)) {
                //     continue; // 如果条件与触发设备不符，则跳过 (此逻辑需根据实际情况调整)
                // }
                
                if (evaluateSingleCondition(condition, facts)) {
                    overallResult = true;
                    break;
                }
            }
        } else { // ALL (AND) logic
            overallResult = true; // 对于AND逻辑，默认true，只要有一个不满足即为false
            for (DeviceCondition condition : triggerCondition.getConditions()) {
                if (!condition.isEnabled()) {
                    continue; // 跳过未启用的条件
                }
                
                // 同上的设备过滤逻辑
                
                if (!evaluateSingleCondition(condition, facts)) {
                    overallResult = false;
                    break;
                }
            }
        }
        return overallResult;
    }

    /**
     * 评估单个设备条件
     */
    private boolean evaluateSingleCondition(DeviceCondition condition, Facts facts) {
        try {
            // 对于 "STATES_KEEP_SECONDS"，检查设备状态是否持续指定时间
            if (Operators.Duration.STATES_KEEP_SECONDS.equals(condition.getOperator())) {
                return evaluateStateKeepTime(condition, facts);
            }

            // 对于其他常规传感器值比较
            // 从Facts中获取对应点位的值
            // 注意：facts中的key可能需要包含deviceCode，如 "deviceCode.pointId"
            // 或者facts本身就是针对单个设备的快照
            //Object factValue = facts.get(condition.getPointId());
            String factKey = FactKey.getStateKey(condition.getSourceDeviceCode(), condition.getPointId());
            Object factValue = facts.get(factKey);

            if (factValue == null) {
                logger.info("Fact for deviceCode:{}, pointId '{}' not found for condition in rule, evaluate condition false", condition.getSourceDeviceCode(), condition.getPointId());
                return false; // 点位数据不存在，条件不满足
            }

            // 使用统一的值比较器
            return ValueComparator.compare(factValue, condition.getOperator(), condition.getValue(), condition.getUpperValue(), condition.getDataType());
            
        } catch (Exception e) {
            logger.error("Error evaluating condition for pointId '{}': {}", condition.getPointId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 转换为double类型
     */
    private double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return Double.parseDouble(String.valueOf(value));
    }

    /**
     * 评估状态持续时间条件
     * 通用的状态持续时间检测，支持各种设备状态
     */
    private boolean evaluateStateKeepTime(DeviceCondition condition, Facts facts) {
        try {

            String stateKey = FactKey.getStateKey(condition.getSourceDeviceCode(), condition.getPointId());
            // 构造状态持续时间事实的键名
            String stateKeepFactKey = FactKey.getStateKeepTimeFactKey(condition.getSourceDeviceCode(), condition.getPointId());

            // 从Facts中获取状态持续时间和值信息
            Object stateValue = facts.get(stateKey);
            Object durationSeconds = facts.get(stateKeepFactKey);

            if (durationSeconds == null || stateValue == null) {
                logger.debug("State keep fact stateKey: '{}' or stateKeepFactKey:'{}' not found", stateKey,stateKeepFactKey);
                return false;
            }

            // 检查状态值是否匹配
            boolean stateMatches = checkStateMatches(condition, stateValue);

            // 检查持续时长是否满足
            boolean durationMatches = checkDurationMatches(condition, durationSeconds);

            logger.debug("State keep evaluation: state={}, expectedState={}, stateMatches={}, duration={}, expectedDuration={}, durationMatches={}", stateValue, condition.getValue(), stateMatches, durationSeconds, condition.getDurationSeconds(), durationMatches);

            return stateMatches && durationMatches;

        } catch (Exception e) {
            logger.error("Error evaluating state keep time condition for device {} point {}: {}", condition.getSourceDeviceCode(), condition.getPointId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查状态值是否匹配
     */
    private boolean checkStateMatches(DeviceCondition condition, Object currentState) {
        // 使用统一的值比较器进行状态匹配（默认使用EQUALS操作符）
        return ValueComparator.compare(currentState, Operators.Basic.EQUALS, condition.getValue(), condition.getDataType());
    }

    /**
     * 检查持续时间是否满足
     */
    private boolean checkDurationMatches(DeviceCondition condition, Object durationSeconds) {
        if (durationSeconds == null) {
            return false;
        }

        try {
            double actualDuration = convertToDouble(durationSeconds);
            double expectedDuration = condition.getDurationSeconds();

            return actualDuration >= expectedDuration;
        } catch (Exception e) {
            logger.error("Error comparing duration: actual={}, expected={}", durationSeconds, condition.getDurationSeconds(), e);
            return false;
        }
    }


}
