package com.inxaiot.ruleengine.core.definition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.inxaiot.ruleengine.common.operator.Operators;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 设备触发条件定义
 * 包含多个设备条件的组合逻辑
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Setter
@Getter
public class TriggerCondition {

    // Getters and Setters
    /**
     * 条件匹配逻辑，默认为ALL(AND)
     */
    @JsonProperty("logic")
    private Operators.Logic logic = Operators.Logic.AND;
    
    /**
     * 设备条件列表
     */
    @JsonProperty("conditions")
    private List<DeviceCondition> conditions;
    
    /**
     * 触发条件是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 触发条件描述
     */
    @JsonProperty("description")
    private String description;

    // 构造函数
    public TriggerCondition() {
    }

    public TriggerCondition(Operators.Logic logic, List<DeviceCondition> conditions) {
        this.logic = logic;
        this.conditions = conditions;
    }

    /**
     * 检查触发条件是否为空
     */
    @JsonIgnore
    public boolean isEmpty() {
        return conditions == null || conditions.isEmpty();
    }

    /**
     * 获取条件数量
     */
    @JsonIgnore
    public int getConditionCount() {
        return conditions == null ? 0 : conditions.size();
    }

    /**
     * 添加设备条件
     */
    public void addCondition(DeviceCondition condition) {
        if (conditions == null) {
            conditions = new java.util.ArrayList<>();
        }
        conditions.add(condition);
    }

    /**
     * 移除设备条件
     */
    public boolean removeCondition(DeviceCondition condition) {
        return conditions != null && conditions.remove(condition);
    }

    /**
     * 根据索引移除设备条件
     */
    public DeviceCondition removeCondition(int index) {
        if (conditions != null && index >= 0 && index < conditions.size()) {
            return conditions.remove(index);
        }
        return null;
    }

    /**
     * 清空所有条件
     */
    public void clearConditions() {
        if (conditions != null) {
            conditions.clear();
        }
    }

    @Override
    public String toString() {
        return "TriggerConditions{" +
                "logic=" + logic +
                ", conditions=" + conditions +
                ", enabled=" + enabled +
                ", conditionCount=" + getConditionCount() +
                '}';
    }
}
