package com.inxaiot.ruleengine.core.action;

import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import lombok.Getter;
import lombok.Setter;
import org.jeasy.rules.api.Facts;

import java.util.ArrayList;
import java.util.List;

/**
 * 动作执行上下文
 * 用于在动作执行过程中传递相关信息和状态
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Getter
public class ActionExecutionContext {

    // Getters and Setters
    @Setter
    private long ruleId;                                    // 规则ID
    @Setter
    private Facts facts;                                      // 规则事实
    private List<ActionDefinition> actions;                   // 动作列表
    private List<ActionExecutionRecord> executionRecords;     // 执行记录列表
    @Setter
    private boolean criticalActionSuccess = false;           // priority=1的动作是否成功
    private long startTime;                                   // 开始执行时间
    
    public ActionExecutionContext() {
        this.actions = new ArrayList<>();
        this.executionRecords = new ArrayList<>();
        this.startTime = System.currentTimeMillis();
    }
    
    public ActionExecutionContext(long ruleId, Facts facts, List<ActionDefinition> actions) {
        this();
        this.ruleId = ruleId;
        this.facts = facts;
        this.actions = actions != null ? new ArrayList<>(actions) : new ArrayList<>();
    }
    
    /**
     * 添加执行记录
     */
    public void addExecutionRecord(ActionExecutionRecord record) {
        this.executionRecords.add(record);
    }
    
    /**
     * 获取指定状态的执行记录
     */
    public List<ActionExecutionRecord> getRecordsByStatus(ActionExecutionRecord.ExecutionStatus status) {
        return executionRecords.stream()
                .filter(record -> record.getStatus() == status)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取关键动作记录（priority=1）
     */
    public List<ActionExecutionRecord> getCriticalActionRecords() {
        return executionRecords.stream()
                .filter(record -> record.getActionDef().getPriority() == 1)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取普通动作记录（priority!=1）
     */
    public List<ActionExecutionRecord> getNormalActionRecords() {
        return executionRecords.stream()
                .filter(record -> record.getActionDef().getPriority() != 1)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 检查是否有失败的关键动作
     */
    public boolean hasCriticalActionFailed() {
        return getCriticalActionRecords().stream()
                .anyMatch(record -> record.getStatus() == ActionExecutionRecord.ExecutionStatus.FAILED ||
                                  record.getStatus() == ActionExecutionRecord.ExecutionStatus.TIMEOUT);
    }
    
    /**
     * 检查所有关键动作是否都成功
     */
    public boolean areAllCriticalActionsSuccessful() {
        List<ActionExecutionRecord> criticalRecords = getCriticalActionRecords();
        if (criticalRecords.isEmpty()) {
            return true; // 没有关键动作，视为成功
        }
        
        return criticalRecords.stream()
                .allMatch(record -> record.getStatus() == ActionExecutionRecord.ExecutionStatus.SUCCESS);
    }
    
    /**
     * 获取执行耗时
     */
    public long getElapsedTime() {
        return System.currentTimeMillis() - startTime;
    }

    public void setActions(List<ActionDefinition> actions) {
        this.actions = actions != null ? new ArrayList<>(actions) : new ArrayList<>();
    }

    public void setExecutionRecords(List<ActionExecutionRecord> executionRecords) {
        this.executionRecords = executionRecords != null ? new ArrayList<>(executionRecords) : new ArrayList<>();
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    @Override
    public String toString() {
        return "ActionExecutionContext{" +
                "ruleId='" + ruleId + '\'' +
                ", actionsCount=" + actions.size() +
                ", executionRecordsCount=" + executionRecords.size() +
                ", criticalActionSuccess=" + criticalActionSuccess +
                ", elapsedTime=" + getElapsedTime() + "ms" +
                '}';
    }
}
