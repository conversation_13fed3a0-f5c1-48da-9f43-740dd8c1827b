package com.inxaiot.ruleengine.core;

/**
 * 规则评估上下文key
 */
public class FactKey {

    /**
     * 触发的规则ID
     */
    public static final String TRIGGERED_RULE_ID = "triggeredRuleId";

    /**
     * 触发事件类型
     */
    public static final String TRIGGERED_EVENT_TYPE = "triggeredEventType";
    /**
     * 时间触发事件类型
     */
    public static final String TRIGGERED_EVENT_TYPE_TIME = "TIME_ACTIVATION_EVENT";

    /**
     * 设备触发事件类型
     */
    public static final String TRIGGERED_EVENT_TYPE_DEVICE = "DEVICE_ACTIVATION_EVENT";

    /**
     * 触发时间
     */
    public static final String TRIGGERED_TIME = "timestamp";

    /**
     * 生成状态键
     * @param deviceCode
     * @param pointId
     * @return
     */
    public static String getStateKey(String deviceCode, String pointId) {
        return deviceCode + "." + pointId;
    }

    /**
     * 生成状态持续时间事实的键名
     * @param deviceCode
     * @param pointId
     * @return
     */
    public static String getStateKeepTimeFactKey(String deviceCode, String pointId) {
        return getStateKey(deviceCode,pointId)+"_keepTime";
    }
}
