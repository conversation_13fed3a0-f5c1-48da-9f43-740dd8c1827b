package com.inxaiot.ruleengine.core.engine;

/**
 * 规则引擎服务接口
 * 用于解耦设备状态管理和规则引擎核心
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface RuleEngineService {
    
    /**
     * 处理设备数据更新，模拟mqtt上报状态
     *
     * @param deviceCode 设备Code
     * @param pointId 点位ID
     * @param value 点位值
     */
    void processDeviceValue(String deviceCode, String pointId, Object value);

    /**
     * 触发设备超时相关的规则检查
     * @param conditionId 超时条件ID
     * @param deviceCode 设备Code
     * @param pointId 点位ID
     */
    void triggerRulesForDeviceTimeout(String conditionId,String deviceCode, String pointId);
    /**
     * 触发规则激活
     * @param ruleId 规则ID
     * @param eventType 触发类型，目前由时间激活，后续可能有其他事件激活满足, 字符见ConextKey
     */
    void triggerRuleActivation(long ruleId, String eventType);

    /**
     * 触发规则失活
     * @param ruleId 规则ID
     * @param eventType 触发类型，目前由时间激活，后续可能有其他事件激活满足, 字符见ConextKey
     */
    void triggerRuleDeactivation(long ruleId, String eventType);
}
