package com.inxaiot.ruleengine.core.definition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 动作定义
 * 定义规则触发后要执行的动作，包括设备控制、消息发送、API调用等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class ActionDefinition {

    // Getters and Setters
    /**
     * 动作类型
     * 支持的动作类型：
     * - DEVICE_CONTROL: 设备控制
     * - SEND_MESSAGE: 发送消息
     * - CALL_API: 调用API
     * - LOG_EVENT: 记录事件日志
     */
    @Setter
    @Getter
    @JsonProperty("actionType")
    private String actionType;
    /**
     * 目标参数
     * 用于其他动作类型的参数，如：
     * - 消息内容、接收者
     * - API地址、请求参数
     * - 空调详细设置（温度、风速、模式等）
     */
    @Setter
    @Getter
    @JsonProperty("params")
    private Map<String, Object> params;
    /**
     * 设备控制参数键名- 消息主题
     */
    public final static String DEVICE_KEY_MQTT_TOPIC = "topic";
    /**
     * 设备控制参数键名- 消息数据内容
     */
    public final static String DEVICE_KEY_DATA = "data";

    public final static String DEVICE_KEY_OPERATE = "operate";
    public final static String DEVICE_KEY_OPERATE_WRITE = "write";
    public final static String DEVICE_KEY_SEQ = "seq";
    /**
     * 设备控制参数键名- 设备Code
     */
    public final static String DEVICE_KEY_CODE = "deviceCode";
    /**
     * 设备控制参数键名- 点位ID
     */
    public final static String DEVICE_KEY_POINT = "tagCode";
    /**
     * 设备控制参数键名- 点位值
     */
    public final static String DEVICE_KEY_VALUE = "val";
    /**
     * 动作执行反馈检查参数，预留
     * 比如：
     * - 执行超时间间隔
     * - 调用http地址查询检查是否执行成功
     * - 检查条件参数等等
     */
    @JsonProperty("checkParams")
    private Map<String, Object> checkParams;

    /**
     * 动作类型所需要的环境参数，如URL地址
     */
    @Setter
    @Getter
    @JsonProperty("typeParams")
    private Map<String, Object> envParams;
    
    /**
     * 动作是否启用
     */
    @Setter
    @Getter
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 动作描述
     */
    @Setter
    @Getter
    @JsonProperty("description")
    private String description;
    
    /**
     * 动作执行优先级，最小值1
     * 数值越小优先级越高,1是设备开关，必须执行成功能才执行其他动作
     */
    @Setter
    @Getter
    @JsonProperty("priority")
    private int priority = 99;

    /**
     * 支持的动作类型常量
     */
    public static class ActionTypes {
        public static final String DEVICE_CONTROL = "DEVICE_CONTROL";
        public static final String SEND_MESSAGE = "SEND_MESSAGE";
        public static final String CALL_API = "CALL_API";
        public static final String LOG_EVENT = "LOG_EVENT";
    }

    /**
     * 常用参数键名
     */
    public static class TypeParamKeys {
        // 消息相关
        public static final String MESSAGE_CONTENT = "messageContent";
        public static final String MESSAGE_RECEIVER = "messageReceiver";
        public static final String MESSAGE_TYPE = "messageType";
        
        // API相关
        public static final String API_URL = "apiUrl";
        public static final String API_METHOD = "apiMethod";
        public static final String API_HEADERS = "apiHeaders";
        public static final String API_BODY = "apiBody";

        
        // 日志相关
        public static final String LOG_LEVEL = "logLevel";
        public static final String LOG_MESSAGE = "logMessage";
        public static final String LOG_CATEGORY = "logCategory";
    }

    // 构造函数
    public ActionDefinition() {
    }

    public ActionDefinition(String actionType) {
        this.actionType = actionType;
    }

    /**
     * 添加参数
     */
    public void addParam(String key, Object value) {
        if (params == null) {
            params = new java.util.HashMap<>();
        }
        params.put(key, value);
    }

    /**
     * 获取参数
     */
    public Object getParam(String key) {
        return params != null ? params.get(key) : null;
    }

    /**
     * 获取字符串类型参数
     */
    public String getStringParam(String key) {
        Object value = getParam(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 添加类型参数
     */
    public void addEnvParam(String key, Object value) {
        if (envParams == null) {
            envParams = new java.util.HashMap<>();
        }
        envParams.put(key, value);
    }

    /**
     * 获取类型参数
     */
    public Object getEnvParam(String key) {
        return envParams != null ? envParams.get(key) : null;
    }


    public String getId(){
        if(isDeviceControlAction()){
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>)params.get(DEVICE_KEY_DATA);
            if(data==null)return this.actionType+"data:null";
            // 安全地获取值并转换为字符串
            String deviceCode = getStringValue(data.get(DEVICE_KEY_CODE));
            String pointId = getStringValue(data.get(DEVICE_KEY_POINT));
            String pointValue = getStringValue(data.get(DEVICE_KEY_VALUE));

            return deviceCode + "." + pointId + "." + pointValue;
        }else{
           StringBuilder sb = new StringBuilder();
           for(String key:params.keySet()){
               sb.append(key).append(".").append(getStringValue(params.get(key))).append(".");
           }
           return sb.toString();
        }
    }

    /**
     * 安全地将Object转换为String
     * 兼容String、Integer、Double、Boolean等类型
     */
    private String getStringValue(Object value) {
        if (value == null) {
            return "null";
        }
        return value.toString();
    }


    /**
     * 检查是否为设备控制动作
     */
    @JsonIgnore
    public boolean isDeviceControlAction() {
        return ActionTypes.DEVICE_CONTROL.equals(actionType);
    }

    @Override
    public String toString() {
        return "ActionDefinition{" +
                "actionType='" + actionType + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                ", paramsCount=" + (params != null ? params.size() : 0) +
                ", typeParamsCount=" + (envParams != null ? envParams.size() : 0) +
                '}';
    }
}
