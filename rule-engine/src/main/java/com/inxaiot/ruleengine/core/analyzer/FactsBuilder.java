package com.inxaiot.ruleengine.core.analyzer;

import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.core.FactKey;
import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.device.state.DevicePointState;
import com.inxaiot.ruleengine.device.state.StateCondition;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.device.state.DevicePointRef;
import org.jeasy.rules.api.Facts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * Facts构建器
 * 负责为规则评估构建包含所有相关设备状态的完整Facts对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class FactsBuilder {

    private static final Logger logger = LoggerFactory.getLogger(FactsBuilder.class);

    @Autowired
    private DependencyAnalyzer dependencyAnalyzer;

    @Autowired
    private StateManager stateManager;
    
    /**
     * 为规则构建完整的Facts对象
     * 包含规则需要的所有设备状态数据
     * 
     * @param rule 规则定义
     * @param triggerDeviceCode 触发设备Code
     * @param triggerPointId 触发点位ID
     * @param triggerValue 触发值
     * @return 包含完整状态的Facts对象
     */
    public Facts buildCompleteFactsForRule(RuleDefinition rule, String triggerDeviceCode, String triggerPointId, Object triggerValue) {
        Facts facts = new Facts();
        
        try {
            // 1. 添加触发信息, 如果有
            if(triggerDeviceCode!=null && triggerPointId!=null && triggerValue!=null){
                addTriggerInfo(facts, triggerDeviceCode, triggerPointId, triggerValue);
            }
            
            // 2. 分析规则依赖点位信息
            Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);
            logger.debug("Rule {} requires {} device points", rule.getRuleId(), dependencies.size());
            
            // 3. 聚合所有相关设备状态（基于内存缓存）
            int addedStates = 0;
            int missingStates = 0;
            for (DevicePointRef ref : dependencies) {
                DevicePointState state = stateManager.getDevicePointState(ref.getDeviceCode(), ref.getPointId());
                //状态数据不存在，可能还没上报数据，或者过期清掉了
                if (state == null) {
                    logger.info("Required refer rule device state missing, ruleId:{}, deviceCode: {}, pointId: {}", rule.getRuleId(), ref.getDeviceCode(), ref.getPointId());
                    missingStates++;
                }else if(state.getCurrentValue()==null){
                    logger.info("Required refer rule device point value is null, ruleId:{},deviceCode: {}, pointId: {}", rule.getRuleId(),ref.getDeviceCode(), ref.getPointId());
                    missingStates++;
                }else{
                    // 添加有效状态数据
                    facts.put(FactKey.getStateKey(ref.getDeviceCode(),ref.getPointId()), state.getCurrentValue());
                    addedStates++;
                }
            }

            // 4. 如果是持续时间条件，需要添加持续时间
            if (rule.getTriggerCondition() != null && rule.getTriggerCondition().getConditions() != null) {
                for (DeviceCondition condition : rule.getTriggerCondition().getConditions()) {
                    if (Operators.Duration.STATES_KEEP_SECONDS.equals(condition.getOperator())) {
                        String conditionId =  StateCondition.generateConditionId(condition.getSourceDeviceCode(),condition.getPointId(),condition.getOperator(),condition.getValue(), condition.getDurationSeconds());
                        StateCondition stateCondition = stateManager.getStateCondition(conditionId);
                        facts.put(FactKey.getStateKeepTimeFactKey(condition.getSourceDeviceCode(), condition.getPointId()), stateCondition.getCurrentDurationSeconds());
                    }
                }
            }
            
            // 5. 添加全局上下文
            addGlobalContextToFacts(facts);
            
            // 6. 添加统计信息
            facts.put("stats_addedStates", addedStates);
            facts.put("stats_missingStates", missingStates);
            facts.put("stats_totalDependencies", dependencies.size());
            
            logger.debug("Facts built for rule {}: {} added, {} missing", rule.getRuleId(), addedStates, missingStates);
            
        } catch (Exception e) {
            logger.error("Error building facts for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }
        
        return facts;
    }
    
    /**
     * 添加触发信息到Facts
     */
    private void addTriggerInfo(Facts facts, String triggerDeviceCode, String triggerPointId, Object triggerValue) {
        //添加触发设备的状态键
        String triggerStateKey = FactKey.getStateKey(triggerDeviceCode,triggerPointId);
        facts.put(triggerStateKey, triggerValue);
    }

    
    /**
     * 添加全局上下文信息到Facts，主要用于监控
     */
    private void addGlobalContextToFacts(Facts facts) {
        try {
            LocalDateTime now = LocalDateTime.now();
            facts.put("currentTime", now);
            facts.put("currentTimestamp", System.currentTimeMillis());
            facts.put("currentDate", now.toLocalDate());
            
        } catch (Exception e) {
            logger.error("Error adding global context to facts: {}", e.getMessage(), e);
        }
    }
}
