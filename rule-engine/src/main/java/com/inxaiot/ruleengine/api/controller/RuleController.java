package com.inxaiot.ruleengine.api.controller;

import com.inxaiot.ruleengine.api.ApiResponse;
import com.inxaiot.ruleengine.api.manager.RuleManager;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.device.state.DevicePointState;
import com.inxaiot.ruleengine.device.state.StateCondition;
import com.inxaiot.ruleengine.device.state.StateManager;
import org.jeasy.rules.api.Facts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 规则管理API控制器
 * 提供规则的CRUD操作和管理功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@RestController
@RequestMapping("/api/engine/rule")
@CrossOrigin(origins = "*")
public class RuleController {

    private static final Logger logger = LoggerFactory.getLogger(RuleController.class);

    private final RuleManager ruleManager;
    private final StateManager stateManager;

    @Autowired
    public RuleController(RuleManager ruleManager, StateManager stateManager) {
        this.ruleManager = ruleManager;
        this.stateManager = stateManager;
    }
    /**
     * 根据规则ID获取规则
     */
    @GetMapping("/{ruleId}")
    public ResponseEntity<ApiResponse<RuleDefinition>> getRule(@PathVariable long ruleId) {
        try {
            RuleDefinition rule = ruleManager.findRuleById(ruleId);
            return ResponseEntity.ok(ApiResponse.success(rule));

        } catch (Exception e) {
            logger.error("Error getting rule: {}", ruleId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error getting rule: " + e.getMessage()));
        }
    }
    /**
     * 根据业务ID获取规则
     */
    @GetMapping("/bizId/{bizId}")
    public ResponseEntity<ApiResponse<RuleDefinition>> getRuleByBizId(@PathVariable String bizId) {
        try {
            RuleDefinition rule = ruleManager.findRuleByBizId(bizId);
            return ResponseEntity.ok(ApiResponse.success(rule));

        } catch (Exception e) {
            logger.error("Error getting rule by bizId: {}", bizId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error getting rule: " + e.getMessage()));

        }
    }
    /**
     * 根据分组ID获取规则
     */
    @GetMapping("/groupId/{groupId}")
    public ResponseEntity<ApiResponse<List<RuleDefinition>>> getRulesByGroupId(@PathVariable String groupId) {
        try {
            List<RuleDefinition> rules = ruleManager.findRulesByGroupId(groupId);
            return ResponseEntity.ok(ApiResponse.success(rules));

        } catch (Exception e) {
            logger.error("Error getting rules by groupId: {}", groupId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error getting rules: " + e.getMessage()));
        }
    }
    /**
     * 启用规则
     */
    @PostMapping("/enable/{ruleId}")
    public ResponseEntity<ApiResponse<String>> enableRule(@PathVariable long ruleId) {
        try {
            boolean b = ruleManager.enableRule(ruleId);
            if (!b) {
                return ResponseEntity.ok(ApiResponse.failed("Rule not found: " + ruleId));
            }
            logger.info("Rule enabled successfully: {}", ruleId);
            return ResponseEntity.ok(ApiResponse.success("Rule enabled successfully: " + ruleId));

        } catch (Exception e) {
            logger.error("Error enabling rule: {}", ruleId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error enabling rule: " + e.getMessage()));
        }
    }

    /**
     * 禁用规则
     * @param ruleId
     * @return
     */
    @PostMapping("/disable/{ruleId}")
    public ResponseEntity<ApiResponse<String>> disableRule(@PathVariable long ruleId) {
        try {
            boolean b = ruleManager.disableRule(ruleId);
            if (!b) {
                return ResponseEntity.ok(ApiResponse.failed("Rule not found: " + ruleId));
            }
            logger.info("Rule disabled successfully: {}", ruleId);
            return ResponseEntity.ok(ApiResponse.success("Rule disabled successfully: " + ruleId));

        } catch (Exception e) {
            logger.error("Error disabling rule: {}", ruleId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error disabling rule: " + e.getMessage()));
        }
    }

    /**
     * 根据业务ID启用规则
     * @param bizId
     * @return
     */
    @PostMapping("/enable/bizId/{bizId}")
    public ResponseEntity<ApiResponse<String>> enableRuleByBizId(@PathVariable String bizId) {
        try {
            RuleDefinition rule = ruleManager.findRuleByBizId(bizId);
            if (rule == null) {
                return ResponseEntity.ok(ApiResponse.failed("Rule not found: " + bizId));
            }
            boolean b = ruleManager.enableRule(rule.getRuleId());
            if (!b) {
                return ResponseEntity.ok(ApiResponse.failed("Rule not found: " + bizId));
            }
            logger.info("Rule enabled successfully: {}", bizId);
            return ResponseEntity.ok(ApiResponse.success("Rule enabled successfully: " + bizId));

        } catch (Exception e) {
            logger.error("Error enabling rule: {}", bizId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error enabling rule: " + e.getMessage()));
        }
    }
    /**
     * 根据业务ID禁用规则
     * @param bizId
     * @return
     */
    @PostMapping("/disable/bizId/{bizId}")
    public ResponseEntity<ApiResponse<String>> disableRuleByBizId(@PathVariable String bizId) {
        try {
            RuleDefinition rule = ruleManager.findRuleByBizId(bizId);
            if (rule == null) {
                return ResponseEntity.ok(ApiResponse.failed("Rule not found: " + bizId));
            }
            boolean b = ruleManager.disableRule(rule.getRuleId());
            if (!b) {
                return ResponseEntity.ok(ApiResponse.failed("Rule not found: " + bizId));
            }
            logger.info("Rule disabled successfully: {}", bizId);
            return ResponseEntity.ok(ApiResponse.success("Rule disabled successfully: " + bizId));

        } catch (Exception e) {
            logger.error("Error disabling rule: {}", bizId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error disabling rule: " + e.getMessage()));
        }
    }

    /**
     * 根据分组ID启用规则
     * @param groupId
     * @return
     */
    @PostMapping("/enable/group/{groupId}")
    public ResponseEntity<ApiResponse<String>> enableRulesByGroupId(@PathVariable String groupId) {
        try {
            boolean b = ruleManager.enableRulesByGroupId(groupId);
            if (!b) {
                return ResponseEntity.ok(ApiResponse.failed("No rules found for group: " + groupId));
            }
            logger.info("Rules enabled successfully: {}", groupId);
            return ResponseEntity.ok(ApiResponse.success("Rules enabled successfully: " + groupId));
        } catch (Exception e) {
            logger.error("Error enabling rules: {}", groupId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error enabling rules: " + e.getMessage()));
        }
    }

    /**
     * 根据分组ID禁用规则
     * @param groupId
     * @return
     */
    @PostMapping("/disable/group/{groupId}")
    public ResponseEntity<ApiResponse<String>> disableRulesByGroupId(@PathVariable String groupId) {
        try {
            boolean b = ruleManager.disableRulesByGroupId(groupId);
            if (!b) {
                return ResponseEntity.ok(ApiResponse.failed("No rules found for group: " + groupId));
            }
            logger.info("Rules disabled successfully: {}", groupId);
            return ResponseEntity.ok(ApiResponse.success("Rules disabled successfully: " + groupId));
        } catch (Exception e) {
            logger.error("Error disabling rules: {}", groupId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error disabling rules: " + e.getMessage()));
        }
    }

    /**
     * 删除规则
     */
    @DeleteMapping("/{ruleId}")
    public ResponseEntity<ApiResponse<String>> deleteRule(@PathVariable Long ruleId) {
        try {
            boolean b = ruleManager.deleteRuleById(ruleId);
            logger.info("Rule {} deleted result: {}", ruleId,b);
            return ResponseEntity.ok(ApiResponse.success("Rule deleted successfully: " + ruleId));
        } catch (Exception e) {
            logger.error("Error deleting rule: {}", ruleId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error deleting rule: " + e.getMessage()));
        }
    }
    /**
     * 根据业务ID删除规则
     */
    @DeleteMapping("/bizId/{bizId}")
    public ResponseEntity<ApiResponse<String>> deleteRuleByBizId(@PathVariable String bizId) {
        try {
            boolean b = ruleManager.deleteRuleByBizId(bizId);
            logger.info("Rule {} deleted result: {}", bizId,b);
            return ResponseEntity.ok(ApiResponse.success("Rule deleted successfully: " + bizId));
        } catch (Exception e) {
            logger.error("Error deleting rule: {}", bizId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error deleting rule: " + e.getMessage()));
        }
    }
    /**
     * 根据分组ID删除规则
     */
    @DeleteMapping("/group/{groupId}")
    public ResponseEntity<ApiResponse<String>> deleteRulesByGroupId(@PathVariable String groupId) {
        try {
            boolean b = ruleManager.deleteRulesByGroupId(groupId);
            logger.info("Rules {} deleted result: {}", groupId,b);
            return ResponseEntity.ok(ApiResponse.success("Rules deleted successfully: " + groupId));
        } catch (Exception e) {
            logger.error("Error deleting rules: {}", groupId, e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error deleting rules: " + e.getMessage()));
        }
    }

    /**
     * 添加或更新规则
     */
    @PostMapping
    public ResponseEntity<ApiResponse<String>> addOrUpdateRule(@Valid @RequestBody RuleDefinition ruleDefinition) {
        try {
            if (ruleDefinition.getRuleId() == null && ruleDefinition.getBizId()==null) {
                return ResponseEntity.ok(ApiResponse.failed("Rule ID and Biz ID cannot all be empty"));
            }
            boolean b = ruleManager.saveOrUpdateRule(ruleDefinition);
            if (!b) {
                return ResponseEntity.ok(ApiResponse.failed("Rule save failed,bizId: " + ruleDefinition.getBizId()));
            }

            logger.info("Rule saved successfully: {}", ruleDefinition.getRuleId());
            return ResponseEntity.ok(ApiResponse.success("Rule saved successfully: " + ruleDefinition.getRuleId()));

        } catch (Exception e) {
            logger.error("Error saving rule: {}", ruleDefinition.getRuleId(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error saving rule: " + e.getMessage()));
        }
    }
    /**
     * 批量添加规则
     */
    @PostMapping("/batch")
    public ResponseEntity<ApiResponse<String>> addRules(@RequestBody List<RuleDefinition> ruleDefinitions) {
        try {
            ruleManager.batchSaveRules(ruleDefinitions);
            logger.info("Rules saved successfully: {}", ruleDefinitions.size());
            return ResponseEntity.ok(ApiResponse.success("Rules saved successfully: " + ruleDefinitions.size()));

        } catch (Exception e) {
            logger.error("Error saving rules: {}", ruleDefinitions.size(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error saving rules: " + e.getMessage()));
        }
    }
    /**
     * 批量更新规则
     */
    @PutMapping("/batch")
    public ResponseEntity<ApiResponse<String>> updateRules(@RequestBody List<RuleDefinition> ruleDefinitions) {
        try {
            ruleManager.batchUpdateRules(ruleDefinitions);
            logger.info("Rules updated successfully: {}", ruleDefinitions.size());
            return ResponseEntity.ok(ApiResponse.success("Rules updated successfully: " + ruleDefinitions.size()));

        } catch (Exception e) {
            logger.error("Error updating rules: {}", ruleDefinitions.size(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error updating rules: " + e.getMessage()));
        }
    }


    /**
     * 获取所有规则
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<RuleDefinition>>> getAllRules(
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) String deviceCode,
            @RequestParam(required = false) String bizId,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) List<String> groupIds
            ) {
        try {
            List<RuleDefinition> rules;

            if (deviceCode != null && !deviceCode.trim().isEmpty()) {
                rules = ruleManager.findAllEnabledRulesRelevantToDevice(deviceCode);
            } else if (bizId != null && !bizId.trim().isEmpty()) {
                rules = new ArrayList<>();
                rules.add(ruleManager.findRuleByBizId(bizId));
            } else if (groupId != null && !groupId.trim().isEmpty()) {
                rules = ruleManager.findRulesByGroupId(groupId);
            }else if (groupIds!=null && !groupIds.isEmpty()){
                rules = ruleManager.findRulesByGroupIds(groupIds);
            } else {
                rules = ruleManager.findAllRules();
            }

            if(enabled!=null){
                rules = rules.stream().filter(r->r.isEnabled()==enabled).collect(Collectors.toList());
            }
            return ResponseEntity.ok(ApiResponse.success(rules));

        } catch (Exception e) {
            logger.error("Error getting rules", e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error getting rules: " + e.getMessage()));
        }
    }
    /**
     * 查询所有规则相关的设备即时状态
     * @return
     */
    @GetMapping("/device/status")
    public ResponseEntity<Map<String, DevicePointState>> getDeviceStates() {
        return ResponseEntity.ok(stateManager.getDeviceStates());
    }

    /**
     * 查询有持续状态监控的设备即时状态
     * @return
     */
    @GetMapping("/device/keepStatus")
    public ResponseEntity<List<StateCondition>> getConditions(){
        return ResponseEntity.ok(stateManager.getStateConditions());
    }

    /**
     * 查询某个规则的即时事实条件
     *
     * @param bizId
     * @return
     */
    @GetMapping("/status/bizId/{bizId}")
    public ResponseEntity<Map<String, Facts>> getRuleStatus(@PathVariable String bizId){
        return ResponseEntity.ok(ruleManager.getRuleStatus(bizId));
    }
    /**
     * 获取规则统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRuleStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalRules", ruleManager.countAllRules());
            stats.put("enabledRules", ruleManager.countEnabledRules());
            stats.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(ApiResponse.success(stats));

        } catch (Exception e) {
            logger.error("Error getting rule statistics", e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error getting rule statistics: " + e.getMessage()));
        }
    }

    /**
     * 刷新规则缓存
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<String>> refreshRules() {
        try {
            ruleManager.refreshRules();
            logger.info("Rule cache refreshed manually");
            return ResponseEntity.ok(ApiResponse.success("Rule cache refreshed successfully"));

        } catch (Exception e) {
            logger.error("Error refreshing rule cache", e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error refreshing rule cache: " + e.getMessage()));
        }
    }
}