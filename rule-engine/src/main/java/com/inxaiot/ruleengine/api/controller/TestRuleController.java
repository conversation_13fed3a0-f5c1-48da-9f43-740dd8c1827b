package com.inxaiot.ruleengine.api.controller;

import com.inxaiot.ruleengine.api.ApiResponse;
import com.inxaiot.ruleengine.core.engine.RuleEngineService;
import com.inxaiot.ruleengine.device.state.StateManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 *
 * 用于接口测试触发规则
 */
@RestController
@RequestMapping("/api/engine/rule/test")
@CrossOrigin(origins = "*")
public class TestRuleController {
    private static final Logger logger = LoggerFactory.getLogger(TestRuleController.class);
    //@Autowired
    //private RuleEngineService ruleEngineService;
    @Autowired
    private StateManager stateManager;
    /**
     * 测试规则触发
     */
    @PostMapping("/test/{ruleId}")
    public ResponseEntity<ApiResponse<String>> testRule(@PathVariable String ruleId, @RequestBody Map<String, Object> testData) {
        try {
            // 这里可以实现规则测试逻辑
            // 例如：模拟设备事件，检查规则是否会被触发

            String deviceCode = (String) testData.get("deviceCode");
            String pointId = (String) testData.get("pointId");
            Object value = testData.get("value");
            String dataType = (String) testData.get("dataType");

            if (deviceCode != null && pointId != null && value != null) {
                stateManager.processDevicePointUpdate(deviceCode, pointId, value);
                //ruleEngineService.processDeviceEvent(deviceCode, pointId, value,dataType);
                return ResponseEntity.ok(ApiResponse.success("Test event sent for rule: " + ruleId));
            } else {
                return ResponseEntity.badRequest().body(ApiResponse.error("Test data must include deviceCode, pointId, and value"));
            }

        } catch (Exception e) {
            logger.error("Error testing rule: {}", ruleId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Error testing rule: " + e.getMessage()));
        }
    }
}
