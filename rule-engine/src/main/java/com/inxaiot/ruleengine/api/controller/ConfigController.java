package com.inxaiot.ruleengine.api.controller;

import com.inxaiot.ruleengine.api.ApiResponse;
import com.inxaiot.ruleengine.storage.GlobalCalendarService;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 配置管理API控制器
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@RestController
@RequestMapping("/api/engine/config")
public class ConfigController {

    private static final Logger logger = LoggerFactory.getLogger(ConfigController.class);

    private final GlobalCalendarService globalCalendarService;

    @Autowired
    public ConfigController(GlobalCalendarService globalConfigStorageService) {
        this.globalCalendarService = globalConfigStorageService;
    }

    /**
     * 获取全局日历
     */
    @GetMapping("/calendar")
    public ResponseEntity<ApiResponse<GlobalCalendar>> getGlobalCalendar() {
        try {
            GlobalCalendar calendar = globalCalendarService.loadGlobalCalendar();
            return ResponseEntity.ok(ApiResponse.success(calendar));
        } catch (Exception e) {
            logger.error("Error getting global calendar", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 更新全局日历
     */
    @PutMapping("/calendar")
    public ResponseEntity<ApiResponse<String>> updateGlobalCalendar(@RequestBody GlobalCalendar calendar) {
        try {
            globalCalendarService.saveGlobalCalendar(calendar);
            return ResponseEntity.ok(ApiResponse.success("Global calendar updated successfully"));
        } catch (Exception e) {
            logger.error("Error updating global calendar", e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error updating global calendar:" + e.getMessage()));
        }
    }



    /**
     * 刷新全局日历缓存
     */
    @PostMapping("/calendar/refresh")
    public ResponseEntity<ApiResponse<String>> refreshCalendarCache() {
        try {
            globalCalendarService.refreshCalendarCache();
            return ResponseEntity.ok(ApiResponse.success("Calendar cache refreshed successfully"));
        } catch (Exception e) {
            logger.error("Error refreshing calendar cache", e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error refreshing calendar cache: " + e.getMessage()));
        }
    }

    /**
     * 获取系统配置
     */
    @GetMapping("/system")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemConfig() {
        try {
            Map<String, Object> config = globalCalendarService.getSystemConfig();
            return ResponseEntity.ok(ApiResponse.success(config));
        } catch (Exception e) {
            logger.error("Error getting system config", e);
            return ResponseEntity.status(500).body(ApiResponse.error("Error getting system config: " + e.getMessage()));
        }
    }
}