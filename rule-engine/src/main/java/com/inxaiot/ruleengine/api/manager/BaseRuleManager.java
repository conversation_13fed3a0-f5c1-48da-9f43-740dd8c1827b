package com.inxaiot.ruleengine.api.manager;

import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.storage.RuleService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class BaseRuleManager {
    @Autowired
    protected RuleService ruleService;

    public RuleDefinition findRuleById(long ruleId){
        return ruleService.findRuleById(ruleId);
    }
    public RuleDefinition findRuleByBizId(String bizId){
        return ruleService.findRuleByBizId(bizId);
    }
    public List<RuleDefinition> findRulesByGroupId(String groupId){
        return ruleService.findRulesByGroupId(groupId);
    }

    public List<RuleDefinition> findAllEnabledRules(){
        return ruleService.findAllEnabledRules();
    }

    public List<RuleDefinition> findRulesByGroupIds(List<String> groupIds){
        return ruleService.findRulesByGroupIds(groupIds);
    }
    public List<RuleDefinition> findAllRules(){
        return ruleService.findAllRules();
    }
    public List<RuleDefinition> findAllEnabledRulesRelevantToDevice(String targetDeviceCode){
        return ruleService.findAllEnabledRulesRelevantToDevice(targetDeviceCode);
    }
    public int countAllRules(){
        return ruleService.countAllRules();
    }
    public int countEnabledRules(){
        return ruleService.countEnabledRules();
    }

}
