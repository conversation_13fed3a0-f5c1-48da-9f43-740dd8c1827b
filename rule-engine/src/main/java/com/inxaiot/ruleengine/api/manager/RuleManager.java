package com.inxaiot.ruleengine.api.manager;

import com.inxaiot.ruleengine.common.IDGenerator;
import com.inxaiot.ruleengine.core.analyzer.FactsBuilder;
import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.device.state.StateCondition;
import com.inxaiot.ruleengine.device.state.DevicePointRef;
import com.inxaiot.ruleengine.core.analyzer.DependencyAnalyzer;
import com.inxaiot.ruleengine.common.operator.Operators;
import org.jeasy.rules.api.Facts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
public class RuleManager extends BaseRuleManager{

    private static final Logger logger = LoggerFactory.getLogger(RuleManager.class);
    @Autowired
    private StateManager stateManager;
    @Autowired
    private DependencyAnalyzer dependencyAnalyzer;
    @Autowired
    private FactsBuilder factsBuilder;

    /**
     * 启用规则
     * @param ruleId
     * @return
     */
    public boolean enableRule(long ruleId){
        RuleDefinition rule = findRuleById(ruleId);
        if(rule==null){
            return false;
        }
        if(rule.isEnabled()){
            return true;
        }
        logger.info("Enabling rule: {}", ruleId);

        List<Long> ruleIds = new ArrayList<>();
        ruleIds.add(ruleId);
        ruleService.batchUpdateRuleEnabledByIds(ruleIds, true);

        //更新状态内存
        rule.setEnabled(true);
        registerDevicePointStatesForRule(rule);
        registerStateConditionsForRule(rule);
        logger.info("Successfully enabled rule: {}", ruleId);
        return true;
    }
    /**
     * 禁用规则
     * @param ruleId
     * @return
     */
    public boolean disableRule(long ruleId){
        RuleDefinition rule = findRuleById(ruleId);
        if(rule==null){
            return false;
        }
        if(!rule.isEnabled()){
            return true;
        }
        logger.info("Disabling rule: {}", ruleId);

        List<Long> ruleIds = new ArrayList<>();
        ruleIds.add(ruleId);
        ruleService.batchUpdateRuleEnabledByIds(ruleIds, false);

        //更新状态内存
        rule.setEnabled(false);
        stateManager.unregisterDevicePointStateByRuleId(ruleId);
        unregisterStateConditionsForRule(rule);
        logger.info("Successfully disabled rule: {}", ruleId);
        return true;
    }
    /**
     * 根据分组ID启用规则
     * @param groupId
     * @return
     */
    public boolean enableRulesByGroupId(String groupId){
        List<RuleDefinition> rules = findRulesByGroupId(groupId);
        if(rules==null||rules.isEmpty()){
            return false;
        }
        logger.info("Enabling rules by group id: {}", groupId);
        List<Long> ruleIds = new ArrayList<>();
        for(RuleDefinition rule:rules){
            if(!rule.isEnabled()){
                ruleIds.add(rule.getRuleId());
            }
        }
        if(ruleIds.isEmpty()){
            return true;
        }
        ruleService.batchUpdateRuleEnabledByIds(ruleIds, true);
        for(RuleDefinition rule:rules){
            if(!rule.isEnabled()){
                rule.setEnabled(true);
                registerDevicePointStatesForRule(rule);
                registerStateConditionsForRule(rule);
            }
        }
        logger.info("Successfully enabled rules by group id: {}", groupId);
        return true;
    }
    /**
     * 根据分组ID禁用规则
     * @param groupId
     * @return
     */
    public boolean disableRulesByGroupId(String groupId){
        List<RuleDefinition> rules = findRulesByGroupId(groupId);
        if(rules==null||rules.isEmpty()){
            return false;
        }
        logger.info("Disabling rules by group id: {}", groupId);
        List<Long> ruleIds = new ArrayList<>();
        for(RuleDefinition rule:rules){
            if(rule.isEnabled()){
                ruleIds.add(rule.getRuleId());
            }
        }
        if(ruleIds.isEmpty()){
            return true;
        }
        ruleService.batchUpdateRuleEnabledByIds(ruleIds, false);
        for(RuleDefinition rule:rules){
            if(rule.isEnabled()){
                rule.setEnabled(false);
                stateManager.unregisterDevicePointStateByRuleId(rule.getRuleId());
                unregisterStateConditionsForRule(rule);
            }
        }
        logger.info("Successfully disabled rules by group id: {}", groupId);
        return true;
    }

    /**
     * 删除规则
     * @param ruleId
     * @return
     */
    public boolean deleteRuleById(long ruleId){
        logger.info("Deleting rule: {}", ruleId);
        RuleDefinition rule = findRuleById(ruleId);
        if(rule==null){
            return true;
        }
        //清理内存状态
        rule.setEnabled(false);
        stateManager.unregisterDevicePointStateByRuleId(ruleId);
        unregisterStateConditionsForRule(rule);

        //删除数据
        ruleService.deleteRuleById(ruleId);
        logger.info("Successfully deleted rule: {}", ruleId);
        return true;
    }
    /**
     * 根据业务ID删除规则
     * @param bizId
     * @return
     */
    public boolean deleteRuleByBizId(String bizId){
        logger.info("Deleting rule by bizId: {}", bizId);
        RuleDefinition rule = findRuleByBizId(bizId);
        if(rule==null){
            return true;
        }
        //清理内存状态
        rule.setEnabled(false);
        stateManager.unregisterDevicePointStateByRuleId(rule.getRuleId());
        unregisterStateConditionsForRule(rule);

        //删除数据
        ruleService.deleteRuleById(rule.getRuleId());
        logger.info("Successfully deleted rule: {}", rule.getRuleId());
        return true;
    }
    /**
     * 根据分组ID删除规则
     * @param groupId
     * @return
     */
    public boolean deleteRulesByGroupId(String groupId){
        logger.info("Deleting rules by group id: {}", groupId);
        List<RuleDefinition> rules = findRulesByGroupId(groupId);
        if(rules==null||rules.isEmpty()){
            return true;
        }
        List<Long> ruleIds = new ArrayList<>();
        //清理内存状态
        for(RuleDefinition rule:rules){
            rule.setEnabled(false);
            stateManager.unregisterDevicePointStateByRuleId(rule.getRuleId());
            unregisterStateConditionsForRule(rule);
            ruleIds.add(rule.getRuleId());
        }
        //删除数据
        ruleService.batchDeleteRules(ruleIds);
        logger.info("Successfully deleted rules by group id: {}", groupId);
        return true;
    }
    /**
     * 保存或更新规则
     * @param rule
     * @return
     */
    public boolean saveOrUpdateRule(RuleDefinition rule){
        RuleDefinition oldRule = null;
        if(rule.getRuleId()!=null){
            oldRule = findRuleById(rule.getRuleId());
            if(oldRule==null){
                logger.error("Failed to update rule, rule not found: {}", rule.getRuleId());
                return false;
            }
            if(oldRule.getBizId()!=null && !oldRule.getBizId().equals(rule.getBizId())){
                logger.error("Failed to update rule, bizId is not match: {}", rule.getBizId());
               return false;
            }
        }else if(rule.getBizId()!=null){
            oldRule = findRuleByBizId(rule.getBizId());
            rule.setRuleId(oldRule==null?null:oldRule.getRuleId());
        }

        if(oldRule!=null){
            stateManager.unregisterDevicePointStateByRuleId(oldRule.getRuleId());
            unregisterStateConditionsForRule(oldRule);
            ruleService.updateRule(rule);
        }else{
            //生成ruleId
            rule.setRuleId(IDGenerator.generateId());
            ruleService.saveRule(rule);
        }
        registerDevicePointStatesForRule(rule);
        registerStateConditionsForRule(rule);
        return true;
    }

    /**
     * 批量保存规则
     * @param rules
     * @return
     */
    public boolean batchSaveRules(List<RuleDefinition> rules){
        for(RuleDefinition rule:rules){
            saveOrUpdateRule(rule);
        }
        return true;
    }
    /**
     * 批量更新规则
     * @param rules
     * @return
     */
    public boolean batchUpdateRules(List<RuleDefinition> rules){
        for(RuleDefinition rule:rules){
            saveOrUpdateRule(rule);
        }
        return true;
    }
    /**
     * 获取规则当前评估状态
     */
    public Map<String,Facts> getRuleStatus(String bizId){
        RuleDefinition rule =  ruleService.findRuleByBizId(bizId);
        Map<String,Facts> factsMap = new HashMap<>();
        if(rule==null){
            return factsMap;
        }
        Facts facts = factsBuilder.buildCompleteFactsForRule(rule,null,null,null);
        factsMap.put(bizId,facts);
        return factsMap;
    }

    //初始化系统业务相关数据
    @PostConstruct
    public void init(){
        // 初始化设备点位状态
        initializeDevicePointStates();

        // 初始化状态条件监控
        initializeStateConditions();
    }


    /**
     * 初始化设备点位状态
     * 在系统启动时重建devicePointStates的引用关系映射
     */
    private void initializeDevicePointStates() {
        try {
            List<RuleDefinition> enabledRules = ruleService.findAllEnabledRules();
            logger.info("Initializing device point states for {} enabled rules", enabledRules.size());

            int registeredDevicePoints = 0;
            for (RuleDefinition rule : enabledRules) {
                int devicePointsForRule = registerDevicePointStatesForRule(rule);
                registeredDevicePoints += devicePointsForRule;
            }

            logger.info("Successfully registered {} device point states from {} rules", registeredDevicePoints, enabledRules.size());

        } catch (Exception e) {
            logger.error("Error initializing device point states: {}", e.getMessage(), e);
        }
    }

    private void initializeStateConditions() {
        try {
            // 从所有启用的规则中提取状态条件并注册
            List<RuleDefinition> enabledRules = ruleService.findAllEnabledRules();
            logger.info("Initializing state conditions for {} enabled rules", enabledRules.size());

            int registeredConditions = 0;
            for (RuleDefinition rule : enabledRules) {
                int conditionsForRule = registerStateConditionsForRule(rule);
                registeredConditions += conditionsForRule;
            }

            logger.info("Successfully registered {} state conditions from {} rules", registeredConditions, enabledRules.size());

        } catch (Exception e) {
            logger.error("Error initializing state conditions: {}", e.getMessage(), e);
        }
    }
    public void refreshRules(){
        ruleService.reloadRules();
        //TODO 是否重新加载DevicePointStates和StateConditions？
    }

    /**
     * 从规则定义中提取并注册设备点位状态
     *
     * @param rule 规则定义
     * @return 注册的设备点位数量
     */
    private int registerDevicePointStatesForRule(RuleDefinition rule) {
        if (rule == null || !rule.isEnabled()) {
            logger.debug("Skipping null or disabled rule: {}", rule != null ? rule.getRuleId() : "null");
            return 0;
        }

        int registeredCount = 0;

        try {
            // 使用DependencyAnalyzer提取规则依赖的设备点位
            Set<DevicePointRef> requiredDevicePoints = dependencyAnalyzer.extractRequiredDevicePoints(rule);

            for (DevicePointRef devicePointRef : requiredDevicePoints) {
                stateManager.registerDevicePointState(devicePointRef.getDeviceCode(), devicePointRef.getPointId(), rule.getRuleId());
                registeredCount++;
            }

            if (registeredCount > 0) {
                logger.debug("Registered {} device point states for rule {}", registeredCount, rule.getRuleId());
            }

        } catch (Exception e) {
            logger.error("Error registering device point states for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }

        return registeredCount;
    }

    /**
     * 从规则定义中提取并注册状态条件
     *
     * @param rule 规则定义
     * @return 注册的状态条件数量
     */
    private int registerStateConditionsForRule(RuleDefinition rule) {
        if (rule == null || !rule.isEnabled()) {
            logger.debug("Skipping null or disabled rule: {}", rule != null ? rule.getRuleId() : "null");
            return 0;
        }

        int registeredCount = 0;

        try {
            // 检查规则是否有触发条件
            TriggerCondition triggerCondition = rule.getTriggerCondition();
            if (triggerCondition == null || !triggerCondition.isEnabled() || triggerCondition.isEmpty()) {
                logger.debug("Rule {} has no enabled trigger conditions", rule.getRuleId());
                return 0;
            }

            // 遍历所有设备条件，查找持续时间条件
            List<DeviceCondition> deviceConditions = triggerCondition.getConditions();
            if (deviceConditions != null) {
                for (DeviceCondition deviceCondition : deviceConditions) {
                    if (isDurationCondition(deviceCondition)) {
                        StateCondition stateCondition = convertToStateCondition(rule, deviceCondition);
                        if (stateCondition != null) {
                            stateManager.registerStateCondition(stateCondition);
                            registeredCount++;
                            logger.debug("Registered state condition: {} for rule {}", stateCondition.getConditionExpression(), rule.getRuleId());
                        }
                    }
                }
            }

            if (registeredCount > 0) {
                logger.info("Registered {} state conditions for rule {}", registeredCount, rule.getRuleId());
            }

        } catch (Exception e) {
            logger.error("Error registering state conditions for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }

        return registeredCount;
    }

    /**
     * 判断设备条件是否为持续时间条件
     *
     * @param deviceCondition 设备条件
     * @return 是否为持续时间条件
     */
    private boolean isDurationCondition(DeviceCondition deviceCondition) {
        if (deviceCondition == null || !deviceCondition.isEnabled()) {
            return false;
        }

        String operator = deviceCondition.getOperator();
        if (operator == null) {
            return false;
        }

        // 检查是否为持续时间操作符
        return Operators.Duration.STATES_KEEP_SECONDS.equals(operator.toUpperCase().trim());
    }

    /**
     * 将设备条件转换为状态条件
     *
     * @param rule 规则定义
     * @param deviceCondition 设备条件
     * @return 状态条件对象
     */
    private StateCondition convertToStateCondition(RuleDefinition rule, DeviceCondition deviceCondition) {
        try {
            // 确定设备Code：优先使用sourceDeviceCode，否则使用规则的targetDeviceCode
            String deviceCode = deviceCondition.getSourceDeviceCode();

            if (deviceCode == null || deviceCondition.getPointId() == null) {
                logger.warn("Missing deviceCode or pointId in condition for rule {}", rule.getRuleId());
                return null;
            }

            // 生成条件ID
            String conditionId = StateCondition.generateConditionId(
                deviceCode,
                deviceCondition.getPointId(),
                deviceCondition.getOperator(),
                deviceCondition.getValue(),
                deviceCondition.getDurationSeconds()
            );

            // 创建状态条件对象
            StateCondition stateCondition = new StateCondition(
                conditionId,
                deviceCode,
                deviceCondition.getPointId(),
                deviceCondition.getOperator(),
                deviceCondition.getSubOperator(),
                deviceCondition.getValue(),
                deviceCondition.getDurationSeconds()
            );

            // 设置其他属性
            stateCondition.setDataType(deviceCondition.getDataType());
            stateCondition.setRuleId(rule.getRuleId());
            stateCondition.setEnabled(deviceCondition.isEnabled());
            stateCondition.setDescription(String.format("Auto-generated from rule %s: %s", rule.getRuleId(), deviceCondition.getDescription()));

            // 处理BETWEEN操作符的第二个值
            if (deviceCondition.getUpperValue() != null) {
                stateCondition.setValue2(deviceCondition.getUpperValue());
            }

            return stateCondition;

        } catch (Exception e) {
            logger.error("Error converting device condition to state condition for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 注销规则的状态条件（用于删除或禁用规则时调用）
     *
     * @param rule 规则定义
     */
    public void unregisterStateConditionsForRule(RuleDefinition rule) {
        if (rule == null) {
            return;
        }

        try {
            TriggerCondition triggerCondition = rule.getTriggerCondition();
            if (triggerCondition == null || triggerCondition.isEmpty()) {
                return;
            }

            int unregisteredCount = 0;
            List<DeviceCondition> deviceConditions = triggerCondition.getConditions();
            if (deviceConditions != null) {
                for (DeviceCondition deviceCondition : deviceConditions) {
                    if (isDurationCondition(deviceCondition)) {
                        String deviceCode = deviceCondition.getSourceDeviceCode();

                        if (deviceCode != null && deviceCondition.getPointId() != null) {
                            String conditionId = StateCondition.generateConditionId(
                                deviceCode,
                                deviceCondition.getPointId(),
                                deviceCondition.getOperator(),
                                deviceCondition.getValue(),
                                deviceCondition.getDurationSeconds()
                            );
                            stateManager.unregisterStateCondition(conditionId);
                            unregisteredCount++;
                        }
                    }
                }
            }

            if (unregisteredCount > 0) {
                logger.info("Unregistered {} state conditions for rule {}", unregisteredCount, rule.getRuleId());
            }

        } catch (Exception e) {
            logger.error("Error unregistering state conditions for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }
    }

    /**
     * 重新加载所有状态条件（用于规则配置变更后的刷新）
     */
    public void reloadAllStateConditions() {
        try {
            logger.info("Reloading all state conditions...");

            // 清除现有的状态条件监控
            stateManager.clearAllStateConditions();

            // 重新初始化
            initializeStateConditions();

        } catch (Exception e) {
            logger.error("Error reloading state conditions: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取状态条件统计信息
     *
     * @return 状态条件统计信息
     */
    public java.util.Map<String, Object> getStateConditionStatistics() {
        return stateManager.getStateConditionStatistics();
    }

    /**
     * 获取指定规则的状态条件
     *
     * @param ruleId 规则ID
     * @return 状态条件列表
     */
    public List<StateCondition> getStateConditionsByRule(long ruleId) {
        return stateManager.getStateConditionsByRule(ruleId);
    }

    /**
     * 检查指定的状态条件是否已注册
     *
     * @param conditionId 条件ID
     * @return 是否已注册
     */
    public boolean isStateConditionRegistered(String conditionId) {
        return stateManager.isStateConditionRegistered(conditionId);
    }

}
