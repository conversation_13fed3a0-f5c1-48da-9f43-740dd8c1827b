package com.inxaiot.ruleengine.storage.entity;

import java.time.LocalDateTime;

/**
 * 全局日历数据库实体类（单记录设计）
 * 对应global_calendar表
 *
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2024-12-19
 */
public class GlobalCalendarEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务ID，预留扩展
     */
    private String bizId;

    /**
     * 排除月日列表（JSON格式）
     */
    private String excludeMonthDays;

    /**
     * 命名时间段定义（JSON格式）
     */
    private String monthDateRanges;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // 构造函数
    public GlobalCalendarEntity() {
        this.bizId = "default";
    }

    public GlobalCalendarEntity(String bizId) {
        this.bizId = bizId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getExcludeMonthDays() {
        return excludeMonthDays;
    }

    public void setExcludeMonthDays(String excludeMonthDays) {
        this.excludeMonthDays = excludeMonthDays;
    }

    public String getMonthDateRanges() {
        return monthDateRanges;
    }

    public void setMonthDateRanges(String monthDateRanges) {
        this.monthDateRanges = monthDateRanges;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "GlobalCalendarEntity{" +
                "id=" + id +
                ", bizId='" + bizId + '\'' +
                ", excludeMonthDays='" + excludeMonthDays + '\'' +
                ", dateRanges='" + monthDateRanges + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
