package com.inxaiot.ruleengine.storage.mapper;

import com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 规则定义Mapper接口
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface RuleDefinitionMapper {
    
    /**
     * 插入规则定义
     */
    int insertRule(RuleDefinitionEntity rule);

    /**
     * 根据ID删除规则定义
     */
    int deleteRuleById(@Param("id") Long id);

    /**
     * 根据ID更新规则定义
     */
    int updateRuleById(RuleDefinitionEntity rule);

    /**
     * 根据ID查询规则定义
     */
    RuleDefinitionEntity findRuleById(@Param("id") Long id);

    /**
     * 根据业务ID查询规则定义
     */
    RuleDefinitionEntity findRuleByBizId(@Param("bizId") String bizId);
    
    /**
     * 查询所有规则定义
     */
    List<RuleDefinitionEntity> findAllRules();
    
    /**
     * 查询所有启用的规则定义
     */
    List<RuleDefinitionEntity> findAllEnabledRules();
    

    /**
     * 根据分组ID查询规则定义
     */
    List<RuleDefinitionEntity> findRulesByGroupId(@Param("groupId") String groupId);

    List<RuleDefinitionEntity> findRulesByGroupIds(@Param("groupIds") List<String> groupIds);
    
    /**
     * 批量插入规则定义
     */
    int batchInsertRules(@Param("rules") List<RuleDefinitionEntity> rules);
    
    /**
     * 批量更新规则启用状态
     */
    int batchUpdateRuleEnabledByIds(@Param("ids") List<Long> ruleIds, @Param("enabled") Boolean enabled);

    
    /**
     * 根据分组ID批量删除规则定义
     */
    int batchDeleteRulesByGroupId(@Param("groupId") String groupId);

    /**
     * 批量删除规则定义
     */
    int batchDeleteRulesByIds(@Param("ids") List<Long> ids);
    
    /**
     * 统计规则总数
     */
    int countAllRules();
    
    /**
     * 统计启用的规则数
     */
    int countEnabledRules();
    

}
