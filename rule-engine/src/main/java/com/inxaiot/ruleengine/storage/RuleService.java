package com.inxaiot.ruleengine.storage;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity;
import com.inxaiot.ruleengine.storage.mapper.RuleDefinitionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 规则存储服务
 * 提供规则定义的存储、查询、更新等功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
@Transactional
public class RuleService {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleService.class);
    
    private final RuleDefinitionMapper ruleDefinitionMapper;
    private final ObjectMapper objectMapper;

    //所有启用规则缓存
    private List<RuleDefinition> allEnabledRulesCache = new ArrayList<>();

    //针对规则相关设备点位的缓存
    private final Map<String,List<RuleDefinition>>  refDeviceRulesCache = new ConcurrentHashMap<>();

    @Autowired
    public RuleService(RuleDefinitionMapper ruleDefinitionMapper, ObjectMapper objectMapper) {
        this.ruleDefinitionMapper = ruleDefinitionMapper;
        this.objectMapper = objectMapper;
    }
    @PostConstruct
    public void init(){
        refreshDeviceRulesCache();
        logger.warn("Init allEnabledRulesCache, total {} rules",allEnabledRulesCache.size());
    }
    private void refreshDeviceRulesCache() {
        long start = System.currentTimeMillis();
        logger.info("Refreshing rule caches...");
        List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findAllEnabledRules();
        allEnabledRulesCache =  entities.stream().map(this::convertToRuleDefinition).collect(Collectors.toList());
        //刷新分组规则缓存、针对设备相关规则缓存、针对规则相关设备点位的缓存
        refDeviceRulesCache.clear();
        allEnabledRulesCache.forEach(rule -> {
            //刷新针对规则相关设备点位的缓存
            if(rule.getTriggerCondition()!=null && rule.getTriggerCondition().getConditions() !=null){
                rule.getTriggerCondition().getConditions().forEach(condition -> {
                    refDeviceRulesCache.computeIfAbsent(condition.getSourceDeviceCode(), k -> new ArrayList<>()).add(rule);
                });
            }
        });
        logger.info("Rule caches refreshed, cost {}ms", System.currentTimeMillis() - start);
    }
    /**
     * 保存规则定义
     */
    public void saveRule(RuleDefinition ruleDefinition) {
        try {
            RuleDefinitionEntity entity = convertToEntity(ruleDefinition);
            int inserted = ruleDefinitionMapper.insertRule(entity);
            logger.info("Inserted rule: {}, affected rows: {}", ruleDefinition.getRuleId(), inserted);
            refreshDeviceRulesCache();
        } catch (Exception e) {
            logger.error("Failed to save rule: {}", ruleDefinition.getRuleId(), e);
            throw new RuntimeException("Failed to save rule: " + ruleDefinition.getRuleId(), e);
        }
    }
    public boolean updateRule(RuleDefinition ruleDefinition){
        try {
            if(ruleDefinition.getRuleId()==null){
                return false;
            }
            RuleDefinitionEntity entity = convertToEntity(ruleDefinition);
            int updated = ruleDefinitionMapper.updateRuleById(entity);
            logger.info("Updated rule: {}, affected rows: {}", ruleDefinition.getRuleId(), updated);
            refreshDeviceRulesCache();
            return true;
        } catch (Exception e) {
            logger.error("Failed to update rule: {}", ruleDefinition.getRuleId(), e);
            throw new RuntimeException("Failed to update rule: " + ruleDefinition.getRuleId(), e);
        }
    }

    /**
     * 根据规则ID查询规则定义
     */
    public RuleDefinition findRuleById(long ruleId) {
        try {
            RuleDefinitionEntity entity = ruleDefinitionMapper.findRuleById(ruleId);
            return entity != null ? convertToRuleDefinition(entity) : null;
        } catch (Exception e) {
            logger.error("Failed to find rule by id: {}", ruleId, e);
            throw new RuntimeException("Failed to find rule by id: " + ruleId, e);
        }
    }

    /**
     * 查询所有规则定义
     */
    public List<RuleDefinition> findAllRules() {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findAllRules();
            return entities.stream().map(this::convertToRuleDefinition).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find all rules", e);
            throw new RuntimeException("Failed to find all rules", e);
        }
    }

    /**
     * 查询所有启用的规则定义
     */
    public List<RuleDefinition> findAllEnabledRules() {
        try {
            return allEnabledRulesCache;
        } catch (Exception e) {
            logger.error("Failed to find all enabled rules", e);
            throw new RuntimeException("Failed to find all enabled rules", e);
        }
    }
    public List<RuleDefinition> findRulesByGroupIds(List<String> groupIds) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findRulesByGroupIds(groupIds);
            return entities.stream().map(this::convertToRuleDefinition).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find rules by group ids: {}", groupIds, e);
            throw new RuntimeException("Failed to find rules by group ids: " + groupIds, e);
        }
    }
    /**
     * 查询与指定设备相关的所有启用规则
     */
    public List<RuleDefinition> findAllEnabledRulesRelevantToDevice(String deviceCode) {
        try {
            List<RuleDefinition> list = refDeviceRulesCache.get(deviceCode);
            return list == null ? new ArrayList<>() : list;
        } catch (Exception e) {
            logger.error("Failed to find enabled rules relevant to device: {}", deviceCode, e);
            throw new RuntimeException("Failed to find enabled rules relevant to device: " + deviceCode, e);
        }
    }

    /**
     * 根据业务ID查询规则定义
     */
    public RuleDefinition findRuleByBizId(String bizId) {
        try {
            return convertToRuleDefinition(ruleDefinitionMapper.findRuleByBizId(bizId));
        } catch (Exception e) {
            logger.error("Failed to find rules by biz id: {}", bizId, e);
            throw new RuntimeException("Failed to find rules by biz id: " + bizId, e);
        }
    }

    public List<RuleDefinition> findRulesByGroupId(String groupId) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findRulesByGroupId(groupId);
            return entities.stream()
                    .map(this::convertToRuleDefinition)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find rules by group id: {}", groupId, e);
            throw new RuntimeException("Failed to find rules by group id: " + groupId, e);
        }
    }

    /**
     * 删除规则定义
     */
    public void deleteRuleById(Long ruleId) {
        try {
            int deleted = ruleDefinitionMapper.deleteRuleById(ruleId);
            logger.info("Deleted rule: {}, affected rows: {}", ruleId, deleted);
            refreshDeviceRulesCache();
        } catch (Exception e) {
            logger.error("Failed to delete rule: {}", ruleId, e);
            throw new RuntimeException("Failed to delete rule: " + ruleId, e);
        }
    }

    /**
     * 批量保存规则定义
     */
    public void batchSaveRules(List<RuleDefinition> ruleDefinitions) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitions.stream()
                    .map(this::convertToEntity)
                    .collect(Collectors.toList());
            
            int inserted = ruleDefinitionMapper.batchInsertRules(entities);
            logger.info("Batch inserted rules, count: {}, affected rows: {}", entities.size(), inserted);
            refreshDeviceRulesCache();
        } catch (Exception e) {
            logger.error("Failed to batch save rules", e);
            throw new RuntimeException("Failed to batch save rules", e);
        }
    }

    /**
     * 批量更新规则启用状态
     */
    public void batchUpdateRuleEnabledByIds(List<Long> ruleIds, boolean enabled) {
        try {
            int updated = ruleDefinitionMapper.batchUpdateRuleEnabledByIds(ruleIds, enabled);
            logger.info("Batch updated rule enabled status, count: {}, enabled: {}, affected rows: {}", ruleIds.size(), enabled, updated);
            refreshDeviceRulesCache();
        } catch (Exception e) {
            logger.error("Failed to batch update rule enabled status", e);
            throw new RuntimeException("Failed to batch update rule enabled status", e);
        }
    }

    /**
     * 批量删除规则定义
     */
    public void batchDeleteRules(List<Long> ruleIds) {
        try {
            int deleted = ruleDefinitionMapper.batchDeleteRulesByIds(ruleIds);
            logger.info("Batch deleted rules, count: {}, affected rows: {}", ruleIds.size(), deleted);
            refreshDeviceRulesCache();
        } catch (Exception e) {
            logger.error("Failed to batch delete rules", e);
            throw new RuntimeException("Failed to batch delete rules", e);
        }
    }

    /**
     * 统计规则数量
     */
    public int countAllRules() {
        return ruleDefinitionMapper.countAllRules();
    }

    /**
     * 统计启用的规则数量
     */
    public int countEnabledRules() {
        return allEnabledRulesCache.size();
    }

    /**
     * 检查规则是否存在
     */
    public boolean existsRule(long ruleId) {
        return ruleDefinitionMapper.findRuleById(ruleId)!=null;
    }

    /**
     * 重新加载规则缓存（预留接口）
     */
    public void reloadRules() {
        logger.info("Rule cache reloaded");
        // 这里可以实现规则缓存的重新加载逻辑
        refreshDeviceRulesCache();
    }

    /**
     * 将RuleDefinition转换为RuleDefinitionEntity
     */
    private RuleDefinitionEntity convertToEntity(RuleDefinition ruleDefinition) {
        if(ruleDefinition==null)return null;
        try {
            RuleDefinitionEntity entity = new RuleDefinitionEntity();
            entity.setId(ruleDefinition.getRuleId());
            entity.setRuleName(ruleDefinition.getRuleName());
            entity.setBizId(ruleDefinition.getBizId());
            entity.setGroupId(ruleDefinition.getGroupId());
            entity.setPriority(ruleDefinition.getPriority());
            entity.setEnabled(ruleDefinition.isEnabled());
            entity.setDescription(ruleDefinition.getDescription());
            entity.setCreateTime(ruleDefinition.getCreateTime());
            entity.setUpdateTime(ruleDefinition.getUpdateTime());
            // 转换logic字段：从枚举转换为字符串
            if (ruleDefinition.getLogic() != null) {
                entity.setLogic(ruleDefinition.getLogic().name());
            } else {
                entity.setLogic(Operators.Logic.AND.name()); // 默认值
            }
            
            // 将复杂对象序列化为JSON
            if (ruleDefinition.getTimeConditions() != null) {
                entity.setTimeConditions(objectMapper.writeValueAsString(ruleDefinition.getTimeConditions()));
            }
            if (ruleDefinition.getTriggerCondition() != null) {
                entity.setTriggerCondition(objectMapper.writeValueAsString(ruleDefinition.getTriggerCondition()));
            }
            if(ruleDefinition.getDeactivationActions() != null) {
                entity.setDeactivationActions(objectMapper.writeValueAsString(ruleDefinition.getDeactivationActions()));
            }
            if (ruleDefinition.getActions() != null) {
                entity.setActions(objectMapper.writeValueAsString(ruleDefinition.getActions()));
            }
            
            return entity;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert RuleDefinition to Entity", e);
        }
    }

    /**
     * 将RuleDefinitionEntity转换为RuleDefinition
     */
    private RuleDefinition convertToRuleDefinition(RuleDefinitionEntity entity) {
        if (entity==null)return null;
        try {
            RuleDefinition ruleDefinition = new RuleDefinition();
            ruleDefinition.setRuleId(entity.getId());
            ruleDefinition.setRuleName(entity.getRuleName());
            ruleDefinition.setBizId(entity.getBizId());
            ruleDefinition.setGroupId(entity.getGroupId());
            ruleDefinition.setPriority(entity.getPriority());
            ruleDefinition.setEnabled(entity.getEnabled());
            ruleDefinition.setDescription(entity.getDescription());
            ruleDefinition.setCreateTime(entity.getCreateTime());
            ruleDefinition.setUpdateTime(entity.getUpdateTime());

            // 转换logic字段：从字符串转换为枚举
            if (entity.getLogic() != null && !entity.getLogic().trim().isEmpty()) {
                try {
                    ruleDefinition.setLogic(Operators.Logic.valueOf(entity.getLogic().toUpperCase()));
                } catch (IllegalArgumentException e) {
                    logger.warn("Invalid logic value: {}, using default AND", entity.getLogic());
                    ruleDefinition.setLogic(Operators.Logic.AND);
                }
            } else {
                ruleDefinition.setLogic(Operators.Logic.AND); // 默认值
            }
            
            // 将JSON反序列化为复杂对象
            if (entity.getTimeConditions() != null && !entity.getTimeConditions().trim().isEmpty()) {
                ruleDefinition.setTimeConditions(objectMapper.readValue(entity.getTimeConditions(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, TimeCondition.class)));
            }
            if (entity.getTriggerCondition() != null && !entity.getTriggerCondition().trim().isEmpty()) {
                ruleDefinition.setTriggerCondition(objectMapper.readValue(entity.getTriggerCondition(),TriggerCondition.class));
            }
            if (entity.getActions() != null && !entity.getActions().trim().isEmpty()) {
                ruleDefinition.setActions(objectMapper.readValue(entity.getActions(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, ActionDefinition.class)));
            }
            if (entity.getDeactivationActions() != null && !entity.getDeactivationActions().trim().isEmpty()) {
                ruleDefinition.setDeactivationActions(objectMapper.readValue(entity.getDeactivationActions(),
                        objectMapper.getTypeFactory().constructCollectionType(List.class, ActionDefinition.class)));
            }
            
            return ruleDefinition;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert Entity to RuleDefinition", e);
        }
    }
}
