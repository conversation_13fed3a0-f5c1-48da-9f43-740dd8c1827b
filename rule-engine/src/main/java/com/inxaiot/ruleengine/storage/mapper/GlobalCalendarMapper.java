package com.inxaiot.ruleengine.storage.mapper;

import com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 全局日历Mapper接口（单记录设计）
 *
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2024-12-19
 */
@Mapper
public interface GlobalCalendarMapper {

    /**
     * 插入全局日历记录
     */
    int insertGlobalCalendar(GlobalCalendarEntity calendar);

    /**
     * 更新全局日历记录
     */
    int updateGlobalCalendar(GlobalCalendarEntity calendar);

    /**
     * 根据业务ID查询全局日历记录
     */
    GlobalCalendarEntity findGlobalCalendarByBizId(@Param("bizId") String bizId);

    /**
     * 查询默认全局日历记录
     */
    GlobalCalendarEntity findDefaultGlobalCalendar();

    /**
     * 检查是否存在全局日历记录
     */
    boolean existsGlobalCalendar(@Param("bizId") String bizId);
}
