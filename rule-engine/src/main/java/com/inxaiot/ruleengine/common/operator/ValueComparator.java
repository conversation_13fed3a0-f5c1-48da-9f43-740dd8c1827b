package com.inxaiot.ruleengine.common.operator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 值比较器
 * 提供通用的值比较功能，支持各种数据类型和操作符
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public final class ValueComparator {

    private static final Logger logger = LoggerFactory.getLogger(ValueComparator.class);
    
    private ValueComparator() {
        // 工具类，禁止实例化
    }
    
    /**
     * 比较两个值
     * 
     * @param actualValue 实际值
     * @param operator 操作符
     * @param targetValue 目标值
     * @param dataType 数据类型
     * @return 比较结果
     */
    public static boolean compare(Object actualValue, String operator, Object targetValue, String dataType) {
        return compare(actualValue, operator, targetValue, null, dataType);
    }
    
    /**
     * 比较两个值（支持双值操作符如BETWEEN）
     * 
     * @param actualValue 实际值
     * @param operator 操作符
     * @param targetValue 目标值
     * @param targetValue2 第二个目标值（用于BETWEEN等）
     * @param dataType 数据类型
     * @return 比较结果
     */
    public static boolean compare(Object actualValue, String operator, Object targetValue, Object targetValue2, String dataType) {
        if (actualValue == null || operator == null || targetValue == null) {
            return false;
        }

        try {
            String upperOperator = operator.toUpperCase().trim();

            // 基础比较操作符
            if (isBasicOperator(upperOperator)) {
                return compareBasic(actualValue, upperOperator, targetValue, dataType);
            }
            // 字符串操作符
            else if (isTextOperator(upperOperator)) {
                return compareString(actualValue, upperOperator, targetValue);
            }
            // 范围操作符
            else if (isRangeOperator(upperOperator)) {
                return compareRange(actualValue, upperOperator, targetValue, targetValue2, dataType);
            }else {
                logger.warn("Unsupported operator: {}", upperOperator);
                return false;
            }
        } catch (Exception e) {
            logger.error("Error comparing values: actualValue={}, operator={}, targetValue={}, dataType={}",
                       actualValue, operator, targetValue, dataType, e);
            return false;
        }
    }
    
    /**
     * 基础比较操作
     */
    private static boolean compareBasic(Object actualValue, String operator, Object targetValue, String dataType) {
        // 支持别名
        if (Operators.Basic.EQUALS.equals(operator)) {
            return compareEquals(actualValue, targetValue, dataType);
        } else if (Operators.Basic.NOT_EQUALS.equals(operator)) {
            return !compareEquals(actualValue, targetValue, dataType);
        } else if (Operators.Basic.GREATER_THAN.equals(operator)) {
            return compareGreaterThan(actualValue, targetValue, dataType);
        } else if (Operators.Basic.GREATER_THAN_OR_EQUAL.equals(operator)) {
            return compareGreaterThanOrEqual(actualValue, targetValue, dataType);
        } else if (Operators.Basic.LESS_THAN.equals(operator)) {
            return compareLessThan(actualValue, targetValue, dataType);
        } else if (Operators.Basic.LESS_THAN_OR_EQUAL.equals(operator)) {
            return compareLessThanOrEqual(actualValue, targetValue, dataType);
        } else {
            logger.warn("Unsupported basic operator: {}", operator);
            return false;
        }
    }

    /**
     * 相等比较
     */
    private static boolean compareEquals(Object actualValue, Object targetValue, String dataType) {
        if ("BOOLEAN".equalsIgnoreCase(dataType)) {
            return convertToBoolean(actualValue).equals(convertToBoolean(targetValue));
        } else if (isNumericType(dataType)) {
            return Double.valueOf(convertToDouble(actualValue)).equals(convertToDouble(targetValue));
        } else {
            return actualValue.toString().equals(targetValue.toString());
        }
    }

    /**
     * 大于比较
     */
    private static boolean compareGreaterThan(Object actualValue, Object targetValue, String dataType) {
        if (isNumericType(dataType)) {
            return convertToDouble(actualValue) > convertToDouble(targetValue);
        } else {
            return actualValue.toString().compareTo(targetValue.toString()) > 0;
        }
    }

    /**
     * 大于等于比较
     */
    private static boolean compareGreaterThanOrEqual(Object actualValue, Object targetValue, String dataType) {
        if (isNumericType(dataType)) {
            return convertToDouble(actualValue) >= convertToDouble(targetValue);
        } else {
            return actualValue.toString().compareTo(targetValue.toString()) >= 0;
        }
    }

    /**
     * 小于比较
     */
    private static boolean compareLessThan(Object actualValue, Object targetValue, String dataType) {
        if (isNumericType(dataType)) {
            return convertToDouble(actualValue) < convertToDouble(targetValue);
        } else {
            return actualValue.toString().compareTo(targetValue.toString()) < 0;
        }
    }

    /**
     * 小于等于比较
     */
    private static boolean compareLessThanOrEqual(Object actualValue, Object targetValue, String dataType) {
        if (isNumericType(dataType)) {
            return convertToDouble(actualValue) <= convertToDouble(targetValue);
        } else {
            return actualValue.toString().compareTo(targetValue.toString()) <= 0;
        }
    }
    
    /**
     * 字符串比较操作
     */
    private static boolean compareString(Object actualValue, String operator, Object targetValue) {
        String actualStr = String.valueOf(actualValue).toLowerCase();
        String targetStr = String.valueOf(targetValue).toLowerCase();

        if (Operators.Text.CONTAINS.equals(operator)) {
            return actualStr.contains(targetStr);
        } else if (Operators.Text.NOT_CONTAINS.equals(operator)) {
            return !actualStr.contains(targetStr);
        } else if (Operators.Text.STARTS_WITH.equals(operator)) {
            return actualStr.startsWith(targetStr);
        } else if (Operators.Text.ENDS_WITH.equals(operator)) {
            return actualStr.endsWith(targetStr);
        } else {
            logger.warn("Unsupported string operator: {}", operator);
            return false;
        }
    }
    
    /**
     * 范围比较操作
     */
    private static boolean compareRange(Object actualValue, String operator, Object targetValue,
                                      Object targetValue2, String dataType) {
        if (Operators.Range.BETWEEN.equals(operator)) {
            if (targetValue2 == null) {
                logger.warn("BETWEEN operator requires two target values");
                return false;
            }
            return compareBetween(actualValue, targetValue, targetValue2, dataType);
        } else if (Operators.Range.NOT_BETWEEN.equals(operator)) {
            if (targetValue2 == null) {
                logger.warn("NOT_BETWEEN operator requires two target values");
                return false;
            }
            return !compareBetween(actualValue, targetValue, targetValue2, dataType);
        } else if (Operators.Range.IN.equals(operator)) {
            return compareIn(actualValue, targetValue, dataType);
        } else if (Operators.Range.NOT_IN.equals(operator)) {
            return !compareIn(actualValue, targetValue, dataType);
        } else {
            logger.warn("Unsupported range operator: {}", operator);
            return false;
        }
    }
    
    /**
     * BETWEEN比较
     */
    private static boolean compareBetween(Object actualValue, Object lowerBound, Object upperBound, String dataType) {
        if (upperBound == null) {
            return false;
        }

        if (isNumericType(dataType)) {
            try {
                double actual = convertToDouble(actualValue);
                double min = Math.min(convertToDouble(lowerBound), convertToDouble(upperBound));
                double max = Math.max(convertToDouble(lowerBound), convertToDouble(upperBound));
                return actual >= min && actual <= max;
            } catch (NumberFormatException e) {
                logger.error("Error in BETWEEN comparison: actual={}, lower={}, upper={}",
                           actualValue, lowerBound, upperBound, e);
                return false;
            }
        } else {
            String actualStr = actualValue.toString();
            String value1Str = lowerBound.toString();
            String value2Str = upperBound.toString();
            return actualStr.compareTo(value1Str) >= 0 && actualStr.compareTo(value2Str) <= 0;
        }
    }
    
    /**
     * IN比较
     */
    private static boolean compareIn(Object actualValue, Object targetValue, String dataType) {
        String actualStr = actualValue.toString();

        if (targetValue instanceof Collection) {
            Collection<?> targetList = (Collection<?>) targetValue;
            return targetList.stream().anyMatch(item -> actualStr.equals(item.toString()));
        } else if (targetValue instanceof Object[]) {
            Object[] array = (Object[]) targetValue;
            return Arrays.stream(array).anyMatch(item -> actualStr.equals(item.toString()));
        } else if (targetValue instanceof String) {
            // 假设是逗号分隔的字符串
            String targetStr = (String) targetValue;
            List<String> targetList = Arrays.asList(targetStr.split(","));
            return targetList.stream().anyMatch(item -> actualStr.equals(item.trim()));
        }

        return false;
    }
    
    /**
     * 转换为double类型
     */
    private static double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return Double.parseDouble(String.valueOf(value));
    }

    /**
     * 转换为Boolean
     */
    private static Boolean convertToBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else {
            return Boolean.parseBoolean(value.toString());
        }
    }

    /**
     * 判断是否为数值类型
     */
    private static boolean isNumericType(String dataType) {
        if (dataType == null) {
            return false;
        }
        return Operators.DataTypes.INTEGER.equalsIgnoreCase(dataType) ||
                Operators.DataTypes.DOUBLE.equalsIgnoreCase(dataType) ||
                Operators.DataTypes.FLOAT.equalsIgnoreCase(dataType) ||
                Operators.DataTypes.LONG.equalsIgnoreCase(dataType);
    }

    /**
     * 检查是否为基础操作符
     */
    private static boolean isBasicOperator(String operator) {
        return Operators.Basic.EQUALS.equals(operator) ||
               Operators.Basic.NOT_EQUALS.equals(operator) ||
               Operators.Basic.GREATER_THAN.equals(operator) ||
               Operators.Basic.GREATER_THAN_OR_EQUAL.equals(operator) ||
               Operators.Basic.LESS_THAN.equals(operator) ||
               Operators.Basic.LESS_THAN_OR_EQUAL.equals(operator);
    }

    /**
     * 检查是否为文本操作符
     */
    private static boolean isTextOperator(String operator) {
        return Operators.Text.CONTAINS.equals(operator) ||
               Operators.Text.NOT_CONTAINS.equals(operator) ||
               Operators.Text.STARTS_WITH.equals(operator) ||
               Operators.Text.ENDS_WITH.equals(operator);
    }

    /**
     * 检查是否为范围操作符
     */
    private static boolean isRangeOperator(String operator) {
        return Operators.Range.BETWEEN.equals(operator) ||
               Operators.Range.NOT_BETWEEN.equals(operator) ||
               Operators.Range.IN.equals(operator) ||
               Operators.Range.NOT_IN.equals(operator);
    }
}
