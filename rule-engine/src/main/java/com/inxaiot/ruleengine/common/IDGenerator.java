package com.inxaiot.ruleengine.common;


import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 只适用于单机运行，如果集群运行，只能限制并发 1毫秒 9个除以机器数
 */
public class IDGenerator {
	private static final String TIME_FORMAT = "yyMMddHHmmssS";
	private static final AtomicInteger sequence = new AtomicInteger(0);
	private static final ThreadLocal<SimpleDateFormat> formatterThreadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat(TIME_FORMAT));
	private static final int MAX_SEQUENCE = 9; // 每毫秒最多生成10个ID

	public static synchronized long generateId() {
		long now = System.currentTimeMillis();
		int seq = sequence.getAndIncrement();
		if (seq > MAX_SEQUENCE) {
			sequence.set(0); // 重置序列号
			try {
				Thread.sleep(1); // 等待下一毫秒
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
			now = System.currentTimeMillis();
			seq = sequence.getAndIncrement();
		}
		SimpleDateFormat formatter = formatterThreadLocal.get();
		String s = String.format("%01d", seq);
		return Long.parseLong(formatter.format(new Date(now)) + s);
	}
	public static String getHMS(){
		LocalTime now = LocalTime.now();
		return now.getHour()+"-"+now.getMinute()+"-"+now.getSecond();
	}

}
