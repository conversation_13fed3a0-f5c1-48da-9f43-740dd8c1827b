package com.inxaiot.ruleengine.common.context;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 系统上下文服务实现
 * 提供规则执行时需要的基础技术信息和标识信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class SystemContextServiceImpl implements SystemContextService {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemContextServiceImpl.class);
    
    // 基础配置信息
    @Value("${rule.engine.id:rule-engine-1}")
    private String engineId;
    
    @Value("${rule.engine.region.id:region-001}")
    private String regionId;
    
    @Value("${rule.engine.building.id:building-A}")
    private String buildingId;
    
    @Value("${rule.engine.version:1.0.0}")
    private String engineVersion;
    
    @Value("${rule.engine.timezone:Asia/Shanghai}")
    private String timezone;
    
    @Value("${rule.engine.locale:zh_CN}")
    private String locale;
    
    @Value("${rule.engine.debug:false}")
    private boolean debugMode;
    
    // 技术配置存储
    private final Map<String, Object> technicalConfigs = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        logger.info("SystemContextService initialized - EngineId: {}, RegionId: {}, BuildingId: {}", 
                   engineId, regionId, buildingId);
        
        // 初始化一些默认的技术配置
        initDefaultConfigs();
    }
    
    /**
     * 初始化默认配置
     */
    private void initDefaultConfigs() {
        technicalConfigs.put("startupTime", System.currentTimeMillis());
        technicalConfigs.put("javaVersion", System.getProperty("java.version"));
        technicalConfigs.put("osName", System.getProperty("os.name"));
        technicalConfigs.put("maxMemory", Runtime.getRuntime().maxMemory());
    }
    
    @Override
    public String getEngineId() {
        return engineId;
    }
    
    @Override
    public String getRegionId() {
        return regionId;
    }
    
    @Override
    public String getBuildingId() {
        return buildingId;
    }
    
    @Override
    public String getEngineVersion() {
        return engineVersion;
    }
    
    @Override
    public String getTimezone() {
        return timezone;
    }
    
    @Override
    public String getLocale() {
        return locale;
    }
    
    @Override
    public boolean isDebugMode() {
        return debugMode;
    }
    
    @Override
    public Object getTechnicalConfig(String key) {
        return technicalConfigs.get(key);
    }
    
    @Override
    public void setTechnicalConfig(String key, Object value) {
        if (key != null && !key.trim().isEmpty()) {
            technicalConfigs.put(key, value);
            logger.debug("Updated technical config: {} = {}", key, value);
        }
    }
    
    @Override
    public Map<String, Object> getAllContext() {
        Map<String, Object> context = new ConcurrentHashMap<>();

        // 基础标识信息（处理null值）
        context.put("engineId", engineId != null ? engineId : "rule-engine-default");
        context.put("regionId", regionId != null ? regionId : "region-default");
        context.put("buildingId", buildingId != null ? buildingId : "building-default");
        context.put("engineVersion", engineVersion != null ? engineVersion : "1.0.0");
        context.put("timezone", timezone != null ? timezone : "Asia/Shanghai");
        context.put("locale", locale != null ? locale : "zh_CN");
        context.put("debugMode", debugMode);

        // 当前时间（每次调用都是最新的）
        context.put("currentTime", System.currentTimeMillis());

        // 技术配置信息
        context.putAll(technicalConfigs);

        return context;
    }
    
    @Override
    public void updateBasicConfig(Map<String, Object> config) {
        if (config == null || config.isEmpty()) {
            logger.warn("Received empty config update");
            return;
        }
        
        try {
            // 更新基础配置（如果提供的话）
            if (config.containsKey("engineId")) {
                this.engineId = String.valueOf(config.get("engineId"));
            }
            if (config.containsKey("regionId")) {
                this.regionId = String.valueOf(config.get("regionId"));
            }
            if (config.containsKey("buildingId")) {
                this.buildingId = String.valueOf(config.get("buildingId"));
            }
            if (config.containsKey("timezone")) {
                this.timezone = String.valueOf(config.get("timezone"));
            }
            if (config.containsKey("locale")) {
                this.locale = String.valueOf(config.get("locale"));
            }
            if (config.containsKey("debugMode")) {
                this.debugMode = Boolean.parseBoolean(String.valueOf(config.get("debugMode")));
            }
            
            // 更新技术配置
            config.entrySet().stream()
                .filter(entry -> !isBasicConfig(entry.getKey()))
                .forEach(entry -> technicalConfigs.put(entry.getKey(), entry.getValue()));
            
            logger.info("Basic config updated successfully. EngineId: {}, RegionId: {}, BuildingId: {}", 
                       engineId, regionId, buildingId);
            
        } catch (Exception e) {
            logger.error("Error updating basic config", e);
        }
    }
    
    /**
     * 检查是否为基础配置项
     */
    private boolean isBasicConfig(String key) {
        return "engineId".equals(key) || "regionId".equals(key) || "buildingId".equals(key) ||
               "timezone".equals(key) || "locale".equals(key) || "debugMode".equals(key);
    }
    
    @Override
    public void resetToDefaults() {
        logger.info("Resetting system context to defaults");
        
        // 重置为配置文件中的默认值
        // 这里可以重新读取配置文件，或者设置硬编码的默认值
        this.debugMode = false;
        
        // 清空技术配置，重新初始化
        technicalConfigs.clear();
        initDefaultConfigs();
        
        logger.info("System context reset completed");
    }
    
    /**
     * 获取系统运行统计信息
     */
    public Map<String, Object> getSystemStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        
        Runtime runtime = Runtime.getRuntime();
        stats.put("totalMemory", runtime.totalMemory());
        stats.put("freeMemory", runtime.freeMemory());
        stats.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        stats.put("maxMemory", runtime.maxMemory());
        stats.put("availableProcessors", runtime.availableProcessors());
        
        Long startupTime = (Long) technicalConfigs.get("startupTime");
        if (startupTime != null) {
            stats.put("uptime", System.currentTimeMillis() - startupTime);
        }
        
        return stats;
    }
}
