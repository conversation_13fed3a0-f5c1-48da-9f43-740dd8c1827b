package com.inxaiot.ruleengine.common;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作日工具类
 * 提供工作日相关的转换和判断功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class WorkDayUtils {
    
    /**
     * 短格式工作日名称映射
     */
    private static final Map<String, DayOfWeek> SHORT_NAME_MAP = new HashMap<>();
    
    /**
     * 长格式工作日名称映射
     */
    private static final Map<String, DayOfWeek> LONG_NAME_MAP = new HashMap<>();
    
    /**
     * DayOfWeek到短格式名称的映射
     */
    private static final Map<DayOfWeek, String> DAY_TO_SHORT_NAME = new HashMap<>();
    
    /**
     * DayOfWeek到长格式名称的映射
     */
    private static final Map<DayOfWeek, String> DAY_TO_LONG_NAME = new HashMap<>();
    
    static {
        // 初始化短格式映射
        SHORT_NAME_MAP.put("MON", DayOfWeek.MONDAY);
        SHORT_NAME_MAP.put("TUE", DayOfWeek.TUESDAY);
        SHORT_NAME_MAP.put("WED", DayOfWeek.WEDNESDAY);
        SHORT_NAME_MAP.put("THU", DayOfWeek.THURSDAY);
        SHORT_NAME_MAP.put("FRI", DayOfWeek.FRIDAY);
        SHORT_NAME_MAP.put("SAT", DayOfWeek.SATURDAY);
        SHORT_NAME_MAP.put("SUN", DayOfWeek.SUNDAY);
        
        // 初始化长格式映射
        LONG_NAME_MAP.put("MONDAY", DayOfWeek.MONDAY);
        LONG_NAME_MAP.put("TUESDAY", DayOfWeek.TUESDAY);
        LONG_NAME_MAP.put("WEDNESDAY", DayOfWeek.WEDNESDAY);
        LONG_NAME_MAP.put("THURSDAY", DayOfWeek.THURSDAY);
        LONG_NAME_MAP.put("FRIDAY", DayOfWeek.FRIDAY);
        LONG_NAME_MAP.put("SATURDAY", DayOfWeek.SATURDAY);
        LONG_NAME_MAP.put("SUNDAY", DayOfWeek.SUNDAY);
        
        // 初始化反向映射
        DAY_TO_SHORT_NAME.put(DayOfWeek.MONDAY, "MON");
        DAY_TO_SHORT_NAME.put(DayOfWeek.TUESDAY, "TUE");
        DAY_TO_SHORT_NAME.put(DayOfWeek.WEDNESDAY, "WED");
        DAY_TO_SHORT_NAME.put(DayOfWeek.THURSDAY, "THU");
        DAY_TO_SHORT_NAME.put(DayOfWeek.FRIDAY, "FRI");
        DAY_TO_SHORT_NAME.put(DayOfWeek.SATURDAY, "SAT");
        DAY_TO_SHORT_NAME.put(DayOfWeek.SUNDAY, "SUN");
        
        DAY_TO_LONG_NAME.put(DayOfWeek.MONDAY, "MONDAY");
        DAY_TO_LONG_NAME.put(DayOfWeek.TUESDAY, "TUESDAY");
        DAY_TO_LONG_NAME.put(DayOfWeek.WEDNESDAY, "WEDNESDAY");
        DAY_TO_LONG_NAME.put(DayOfWeek.THURSDAY, "THURSDAY");
        DAY_TO_LONG_NAME.put(DayOfWeek.FRIDAY, "FRIDAY");
        DAY_TO_LONG_NAME.put(DayOfWeek.SATURDAY, "SATURDAY");
        DAY_TO_LONG_NAME.put(DayOfWeek.SUNDAY, "SUNDAY");
    }
    
    /**
     * 将字符串工作日列表转换为DayOfWeek集合
     * 
     * @param workDayStrings 工作日字符串列表，支持短格式（MON）和长格式（MONDAY）
     * @return DayOfWeek集合
     */
    public static Set<DayOfWeek> parseWorkDays(List<String> workDayStrings) {
        if (workDayStrings == null || workDayStrings.isEmpty()) {
            return EnumSet.noneOf(DayOfWeek.class);
        }
        
        Set<DayOfWeek> workDays = EnumSet.noneOf(DayOfWeek.class);
        
        for (String dayStr : workDayStrings) {
            if (dayStr == null || dayStr.trim().isEmpty()) {
                continue;
            }
            
            String upperDayStr = dayStr.trim().toUpperCase();
            DayOfWeek dayOfWeek = null;
            
            // 先尝试短格式
            if (SHORT_NAME_MAP.containsKey(upperDayStr)) {
                dayOfWeek = SHORT_NAME_MAP.get(upperDayStr);
            }
            // 再尝试长格式
            else if (LONG_NAME_MAP.containsKey(upperDayStr)) {
                dayOfWeek = LONG_NAME_MAP.get(upperDayStr);
            }
            
            if (dayOfWeek != null) {
                workDays.add(dayOfWeek);
            }
        }
        
        return workDays;
    }
    
    /**
     * 将DayOfWeek集合转换为短格式字符串列表
     * 
     * @param workDays DayOfWeek集合
     * @return 短格式字符串列表
     */
    public static List<String> toShortNames(Set<DayOfWeek> workDays) {
        if (workDays == null || workDays.isEmpty()) {
            return new ArrayList<>();
        }
        
        return workDays.stream()
                .map(DAY_TO_SHORT_NAME::get)
                .collect(Collectors.toList());
    }
    
    /**
     * 将DayOfWeek集合转换为长格式字符串列表
     * 
     * @param workDays DayOfWeek集合
     * @return 长格式字符串列表
     */
    public static List<String> toLongNames(Set<DayOfWeek> workDays) {
        if (workDays == null || workDays.isEmpty()) {
            return new ArrayList<>();
        }
        
        return workDays.stream()
                .map(DAY_TO_LONG_NAME::get)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查指定日期是否为工作日
     * 
     * @param date 要检查的日期
     * @param workDayStrings 工作日字符串列表
     * @return 如果是工作日返回true，否则返回false
     */
    public static boolean isWorkDay(LocalDate date, List<String> workDayStrings) {
        if (date == null || workDayStrings == null || workDayStrings.isEmpty()) {
            return false;
        }
        
        Set<DayOfWeek> workDays = parseWorkDays(workDayStrings);
        return workDays.contains(date.getDayOfWeek());
    }
    
    /**
     * 获取标准工作日（周一到周五）
     * 
     * @return 标准工作日的DayOfWeek集合
     */
    public static Set<DayOfWeek> getStandardWorkDays() {
        return EnumSet.of(
            DayOfWeek.MONDAY,
            DayOfWeek.TUESDAY,
            DayOfWeek.WEDNESDAY,
            DayOfWeek.THURSDAY,
            DayOfWeek.FRIDAY
        );
    }
    
    /**
     * 获取标准工作日的短格式字符串列表
     * 
     * @return 标准工作日的短格式字符串列表
     */
    public static List<String> getStandardWorkDayShortNames() {
        return Arrays.asList("MON", "TUE", "WED", "THU", "FRI");
    }
    
    /**
     * 获取标准工作日的长格式字符串列表
     * 
     * @return 标准工作日的长格式字符串列表
     */
    public static List<String> getStandardWorkDayLongNames() {
        return Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY");
    }
    
    /**
     * 获取周末（周六和周日）
     * 
     * @return 周末的DayOfWeek集合
     */
    public static Set<DayOfWeek> getWeekends() {
        return EnumSet.of(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY);
    }
    
    /**
     * 获取周末的短格式字符串列表
     * 
     * @return 周末的短格式字符串列表
     */
    public static List<String> getWeekendShortNames() {
        return Arrays.asList("SAT", "SUN");
    }
    
    /**
     * 检查指定日期是否为周末
     * 
     * @param date 要检查的日期
     * @return 如果是周末返回true，否则返回false
     */
    public static boolean isWeekend(LocalDate date) {
        if (date == null)return false;

        
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
    }
    
    /**
     * 验证工作日字符串是否有效
     * 
     * @param workDayString 工作日字符串
     * @return 如果有效返回true，否则返回false
     */
    public static boolean isValidWorkDayString(String workDayString) {
        if (workDayString == null || workDayString.trim().isEmpty()) {
            return false;
        }
        
        String upperDayStr = workDayString.trim().toUpperCase();
        return SHORT_NAME_MAP.containsKey(upperDayStr) || LONG_NAME_MAP.containsKey(upperDayStr);
    }
    
    /**
     * 获取所有支持的工作日格式示例
     * 
     * @return 格式示例字符串
     */
    public static String getSupportedFormats() {
        return "支持的格式：短格式（MON, TUE, WED, THU, FRI, SAT, SUN）或长格式（MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY）";
    }
}
