package com.inxaiot.ruleengine.common.operator;

import java.util.Map;

import static com.inxaiot.ruleengine.common.operator.Operators.DataTypes.STRING;

/**
 * 统一操作符常量定义
 * 按类别组织所有操作符常量，避免重复定义
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public final class Operators {
    
    /**
     * 基础比较操作符
     */
    public static final class Basic {
        public static final String EQUALS = "EQUALS";
        public static final String NOT_EQUALS = "NOT_EQUALS";
        public static final String GREATER_THAN = "GREATER_THAN";
        public static final String GREATER_THAN_OR_EQUAL = "GREATER_THAN_OR_EQUAL";
        public static final String LESS_THAN = "LESS_THAN";
        public static final String LESS_THAN_OR_EQUAL = "LESS_THAN_OR_EQUAL";
        
        private Basic() {}
    }
    
    /**
     * 字符串操作符
     */
    public static final class Text {
        public static final String CONTAINS = "CONTAINS";
        public static final String NOT_CONTAINS = "NOT_CONTAINS";
        public static final String STARTS_WITH = "STARTS_WITH";
        public static final String ENDS_WITH = "ENDS_WITH";
        
        private Text() {}
    }
    
    /**
     * 范围操作符
     */
    public static final class Range {
        public static final String BETWEEN = "BETWEEN";
        public static final String NOT_BETWEEN = "NOT_BETWEEN";
        public static final String IN = "IN";
        public static final String NOT_IN = "NOT_IN";
        
        private Range() {}
    }
    /**
     * 数据类型
     */
    public static final class DataTypes {
        public static final String STRING = "STRING";
        public static final String INTEGER = "INTEGER";
        public static final String DOUBLE = "DOUBLE";
        public static final String FLOAT = "FLOAT";
        public static final String BOOLEAN = "BOOLEAN";
        public static final String LONG = "LONG";
        public static final String JSON = "JSON";

        private DataTypes() {}
    }
    /**
     * 推断数据类型
     */
    public static  String inferDataType(Object value) {
        if (value == null) {
            return DataTypes.STRING;
        }

        if (value instanceof Boolean) {
            return DataTypes.BOOLEAN;
        } else if (value instanceof Integer || value instanceof Long) {
            return DataTypes.INTEGER;
        } else if (value instanceof Double || value instanceof Float) {
            return DataTypes.DOUBLE;
        } else if (value instanceof String) {
            String strValue = (String) value;
            // 尝试推断字符串的实际类型
            if ("true".equalsIgnoreCase(strValue) || "false".equalsIgnoreCase(strValue)) {
                return DataTypes.BOOLEAN;
            }
            try {
                if (strValue.contains(".")) {
                    Double.parseDouble(strValue);
                    return DataTypes.DOUBLE;
                } else {
                    Long.parseLong(strValue);
                    return DataTypes.INTEGER;
                }
            } catch (NumberFormatException e) {
                return DataTypes.STRING;
            }
        } else if (value instanceof Map || value.toString().startsWith("{")) {
            return DataTypes.JSON;
        } else {
            return DataTypes.STRING;
        }
    }
    /**
     * 逻辑操作符
     */
    public enum Logic {
        /**
         * 所有条件都必须满足
         */
        AND,
        /**
         * 任意条件满足即可
         */
        OR
    }
    /**
     * 时间相关操作符
     */
    public static final class Duration {
        public static final String STATES_KEEP_SECONDS = "STATES_KEEP_SECONDS";
        
        private Duration() {}
    }
    
    private Operators() {
        // 工具类，禁止实例化
    }
}
