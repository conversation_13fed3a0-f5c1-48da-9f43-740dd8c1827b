<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle != null ? pageTitle + ' - 规则引擎管理' : '规则引擎管理'}">规则引擎管理</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .sidebar .nav-link {
            color: #495057;
            border-radius: 0.375rem;
            margin: 0.125rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
        .main-content {
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .status-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web">
                <i class="bi bi-gear-fill me-2"></i>
                规则引擎管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-person-circle me-1"></i>
                    管理员
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-md-block sidebar p-3">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#request.requestURI.contains('/rules') ? 'active' : ''}" 
                               href="/web/rules">
                                <i class="bi bi-list-ul me-2"></i>
                                规则管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#request.requestURI.contains('/monitor') ? 'active' : ''}" 
                               href="/web/monitor">
                                <i class="bi bi-graph-up me-2"></i>
                                系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#request.requestURI.contains('/config') ? 'active' : ''}" 
                               href="/web/config">
                                <i class="bi bi-gear me-2"></i>
                                系统配置
                            </a>
                        </li>
                        <hr>
                        <li class="nav-item">
                            <a class="nav-link" href="/api/engine/rule" target="_blank">
                                <i class="bi bi-code-square me-2"></i>
                                API文档
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="p-4">
                    <!-- 面包屑导航 -->
                    <nav aria-label="breadcrumb" th:if="${pageTitle}">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/web">首页</a></li>
                            <li class="breadcrumb-item active" aria-current="page" th:text="${pageTitle}">当前页面</li>
                        </ol>
                    </nav>

                    <!-- 页面标题 -->
                    <div class="d-flex justify-content-between align-items-center mb-4" th:if="${pageTitle}">
                        <h2 th:text="${pageTitle}">页面标题</h2>
                    </div>

                    <!-- 错误信息显示 -->
                    <div class="alert alert-danger" role="alert" th:if="${error}">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <span th:text="${error}">错误信息</span>
                    </div>

                    <!-- 成功信息显示 -->
                    <div class="alert alert-success" role="alert" th:if="${success}">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <span th:text="${success}">成功信息</span>
                    </div>

                    <!-- 页面内容区域 -->
                    <div th:replace="${contentTemplate ?: 'fragments/default-content'}">
                        <!-- 页面具体内容将在这里替换 -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.0/dist/jquery.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 全局AJAX设置
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                // 显示加载状态
                if (settings.showLoading !== false) {
                    showLoading();
                }
            },
            complete: function() {
                // 隐藏加载状态
                hideLoading();
            },
            error: function(xhr, status, error) {
                hideLoading();
                showAlert('操作失败: ' + (xhr.responseJSON?.message || error), 'danger');
            }
        });

        // 显示加载状态
        function showLoading() {
            if (!$('#loadingModal').length) {
                $('body').append(`
                    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
                        <div class="modal-dialog modal-sm modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-body text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <div class="mt-2">处理中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
            }
            $('#loadingModal').modal('show');
        }

        // 隐藏加载状态
        function hideLoading() {
            $('#loadingModal').modal('hide');
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle-fill' : type === 'danger' ? 'exclamation-triangle-fill' : 'info-circle-fill'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.main-content .p-4').prepend(alertHtml);
            
            // 3秒后自动消失
            setTimeout(() => {
                $('.alert').fadeOut();
            }, 3000);
        }

        // 确认对话框
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }
    </script>
    
    <!-- 页面特定脚本 -->
    <th:block th:replace="${scriptTemplate ?: ''}"></th:block>
</body>
</html>
