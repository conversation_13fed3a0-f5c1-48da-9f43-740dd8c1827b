-- 物联网规则引擎数据库表结构
-- 数据库: SQLite
-- 创建时间: 2024-12-19

-- 规则定义表
CREATE TABLE IF NOT EXISTS rule_definition (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name VARCHAR(255),
    biz_id VARCHAR(64),
    group_id VARCHAR(64),
    priority INTEGER DEFAULT 1,
    enabled BOOLEAN DEFAULT 1,
    time_conditions TEXT,  -- JSON格式存储时间条件
    logic VARCHAR(16) DEFAULT 'AND',  -- 时间条件和设备条件(组)的逻辑关系
    trigger_conditions TEXT,  -- JSON格式存储触发条件
    actions TEXT,  -- JSON格式存储动作列表
    deactivation_actions TEXT,  -- JSON格式存储失活动作列表
    description TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_rule_definition_biz_id ON rule_definition(biz_id);
CREATE INDEX IF NOT EXISTS idx_rule_definition_group_id ON rule_definition(group_id);
CREATE INDEX IF NOT EXISTS idx_rule_definition_enabled ON rule_definition(enabled);

-- 全局日历表（单记录设计）
CREATE TABLE IF NOT EXISTS global_calendar (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    biz_id VARCHAR(64) DEFAULT 'default',  -- 业务ID，预留扩展
    exclude_month_days TEXT,  -- JSON格式存储排除月日列表
    month_date_ranges TEXT,  -- JSON格式存储命名时间段定义
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_global_calendar_biz_id ON global_calendar(biz_id);


-- 规则执行日志表
CREATE TABLE IF NOT EXISTS rule_execution_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_id VARCHAR(64) NOT NULL,
    execution_id VARCHAR(64) NOT NULL,
    trigger_device_id VARCHAR(64),
    trigger_point_id VARCHAR(64),
    trigger_value TEXT,
    execution_status VARCHAR(16),  -- SUCCESS, FAILED, TIMEOUT
    execution_result TEXT,
    error_message TEXT,
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    duration_ms INTEGER
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_rule_execution_log_rule_id ON rule_execution_log(rule_id);
CREATE INDEX IF NOT EXISTS idx_rule_execution_log_execution_time ON rule_execution_log(execution_time);
CREATE INDEX IF NOT EXISTS idx_rule_execution_log_status ON rule_execution_log(execution_status);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(64) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(16) DEFAULT 'STRING',  -- STRING, INTEGER, DOUBLE, BOOLEAN, JSON
    description TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);

-- 插入默认系统配置
INSERT OR IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES
('rule.engine.version', '1.0.0', 'STRING', '规则引擎版本'),
('rule.engine.timezone', 'Asia/Shanghai', 'STRING', '系统时区'),
('rule.engine.max_rules', '5000', 'INTEGER', '最大规则数量'),
('rule.engine.execution.timeout', '30', 'INTEGER', '规则执行超时时间(秒)'),
('rule.engine.state.cleanup.interval', '60', 'INTEGER', '状态清理间隔(分钟)'),
('calendar.refresh.interval', '24', 'INTEGER', '日历刷新间隔(小时)');

-- 插入默认全局日历数据 (示例)
