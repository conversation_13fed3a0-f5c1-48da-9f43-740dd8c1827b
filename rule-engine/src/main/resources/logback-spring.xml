<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 定义日志路径变量 -->
    <springProperty scope="context" name="LOG_PATH" source="rule.engine.log.path" defaultValue="logs"/>

    <!-- 主日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/rule-engine.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/rule-engine.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 动作执行专用日志文件 -->
    <appender name="ACTION_EXECUTION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/action-execution.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/action-execution.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 动作执行日志记录器 - 专门输出到action-execution.log -->
    <logger name="com.inxaiot.ruleengine.core.action.ActionExecutionLogger" level="INFO" additivity="false">
        <appender-ref ref="ACTION_EXECUTION_FILE"/>
    </logger>

    <!-- 规则引擎相关日志 -->
    <logger name="com.inxaiot.ruleengine" level="INFO"/>

    <!-- 动作执行相关日志 -->
    <logger name="com.inxaiot.ruleengine.core.action" level="INFO"/>

    <!-- MQTT相关日志 -->
    <logger name="com.inxaiot.ruleengine.transport.mqtt" level="INFO"/>

    <!-- 状态管理相关日志 -->
    <logger name="com.inxaiot.ruleengine.device.state" level="INFO"/>

    <!-- Easy Rules框架日志 -->
    <logger name="org.jeasy.rules" level="WARN"/>

    <!-- Spring框架日志 -->
    <logger name="org.springframework" level="INFO"/>

    <!-- 数据库相关日志 -->
    <logger name="org.mybatis" level="INFO"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>

    <!-- 根日志记录器 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
    
</configuration>
