/* 规则引擎管理系统 - 自定义样式 */

/* 侧边栏样式 */
.sidebar {
    min-height: 100vh;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
}

.sidebar .nav-link {
    color: #495057;
    border-radius: 0.375rem;
    margin: 0.125rem 0;
    transition: all 0.2s ease-in-out;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: #0d6efd;
    color: white;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

/* 主内容区域 */
.main-content {
    min-height: 100vh;
    background-color: #ffffff;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75rem;
}

.status-enabled {
    background-color: #198754;
}

.status-disabled {
    background-color: #6c757d;
}



/* 表格样式 */
.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* 操作按钮组 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 搜索框样式 */
.search-box {
    max-width: 300px;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card.danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}



/* 代码块样式 */
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    white-space: pre-wrap;
    word-break: break-all;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* JSON编辑器样式 */
.json-editor {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.75rem;
    resize: vertical;
}

.json-editor:focus {
    background-color: #ffffff;
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* 浮动提示层样式 - 保持原Alert风格 */
.float-alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    min-width: 300px;
}

.float-alert {
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 0.375rem;
    animation: slideInRight 0.3s ease-out;
}

/* 保持原来的Alert样式 */
.float-alert.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border: 1px solid #badbcc;
}

.float-alert.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border: 1px solid #f5c2c7;
}

.float-alert.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border: 1px solid #ffecb5;
}

.float-alert.alert-info {
    color: #055160;
    background-color: #d1ecf1;
    border: 1px solid #b6effb;
}

.float-alert .btn-close {
    padding: 0.5rem 0.5rem;
    margin: -0.25rem -0.25rem -0.25rem auto;
}

/* 滑入动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 淡出动画 */
@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.float-alert.fade-out {
    animation: fadeOut 0.3s ease-in forwards;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }

    .main-content {
        min-height: auto;
    }

    .stat-card .stat-number {
        font-size: 1.5rem;
    }

    .float-alert-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        min-width: auto;
    }
}
