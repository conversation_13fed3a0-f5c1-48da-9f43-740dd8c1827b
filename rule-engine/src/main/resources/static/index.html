<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则引擎管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/app.css" rel="stylesheet">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-gear-fill me-2"></i>
                规则引擎管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-person-circle me-1"></i>
                    管理员
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-md-block sidebar p-3">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-page="rules">
                                <i class="bi bi-list-ul me-2"></i>
                                规则管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-page="monitor">
                                <i class="bi bi-graph-up me-2"></i>
                                系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-page="config">
                                <i class="bi bi-gear me-2"></i>
                                系统配置
                            </a>
                        </li>
                        <hr>
                        <li class="nav-item">
                            <a class="nav-link" href="/api/engine/rule" target="_blank">
                                <i class="bi bi-code-square me-2"></i>
                                API接口
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="p-4">
                    <!-- 面包屑导航 -->
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/">首页</a></li>
                            <li class="breadcrumb-item active" id="current-page">规则管理</li>
                        </ol>
                    </nav>

                    <!-- 页面标题 -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 id="page-title">规则管理</h2>
                        <div id="page-actions">
                            <!-- 页面操作按钮将在这里动态添加 -->
                        </div>
                    </div>



                    <!-- 页面内容区域 -->
                    <div id="page-content">
                        <!-- 页面具体内容将在这里动态加载 -->
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载页面...</div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 浮动提示容器 -->
    <div class="float-alert-container" id="float-alert-container"></div>

    <!-- 通用模态框 -->
    <div class="modal fade" id="commonModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">标题</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 模态框内容 -->
                </div>
                <div class="modal-footer" id="modalFooter">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>



    <!-- JavaScript -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.0/dist/jquery.min.js"></script>
    <!-- Chart.js (可选) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/app.js"></script>

    <!-- 初始化脚本 -->
    <script>
        $(document).ready(function() {
            console.log('页面初始化完成');
        });
    </script>
</body>
</html>
