/**
 * 主应用逻辑
 * 处理页面路由和主要业务逻辑
 */

// 全局应用对象
window.App = {
    
    // 当前页面
    currentPage: 'rules',
    
    // 页面配置
    pages: {
        rules: {
            title: '规则管理',
            loadFunction: 'loadRulesPage'
        },
        monitor: {
            title: '系统监控',
            loadFunction: 'loadMonitorPage'
        },
        config: {
            title: '系统配置',
            loadFunction: 'loadConfigPage'
        }
    },
    
    /**
     * 应用初始化
     */
    init: function() {
        console.log('应用初始化开始');

        this.bindEvents();
        this.loadPage('rules');

        console.log('应用初始化完成');
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 侧边栏导航点击事件
        $('.sidebar .nav-link[data-page]').on('click', (e) => {
            e.preventDefault();
            const page = $(e.currentTarget).data('page');
            this.loadPage(page);
        });
        
        // 全局键盘事件
        $(document).on('keydown', (e) => {
            // ESC键关闭模态框
            if (e.key === 'Escape') {
                $('.modal').modal('hide');
            }
        });
    },
    
    /**
     * 加载页面
     * @param {string} pageName 页面名称
     */
    loadPage: function(pageName) {
        if (!this.pages[pageName]) {
            Utils.showMessage('页面不存在', 'danger');
            return;
        }



        // 清理之前页面的定时器
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }

        // 更新当前页面
        this.currentPage = pageName;

        // 更新导航状态
        $('.sidebar .nav-link').removeClass('active');
        $(`.sidebar .nav-link[data-page="${pageName}"]`).addClass('active');

        // 更新页面标题
        const pageConfig = this.pages[pageName];
        $('#page-title').text(pageConfig.title);
        $('#current-page').text(pageConfig.title);

        // 清空页面内容并显示页面级加载状态
        $('#page-content').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><div class="mt-2">正在加载页面...</div></div>');
        $('#page-actions').empty();

        // 加载页面内容
        if (this[pageConfig.loadFunction]) {
            this[pageConfig.loadFunction]();
        }
    },
    
    /**
     * 加载规则管理页面
     */
    loadRulesPage: function() {
        // 添加页面操作按钮
        $('#page-actions').html(`
            <button type="button" class="btn btn-primary" onclick="App.showCreateRuleModal()">
                <i class="bi bi-plus-lg me-1"></i>创建规则
            </button>
        `);
        
        // 加载规则列表
        API.getRules().done((response) => {
            if (response.success) {
                this.renderRulesList(response.data);
            } else {
                Utils.showMessage('加载规则列表失败: ' + response.message, 'danger');
            }



        }).fail(() => {
            // 失败时显示空状态
            $('#page-content').html(`
                <div class="empty-state">
                    <i class="bi bi-exclamation-triangle"></i>
                    <h5>加载失败</h5>
                    <p class="text-muted">无法加载规则列表，请检查网络连接或稍后重试</p>
                    <button class="btn btn-primary" onclick="App.loadRulesPage()">重新加载</button>
                </div>
            `);


        });
    },
    
    /**
     * 渲染规则列表
     * @param {array} rules 规则数组
     */
    renderRulesList: function(rules) {
        if (!rules || rules.length === 0) {
            $('#page-content').html(`
                <div class="empty-state">
                    <i class="bi bi-inbox"></i>
                    <h5>暂无规则</h5>
                    <p class="text-muted">点击"创建规则"按钮开始创建您的第一个规则</p>
                </div>
            `);
            return;
        }
        
        let html = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group search-box">
                        <input type="text" class="form-control" placeholder="搜索规则..." id="searchInput">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="App.batchEnableRules()">
                            <i class="bi bi-check-circle me-1"></i>批量启用
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="App.batchDisableRules()">
                            <i class="bi bi-x-circle me-1"></i>批量禁用
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="App.batchDeleteRules()">
                            <i class="bi bi-trash me-1"></i>批量删除
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>规则名称</th>
                            <th>业务ID</th>
                            <th>分组ID</th>
                            <th>优先级</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th width="200">操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        rules.forEach(rule => {
            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input rule-checkbox" value="${rule.ruleId}">
                    </td>
                    <td>
                        <strong>${Utils.escapeHtml(rule.ruleName || '-')}</strong>
                        <br>
                        <small class="text-muted">${Utils.truncateText(rule.description || '', 50)}</small>
                    </td>
                    <td><code>${Utils.escapeHtml(rule.bizId || '-')}</code></td>
                    <td><code>${Utils.escapeHtml(rule.groupId || '-')}</code></td>
                    <td><span class="badge bg-info">${rule.priority || 1}</span></td>
                    <td>${Utils.getStatusBadge(rule.enabled)}</td>
                    <td>${Utils.formatDateTime(rule.createTime)}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="App.viewRule(${rule.ruleId})" title="查看">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="App.editRule(${rule.ruleId})" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-${rule.enabled ? 'secondary' : 'success'}" 
                                    onclick="App.toggleRule(${rule.ruleId}, ${!rule.enabled})" 
                                    title="${rule.enabled ? '禁用' : '启用'}">
                                <i class="bi bi-${rule.enabled ? 'pause' : 'play'}"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="App.deleteRule(${rule.ruleId})" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        $('#page-content').html(html);
        
        // 绑定搜索事件
        $('#searchInput').on('input', Utils.debounce(this.filterRules, 300));
        
        // 绑定全选事件
        $('#selectAll').on('change', function() {
            $('.rule-checkbox').prop('checked', $(this).prop('checked'));
        });
    },
    
    /**
     * 过滤规则
     */
    filterRules: function() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        $('tbody tr').each(function() {
            const text = $(this).text().toLowerCase();
            $(this).toggle(text.includes(searchTerm));
        });
    },
    
    /**
     * 查看规则详情
     * @param {number} ruleId 规则ID
     */
    viewRule: function(ruleId) {
        $.ajax({
            url: `/api/engine/rule/${ruleId}`,
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.showRuleDetailModal(response.data);
                } else {
                    Utils.showMessage('获取规则详情失败: ' + response.message, 'danger');
                }
            },
            error: (xhr, status, error) => {
                Utils.showMessage('获取规则详情失败', 'danger');
            }
        });
    },
    
    /**
     * 显示规则详情模态框
     * @param {object} rule 规则对象
     */
    showRuleDetailModal: function(rule) {
        $('#modalTitle').text('规则详情');
        $('#modalBody').html(`
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>规则名称:</td><td>${Utils.escapeHtml(rule.ruleName || '-')}</td></tr>
                        <tr><td>业务ID:</td><td><code>${Utils.escapeHtml(rule.bizId || '-')}</code></td></tr>
                        <tr><td>分组ID:</td><td><code>${Utils.escapeHtml(rule.groupId || '-')}</code></td></tr>
                        <tr><td>优先级:</td><td>${rule.priority || 1}</td></tr>
                        <tr><td>状态:</td><td>${Utils.getStatusBadge(rule.enabled)}</td></tr>
                        <tr><td>创建时间:</td><td>${Utils.formatDateTime(rule.createTime)}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>描述</h6>
                    <p>${Utils.escapeHtml(rule.description || '无描述')}</p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6>时间条件</h6>
                    <pre class="code-block">${Utils.formatJSON(rule.timeConditions || '{}')}</pre>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6>触发条件</h6>
                    <pre class="code-block">${Utils.formatJSON(rule.triggerCondition || '{}')}</pre>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6>执行动作</h6>
                    <pre class="code-block">${Utils.formatJSON(rule.actions || '[]')}</pre>
                </div>
            </div>
        `);
        $('#modalFooter').html(`
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            <button type="button" class="btn btn-primary" onclick="App.editRule(${rule.ruleId}); $('#commonModal').modal('hide');">编辑规则</button>
        `);
        $('#commonModal').modal('show');
    },

    /**
     * 显示创建规则模态框
     */
    showCreateRuleModal: function() {
        this.showRuleFormModal(null, false);
    },

    /**
     * 编辑规则
     * @param {number} ruleId 规则ID
     */
    editRule: function(ruleId) {
        $.ajax({
            url: `/api/engine/rule/${ruleId}`,
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.showRuleFormModal(response.data, true);
                } else {
                    Utils.showMessage('获取规则详情失败: ' + response.message, 'danger');
                }
            },
            error: () => {
                Utils.showMessage('获取规则详情失败', 'danger');
            }
        });
    },

    /**
     * 显示规则表单模态框
     * @param {object} rule 规则对象，null表示创建新规则
     * @param {boolean} isEdit 是否为编辑模式
     */
    showRuleFormModal: function(rule, isEdit) {
        const title = isEdit ? '编辑规则' : '创建规则';
        const submitText = isEdit ? '更新规则' : '创建规则';

        // 初始化表单数据
        const formData = rule ? {
            ruleId: rule.ruleId,
            ruleName: rule.ruleName || '',
            bizId: rule.bizId || '',
            groupId: rule.groupId || '',
            priority: rule.priority || 1,
            enabled: rule.enabled !== false,
            logic: rule.logic || 'AND',
            description: rule.description || '',
            timeConditions: rule.timeConditions ? JSON.stringify(rule.timeConditions, null, 2) : '[]',
            triggerCondition: rule.triggerCondition ? JSON.stringify(rule.triggerCondition, null, 2) : '{}',
            actions: rule.actions ? JSON.stringify(rule.actions, null, 2) : '[]',
            deactivationActions: rule.deactivationActions ? JSON.stringify(rule.deactivationActions, null, 2) : '[]'
        } : {
            ruleId: null,
            ruleName: '',
            bizId: '',
            groupId: '',
            priority: 1,
            enabled: true,
            logic: 'AND',
            description: '',
            timeConditions: '[]',
            triggerCondition: '{}',
            actions: '[]',
            deactivationActions: '[]'
        };

        $('#modalTitle').text(title);
        $('#modalBody').html(this.getRuleFormHtml(formData, isEdit));
        $('#modalFooter').html(`
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="App.submitRuleForm(${isEdit})">${submitText}</button>
        `);
        $('#commonModal').modal('show');
    },

    /**
     * 生成规则表单HTML
     * @param {object} formData 表单数据
     * @param {boolean} isEdit 是否为编辑模式
     */
    getRuleFormHtml: function(formData, isEdit) {
        return `
            <form id="ruleForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="ruleName" class="form-label">规则名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="ruleName" value="${Utils.escapeHtml(formData.ruleName)}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="bizId" class="form-label">业务ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="bizId" value="${Utils.escapeHtml(formData.bizId)}" ${isEdit ? 'readonly' : ''} required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="groupId" class="form-label">分组ID</label>
                            <input type="text" class="form-control" id="groupId" value="${Utils.escapeHtml(formData.groupId)}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="priority" class="form-label">优先级</label>
                            <input type="number" class="form-control" id="priority" value="${formData.priority}" min="1" max="100">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="logic" class="form-label">逻辑关系</label>
                            <select class="form-select" id="logic">
                                <option value="AND" ${formData.logic === 'AND' ? 'selected' : ''}>AND (且)</option>
                                <option value="OR" ${formData.logic === 'OR' ? 'selected' : ''}>OR (或)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enabled" ${formData.enabled ? 'checked' : ''}>
                                <label class="form-check-label" for="enabled">
                                    启用规则
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">描述</label>
                    <textarea class="form-control" id="description" rows="2">${Utils.escapeHtml(formData.description)}</textarea>
                </div>

                <div class="mb-3">
                    <label for="timeConditions" class="form-label">时间条件 (JSON格式)</label>
                    <textarea class="form-control json-editor" id="timeConditions" rows="4">${formData.timeConditions}</textarea>
                    <div class="form-text">时间条件的JSON配置，例如：[{"type": "daily", "startTime": "08:00", "endTime": "18:00"}]</div>
                </div>

                <div class="mb-3">
                    <label for="triggerCondition" class="form-label">触发条件 (JSON格式)</label>
                    <textarea class="form-control json-editor" id="triggerCondition" rows="4">${formData.triggerCondition}</textarea>
                    <div class="form-text">设备触发条件的JSON配置</div>
                </div>

                <div class="mb-3">
                    <label for="actions" class="form-label">执行动作 (JSON格式) <span class="text-danger">*</span></label>
                    <textarea class="form-control json-editor" id="actions" rows="4" required>${formData.actions}</textarea>
                    <div class="form-text">规则激活时执行的动作列表</div>
                </div>

                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>注意：</strong>时间条件和触发条件至少需要配置一个
                </div>

                <div class="mb-3">
                    <label for="deactivationActions" class="form-label">停用动作 (JSON格式)</label>
                    <textarea class="form-control json-editor" id="deactivationActions" rows="4">${formData.deactivationActions}</textarea>
                    <div class="form-text">规则停用时执行的动作列表</div>
                </div>

                ${isEdit ? `<input type="hidden" id="ruleId" value="${formData.ruleId}">` : ''}
            </form>
        `;
    },

    /**
     * 提交规则表单
     * @param {boolean} isEdit 是否为编辑模式
     */
    submitRuleForm: function(isEdit) {
        // 获取表单数据
        const formData = {
            ruleName: $('#ruleName').val().trim(),
            bizId: $('#bizId').val().trim(),
            groupId: $('#groupId').val().trim() || null,
            priority: parseInt($('#priority').val()) || 1,
            enabled: $('#enabled').is(':checked'),
            logic: $('#logic').val(),
            description: $('#description').val().trim() || null,
            timeConditions: $('#timeConditions').val().trim(),
            triggerCondition: $('#triggerCondition').val().trim(),
            actions: $('#actions').val().trim(),
            deactivationActions: $('#deactivationActions').val().trim()
        };

        // 如果是编辑模式，添加ruleId
        if (isEdit) {
            formData.ruleId = parseInt($('#ruleId').val());
        }

        // 验证必填字段
        if (!formData.ruleName) {
            Utils.showMessage('请输入规则名称', 'warning');
            return;
        }

        if (!formData.bizId) {
            Utils.showMessage('请输入业务ID', 'warning');
            return;
        }

        // 验证和解析JSON字段
        let parsedTimeConditions = null;
        let parsedTriggerCondition = null;
        let parsedActions = null;
        let parsedDeactivationActions = null;

        try {
            // 解析时间条件
            if (formData.timeConditions && formData.timeConditions.trim() !== '[]') {
                parsedTimeConditions = JSON.parse(formData.timeConditions);
                if (Array.isArray(parsedTimeConditions) && parsedTimeConditions.length === 0) {
                    parsedTimeConditions = null;
                }
            }

            // 解析触发条件
            if (formData.triggerCondition && formData.triggerCondition.trim() !== '{}') {
                parsedTriggerCondition = JSON.parse(formData.triggerCondition);
                if (typeof parsedTriggerCondition === 'object' && Object.keys(parsedTriggerCondition).length === 0) {
                    parsedTriggerCondition = null;
                }
            }

            // 验证时间条件和触发条件至少有一个
            if (!parsedTimeConditions && !parsedTriggerCondition) {
                Utils.showMessage('时间条件和触发条件至少需要配置一个', 'warning');
                return;
            }

            // 解析执行动作
            if (!formData.actions) {
                Utils.showMessage('请输入执行动作', 'warning');
                return;
            }
            if (formData.actions.trim() !== '[]') {
                parsedActions = JSON.parse(formData.actions);
                if (Array.isArray(parsedActions) && parsedActions.length === 0) {
                    parsedActions = null;
                }
            }
            if (!parsedActions) {
                Utils.showMessage('请配置执行动作', 'warning');
                return;
            }

            // 解析停用动作
            if (formData.deactivationActions && formData.deactivationActions.trim() !== '[]') {
                parsedDeactivationActions = JSON.parse(formData.deactivationActions);
                if (Array.isArray(parsedDeactivationActions) && parsedDeactivationActions.length === 0) {
                    parsedDeactivationActions = null;
                }
            }

        } catch (e) {
            Utils.showMessage('JSON格式错误，请检查配置: ' + e.message, 'danger');
            return;
        }

        // 更新formData，只包含非空值
        if (parsedTimeConditions) {
            formData.timeConditions = parsedTimeConditions;
        } else {
            delete formData.timeConditions;
        }

        if (parsedTriggerCondition) {
            formData.triggerCondition = parsedTriggerCondition;
        } else {
            delete formData.triggerCondition;
        }

        formData.actions = parsedActions;

        if (parsedDeactivationActions) {
            formData.deactivationActions = parsedDeactivationActions;
        } else {
            delete formData.deactivationActions;
        }

        // 提交数据
        $.ajax({
            url: '/api/engine/rule',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: (response) => {
                if (response.success) {
                    Utils.showMessage(isEdit ? '规则更新成功' : '规则创建成功', 'success');
                    $('#commonModal').modal('hide');
                    this.loadRulesPage();
                } else {
                    Utils.showMessage((isEdit ? '规则更新失败: ' : '规则创建失败: ') + response.message, 'danger');
                }
            },
            error: (xhr) => {
                let message = isEdit ? '规则更新失败' : '规则创建失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message += ': ' + xhr.responseJSON.message;
                }
                Utils.showMessage(message, 'danger');
            }
        });
    },

    /**
     * 切换规则状态
     * @param {number} ruleId 规则ID
     * @param {boolean} enable 是否启用
     */
    toggleRule: function(ruleId, enable) {
        const action = enable ? '启用' : '禁用';
        const url = enable ? `/api/engine/rule/enable/${ruleId}` : `/api/engine/rule/disable/${ruleId}`;

        Utils.confirm(`确定要${action}这个规则吗？`, () => {
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                success: (response) => {
                    if (response.success) {
                        Utils.showMessage(`规则${action}成功`, 'success');
                        this.loadRulesPage();
                    } else {
                        Utils.showMessage(`规则${action}失败: ` + response.message, 'danger');
                    }
                },
                error: (xhr, status, error) => {
                    Utils.showMessage(`规则${action}失败`, 'danger');
                }
            });
        });
    },

    /**
     * 删除规则
     * @param {number} ruleId 规则ID
     */
    deleteRule: function(ruleId) {
        Utils.confirm('确定要删除这个规则吗？此操作不可恢复！', () => {
            $.ajax({
                url: `/api/engine/rule/${ruleId}`,
                type: 'DELETE',
                success: (response) => {
                    if (response.success) {
                        Utils.showMessage('规则删除成功', 'success');
                        this.loadRulesPage();
                    }
                },
                error: () => {
                    Utils.showMessage('规则删除失败', 'danger');
                }
            });
        });
    },

    /**
     * 批量启用规则
     */
    batchEnableRules: function() {
        const selectedIds = this.getSelectedRuleIds();
        if (selectedIds.length === 0) {
            Utils.showMessage('请选择要启用的规则', 'warning');
            return;
        }

        Utils.confirm(`确定要启用选中的 ${selectedIds.length} 个规则吗？`, () => {
            let completed = 0;
            let errors = 0;

            selectedIds.forEach(ruleId => {
                $.ajax({
                    url: `/api/engine/rule/enable/${ruleId}`,
                    type: 'POST',
                    dataType: 'json',
                    success: (response) => {
                        if (response.success) {
                            completed++;
                        } else {
                            errors++;
                        }
                        if (completed + errors === selectedIds.length) {
                            if (errors === 0) {
                                Utils.showMessage('批量启用成功', 'success');
                            } else {
                                Utils.showMessage(`批量启用完成，${completed}个成功，${errors}个失败`, 'warning');
                            }
                            this.loadRulesPage();
                        }
                    },
                    error: () => {
                        errors++;
                        if (completed + errors === selectedIds.length) {
                            Utils.showMessage(`批量启用完成，${completed}个成功，${errors}个失败`, 'warning');
                            this.loadRulesPage();
                        }
                    }
                });
            });
        });
    },

    /**
     * 批量禁用规则
     */
    batchDisableRules: function() {
        const selectedIds = this.getSelectedRuleIds();
        if (selectedIds.length === 0) {
            Utils.showMessage('请选择要禁用的规则', 'warning');
            return;
        }

        Utils.confirm(`确定要禁用选中的 ${selectedIds.length} 个规则吗？`, () => {
            let completed = 0;
            let errors = 0;

            selectedIds.forEach(ruleId => {
                $.ajax({
                    url: `/api/engine/rule/disable/${ruleId}`,
                    type: 'POST',
                    dataType: 'json',
                    success: (response) => {
                        if (response.success) {
                            completed++;
                        } else {
                            errors++;
                        }
                        if (completed + errors === selectedIds.length) {
                            if (errors === 0) {
                                Utils.showMessage('批量禁用成功', 'success');
                            } else {
                                Utils.showMessage(`批量禁用完成，${completed}个成功，${errors}个失败`, 'warning');
                            }
                            this.loadRulesPage();
                        }
                    },
                    error: () => {
                        errors++;
                        if (completed + errors === selectedIds.length) {
                            Utils.showMessage(`批量禁用完成，${completed}个成功，${errors}个失败`, 'warning');
                            this.loadRulesPage();
                        }
                    }
                });
            });
        });
    },

    /**
     * 批量删除规则
     */
    batchDeleteRules: function() {
        const selectedIds = this.getSelectedRuleIds();
        if (selectedIds.length === 0) {
            Utils.showMessage('请选择要删除的规则', 'warning');
            return;
        }

        Utils.confirm(`确定要删除选中的 ${selectedIds.length} 个规则吗？此操作不可恢复！`, () => {
            let completed = 0;
            let errors = 0;

            selectedIds.forEach(ruleId => {
                $.ajax({
                    url: `/api/engine/rule/${ruleId}`,
                    type: 'DELETE',
                    success: () => {
                        completed++;
                        if (completed + errors === selectedIds.length) {
                            if (errors === 0) {
                                Utils.showMessage('批量删除成功', 'success');
                            } else {
                                Utils.showMessage(`批量删除完成，${completed}个成功，${errors}个失败`, 'warning');
                            }
                            this.loadRulesPage();
                        }
                    },
                    error: () => {
                        errors++;
                        if (completed + errors === selectedIds.length) {
                            Utils.showMessage(`批量删除完成，${completed}个成功，${errors}个失败`, 'warning');
                            this.loadRulesPage();
                        }
                    }
                });
            });
        });
    },

    /**
     * 获取选中的规则ID
     */
    getSelectedRuleIds: function() {
        const selectedIds = [];
        $('.rule-checkbox:checked').each(function() {
            selectedIds.push(parseInt($(this).val()));
        });
        return selectedIds;
    },

    /**
     * 加载监控页面
     */
    loadMonitorPage: function() {
        $('#page-content').html(`
            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number" id="totalRules">-</div>
                        <div class="stat-label">总规则数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card success">
                        <div class="stat-number" id="enabledRules">-</div>
                        <div class="stat-label">启用规则</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card warning">
                        <div class="stat-number" id="disabledRules">-</div>
                        <div class="stat-label">禁用规则</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card danger">
                        <div class="stat-number" id="systemStatus">运行中</div>
                        <div class="stat-label">系统状态</div>
                    </div>
                </div>
            </div>

            <!-- 动作执行统计 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number" id="queueSize">-</div>
                        <div class="stat-label">队列大小</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card success">
                        <div class="stat-number" id="successCount">-</div>
                        <div class="stat-label">成功执行</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card danger">
                        <div class="stat-number" id="failureCount">-</div>
                        <div class="stat-label">执行失败</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card warning">
                        <div class="stat-number" id="pendingCount">-</div>
                        <div class="stat-label">待执行</div>
                    </div>
                </div>
            </div>

            <!-- 动作执行监控详情 -->
            <div class="row mb-4">
                <!-- 动作执行监控统计 -->
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">动作执行监控统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <h6>执行状态分布</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>成功:</strong> <span id="monitorSuccessCount" class="text-success">-</span></li>
                                        <li><strong>失败:</strong> <span id="monitorFailureCount" class="text-danger">-</span></li>
                                        <li><strong>超时:</strong> <span id="monitorTimeoutCount" class="text-warning">-</span></li>
                                        <li><strong>重试:</strong> <span id="monitorRetryCount" class="text-info">-</span></li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <h6>性能指标</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>平均响应时间:</strong> <span id="avgResponseTime">-</span></li>
                                        <li><strong>最大响应时间:</strong> <span id="maxResponseTime">-</span></li>
                                        <li><strong>最小响应时间:</strong> <span id="minResponseTime">-</span></li>
                                        <li><strong>吞吐量:</strong> <span id="throughput">-</span></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>监控信息</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>监控开始时间:</strong> <span id="monitorStartTime">-</span></li>
                                        <li><strong>数据收集间隔:</strong> <span id="collectInterval">-</span></li>
                                        <li><strong>活跃监控器:</strong> <span id="activeMonitors">-</span></li>
                                        <li><strong>数据点数量:</strong> <span id="dataPoints">-</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 动作执行队列状态 -->
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">动作执行队列状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <h6>队列信息</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>队列大小:</strong> <span id="queueSizeDetail">-</span></li>
                                        <li><strong>活跃线程:</strong> <span id="activeThreads">-</span></li>
                                        <li><strong>核心线程:</strong> <span id="coreThreads">-</span></li>
                                        <li><strong>最大线程:</strong> <span id="maxThreads">-</span></li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <h6>执行统计</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>总执行数:</strong> <span id="totalExecutions">-</span></li>
                                        <li><strong>成功率:</strong> <span id="successRate">-</span></li>
                                        <li><strong>平均耗时:</strong> <span id="avgDuration">-</span></li>
                                        <li><strong>最后更新:</strong> <span id="lastUpdate">-</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">系统信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>运行时信息</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>启动时间:</strong> <span id="startTime">-</span></li>
                                        <li><strong>运行时长:</strong> <span id="uptime">-</span></li>
                                        <li><strong>Java版本:</strong> <span id="javaVersion">-</span></li>
                                        <li><strong>操作系统:</strong> <span id="osName">-</span></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>内存使用</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>最大内存:</strong> <span id="maxMemory">-</span></li>
                                        <li><strong>已用内存:</strong> <span id="usedMemory">-</span></li>
                                        <li><strong>空闲内存:</strong> <span id="freeMemory">-</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `);

        // 加载统计数据
        this.loadMonitorData();

        // 定时刷新（清理之前的定时器）
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
        }
        this.monitorInterval = setInterval(() => {
            this.loadMonitorData();
        }, 30000);
    },

    /**
     * 加载监控数据
     */
    loadMonitorData: function() {
        // 加载规则统计
        this.loadRuleStats();

        // 加载系统信息
        this.loadSystemInfo();

        // 加载动作执行队列状态
        this.loadActionQueueStatus();

        // 加载动作执行统计
        this.loadActionExecutionStats();

        // 加载动作监控统计
        this.loadActionMonitorStats();
    },

    /**
     * 加载规则统计
     */
    loadRuleStats: function() {
        $.ajax({
            url: '/api/engine/rule',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    const rules = response.data;
                    const enabledCount = rules.filter(r => r.enabled).length;
                    const disabledCount = rules.length - enabledCount;

                    $('#totalRules').text(rules.length);
                    $('#enabledRules').text(enabledCount);
                    $('#disabledRules').text(disabledCount);
                }
            },
            error: () => {
                $('#totalRules').text('-');
                $('#enabledRules').text('-');
                $('#disabledRules').text('-');
            }
        });
    },

    /**
     * 加载系统信息
     */
    loadSystemInfo: function() {
        $.ajax({
            url: '/api/engine/monitor/status',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.systemInfo) {
                    const systemInfo = response.systemInfo;
                    $('#maxMemory').text(this.formatBytes(systemInfo.maxMemory));
                    $('#usedMemory').text(this.formatBytes(systemInfo.usedMemory));
                    $('#freeMemory').text(this.formatBytes(systemInfo.freeMemory));
                }

                if (response.startTime) {
                    $('#startTime').text(new Date(response.startTime).toLocaleString());
                }

                if (response.uptime) {
                    $('#uptime').text(this.formatDuration(response.uptime));
                }
            },
            error: () => {
                $('#maxMemory').text('-');
                $('#usedMemory').text('-');
                $('#freeMemory').text('-');
                $('#startTime').text('-');
                $('#uptime').text('-');
            }
        });

        // 设置固定信息
        $('#javaVersion').text('Java 8+');
        $('#osName').text(navigator.platform);
    },

    /**
     * 加载动作执行队列状态
     */
    loadActionQueueStatus: function() {
        $.ajax({
            url: '/api/engine/monitor/action-execution-queue',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data) {
                    const data = response.data;

                    // 更新统计卡片
                    $('#queueSize').text(data.queueSize || 0);
                    $('#pendingCount').text(data.pendingTasks || 0);

                    // 更新详细信息
                    $('#queueSizeDetail').text(data.queueSize || 0);
                    $('#activeThreads').text(data.activeThreads || 0);
                    $('#coreThreads').text(data.corePoolSize || 0);
                    $('#maxThreads').text(data.maximumPoolSize || 0);
                }
            },
            error: () => {
                $('#queueSize').text('-');
                $('#pendingCount').text('-');
                $('#queueSizeDetail').text('-');
                $('#activeThreads').text('-');
                $('#coreThreads').text('-');
                $('#maxThreads').text('-');
            }
        });
    },

    /**
     * 加载动作执行统计
     */
    loadActionExecutionStats: function() {
        $.ajax({
            url: '/api/engine/monitor/action-execution-stats',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data) {
                    const data = response.data;

                    // 更新统计卡片
                    $('#successCount').text(data.successCount || 0);
                    $('#failureCount').text(data.failureCount || 0);

                    // 更新详细信息
                    $('#totalExecutions').text(data.totalExecutions || 0);

                    if (data.totalExecutions > 0) {
                        const successRate = ((data.successCount || 0) / data.totalExecutions * 100).toFixed(1);
                        $('#successRate').text(successRate + '%');
                    } else {
                        $('#successRate').text('-');
                    }

                    $('#avgDuration').text(data.averageDuration ? data.averageDuration + 'ms' : '-');
                    $('#lastUpdate').text(data.lastUpdateTime ? new Date(data.lastUpdateTime).toLocaleString() : '-');
                }
            },
            error: () => {
                $('#successCount').text('-');
                $('#failureCount').text('-');
                $('#totalExecutions').text('-');
                $('#successRate').text('-');
                $('#avgDuration').text('-');
                $('#lastUpdate').text('-');
            }
        });
    },

    /**
     * 加载动作监控统计
     */
    loadActionMonitorStats: function() {
        $.ajax({
            url: '/api/engine/monitor/action-monitor-stats',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data) {
                    const data = response.data;

                    // 执行状态分布
                    $('#monitorSuccessCount').text(data.successCount || 0);
                    $('#monitorFailureCount').text(data.failureCount || 0);
                    $('#monitorTimeoutCount').text(data.timeoutCount || 0);
                    $('#monitorRetryCount').text(data.retryCount || 0);

                    // 性能指标
                    $('#avgResponseTime').text(data.averageResponseTime ? data.averageResponseTime + 'ms' : '-');
                    $('#maxResponseTime').text(data.maxResponseTime ? data.maxResponseTime + 'ms' : '-');
                    $('#minResponseTime').text(data.minResponseTime ? data.minResponseTime + 'ms' : '-');
                    $('#throughput').text(data.throughput ? data.throughput + '/s' : '-');

                    // 监控信息
                    $('#monitorStartTime').text(data.startTime ? new Date(data.startTime).toLocaleString() : '-');
                    $('#collectInterval').text(data.collectInterval ? data.collectInterval + 'ms' : '-');
                    $('#activeMonitors').text(data.activeMonitors || 0);
                    $('#dataPoints').text(data.dataPoints || 0);
                }
            },
            error: () => {
                $('#monitorSuccessCount').text('-');
                $('#monitorFailureCount').text('-');
                $('#monitorTimeoutCount').text('-');
                $('#monitorRetryCount').text('-');
                $('#avgResponseTime').text('-');
                $('#maxResponseTime').text('-');
                $('#minResponseTime').text('-');
                $('#throughput').text('-');
                $('#monitorStartTime').text('-');
                $('#collectInterval').text('-');
                $('#activeMonitors').text('-');
                $('#dataPoints').text('-');
            }
        });
    },

    /**
     * 格式化字节数
     * @param {number} bytes 字节数
     */
    formatBytes: function(bytes) {
        if (!bytes || bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化持续时间
     * @param {number} milliseconds 毫秒数
     */
    formatDuration: function(milliseconds) {
        if (!milliseconds || milliseconds === 0) return '0秒';

        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}天 ${hours % 24}小时`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes % 60}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟 ${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    },

    /**
     * 加载配置页面
     */
    loadConfigPage: function() {
        $('#page-actions').html(`
            <button type="button" class="btn btn-primary" onclick="App.editGlobalCalendar()">
                <i class="bi bi-pencil me-1"></i>编辑日历配置
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="App.refreshCalendarCache()">
                <i class="bi bi-arrow-clockwise me-1"></i>刷新缓存
            </button>
        `);

        $('#page-content').html(`
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">排除月日配置</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">配置需要排除的月日（如节假日），格式：MM-dd，每年重复生效。</p>
                            <div id="excludeMonthDaysContainer">
                                <div class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <div class="mt-2">正在加载...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">命名时间段配置</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">配置命名时间段（如季节、特殊时期），支持跨年时间段。</p>
                            <div id="dateRangesContainer">
                                <div class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <div class="mt-2">正在加载...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">系统配置信息</h5>
                        </div>
                        <div class="card-body">
                            <div id="systemConfigContainer">
                                <div class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <div class="mt-2">正在加载...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `);

        // 加载配置数据
        this.loadGlobalCalendar();
        this.loadSystemConfig();
    },

    /**
     * 加载全局日历配置
     */
    loadGlobalCalendar: function() {
        $.ajax({
            url: '/api/engine/config/calendar',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data) {
                    this.renderGlobalCalendar(response.data);
                } else {
                    this.renderGlobalCalendar({
                        excludeMonthDays: [],
                        dateRanges: {}
                    });
                }
            },
            error: () => {
                $('#excludeMonthDaysContainer').html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        加载全局日历配置失败
                    </div>
                `);
                $('#dateRangesContainer').html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        加载命名时间段配置失败
                    </div>
                `);
            }
        });
    },

    /**
     * 渲染全局日历配置
     * @param {object} calendar 日历配置对象
     */
    renderGlobalCalendar: function(calendar) {
        // 渲染排除月日
        this.renderExcludeMonthDays(calendar.excludeMonthDays || []);

        // 渲染命名时间段
        this.renderDateRanges(calendar.dateRanges || {});
    },

    /**
     * 渲染排除月日列表
     * @param {array} excludeMonthDays 排除月日数组
     */
    renderExcludeMonthDays: function(excludeMonthDays) {
        if (excludeMonthDays.length === 0) {
            $('#excludeMonthDaysContainer').html(`
                <div class="text-muted text-center py-3">
                    <i class="bi bi-calendar-x"></i>
                    <div class="mt-2">暂无排除月日配置</div>
                </div>
            `);
            return;
        }

        let html = '<div class="row">';
        excludeMonthDays.forEach((monthDay, index) => {
            html += `
                <div class="col-md-6 mb-2">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-warning me-2">${index + 1}</span>
                        <code class="flex-grow-1">${Utils.escapeHtml(monthDay)}</code>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        $('#excludeMonthDaysContainer').html(html);
    },

    /**
     * 渲染命名时间段
     * @param {object} dateRanges 命名时间段对象
     */
    renderDateRanges: function(dateRanges) {
        const rangeNames = Object.keys(dateRanges);

        if (rangeNames.length === 0) {
            $('#dateRangesContainer').html(`
                <div class="text-muted text-center py-3">
                    <i class="bi bi-calendar-range"></i>
                    <div class="mt-2">暂无命名时间段配置</div>
                </div>
            `);
            return;
        }

        let html = '';
        rangeNames.forEach(rangeName => {
            const ranges = dateRanges[rangeName];
            html += `
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="bi bi-calendar-event me-1"></i>
                        ${Utils.escapeHtml(rangeName)}
                    </h6>
                    <div class="ms-3">
            `;

            ranges.forEach((range, index) => {
                html += `
                    <div class="d-flex align-items-center mb-1">
                        <span class="badge bg-info me-2">${index + 1}</span>
                        <code class="me-2">${Utils.escapeHtml(range.startDate)}</code>
                        <span class="text-muted me-2">至</span>
                        <code class="me-2">${Utils.escapeHtml(range.endDate)}</code>
                        ${range.description ? `<small class="text-muted">(${Utils.escapeHtml(range.description)})</small>` : ''}
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        $('#dateRangesContainer').html(html);
    },

    /**
     * 加载系统配置
     */
    loadSystemConfig: function() {
        $.ajax({
            url: '/api/engine/config/system',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response.success && response.data) {
                    this.renderSystemConfig(response.data);
                } else {
                    $('#systemConfigContainer').html(`
                        <div class="text-muted text-center py-3">
                            <i class="bi bi-gear"></i>
                            <div class="mt-2">暂无系统配置信息</div>
                        </div>
                    `);
                }
            },
            error: () => {
                $('#systemConfigContainer').html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        加载系统配置失败
                    </div>
                `);
            }
        });
    },

    /**
     * 渲染系统配置
     * @param {object} config 系统配置对象
     */
    renderSystemConfig: function(config) {
        let html = '<div class="row">';

        Object.keys(config).forEach(key => {
            const value = config[key];
            html += `
                <div class="col-md-6 mb-2">
                    <div class="d-flex">
                        <strong class="me-2">${Utils.escapeHtml(key)}:</strong>
                        <span class="text-muted">${Utils.escapeHtml(String(value))}</span>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        $('#systemConfigContainer').html(html);
    },

    /**
     * 编辑全局日历配置
     */
    editGlobalCalendar: function() {
        // 先获取当前配置
        $.ajax({
            url: '/api/engine/config/calendar',
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                const calendar = response.success && response.data ? response.data : {
                    excludeMonthDays: [],
                    dateRanges: {}
                };
                this.showCalendarEditModal(calendar);
            },
            error: () => {
                // 如果获取失败，使用空配置
                this.showCalendarEditModal({
                    excludeMonthDays: [],
                    dateRanges: {}
                });
            }
        });
    },

    /**
     * 显示日历编辑模态框
     * @param {object} calendar 日历配置
     */
    showCalendarEditModal: function(calendar) {
        $('#modalTitle').text('编辑全局日历配置');
        $('#modalBody').html(`
            <form id="calendarForm">
                <div class="mb-4">
                    <label for="excludeMonthDaysInput" class="form-label">排除月日配置</label>
                    <textarea class="form-control json-editor" id="excludeMonthDaysInput" rows="6">${JSON.stringify(calendar.excludeMonthDays || [], null, 2)}</textarea>
                    <div class="form-text">
                        配置需要排除的月日，格式：["01-01", "05-01", "10-01"]<br>
                        例如：["01-01", "02-14", "05-01", "10-01", "12-25"]
                    </div>
                </div>

                <div class="mb-3">
                    <label for="dateRangesInput" class="form-label">命名时间段配置</label>
                    <textarea class="form-control json-editor" id="dateRangesInput" rows="10">${JSON.stringify(calendar.dateRanges || {}, null, 2)}</textarea>
                    <div class="form-text">
                        配置命名时间段，格式：<br>
                        {<br>
                        &nbsp;&nbsp;"春季": [{"startDate": "03-01", "endDate": "05-31", "description": "春季时间段"}],<br>
                        &nbsp;&nbsp;"夏季": [{"startDate": "06-01", "endDate": "08-31", "description": "夏季时间段"}]<br>
                        }
                    </div>
                </div>
            </form>
        `);
        $('#modalFooter').html(`
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="App.saveGlobalCalendar()">保存配置</button>
        `);
        $('#commonModal').modal('show');
    },

    /**
     * 保存全局日历配置
     */
    saveGlobalCalendar: function() {
        const excludeMonthDaysText = $('#excludeMonthDaysInput').val().trim();
        const dateRangesText = $('#dateRangesInput').val().trim();

        try {
            // 解析JSON
            const excludeMonthDays = excludeMonthDaysText ? JSON.parse(excludeMonthDaysText) : [];
            const dateRanges = dateRangesText ? JSON.parse(dateRangesText) : {};

            // 验证数据格式
            if (!Array.isArray(excludeMonthDays)) {
                Utils.showMessage('排除月日配置必须是数组格式', 'danger');
                return;
            }

            if (typeof dateRanges !== 'object' || Array.isArray(dateRanges)) {
                Utils.showMessage('命名时间段配置必须是对象格式', 'danger');
                return;
            }

            // 提交配置
            $.ajax({
                url: '/api/engine/config/calendar',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    excludeMonthDays: excludeMonthDays,
                    dateRanges: dateRanges
                }),
                success: (response) => {
                    if (response.success) {
                        Utils.showMessage('全局日历配置保存成功', 'success');
                        $('#commonModal').modal('hide');
                        this.loadGlobalCalendar();
                    } else {
                        Utils.showMessage('保存失败: ' + response.message, 'danger');
                    }
                },
                error: (xhr) => {
                    let message = '保存全局日历配置失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message += ': ' + xhr.responseJSON.message;
                    }
                    Utils.showMessage(message, 'danger');
                }
            });

        } catch (e) {
            Utils.showMessage('JSON格式错误，请检查配置: ' + e.message, 'danger');
        }
    },

    /**
     * 刷新日历缓存
     */
    refreshCalendarCache: function() {
        Utils.confirm('确定要刷新日历缓存吗？', () => {
            $.ajax({
                url: '/api/engine/config/calendar/refresh',
                type: 'POST',
                success: (response) => {
                    if (response.success) {
                        Utils.showMessage('日历缓存刷新成功', 'success');
                    } else {
                        Utils.showMessage('刷新失败: ' + response.message, 'danger');
                    }
                },
                error: () => {
                    Utils.showMessage('刷新日历缓存失败', 'danger');
                }
            });
        });
    }
};

// 页面加载完成后初始化应用
$(document).ready(function() {
    App.init();
});
