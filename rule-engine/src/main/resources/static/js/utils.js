/**
 * 工具函数库
 * 提供通用的工具方法
 */

// 全局工具对象
window.Utils = {

    /**
     * 显示浮动提示消息 - 保持原Alert样式
     * @param {string} message 消息内容
     * @param {string} type 消息类型: success, danger, warning, info
     * @param {number} duration 显示时长(毫秒)，默认3000
     */
    showMessage: function(message, type = 'info', duration = 3000) {
        const container = $('#float-alert-container');
        const alertId = 'float-alert-' + Date.now();
        const icon = this.getIconByType(type);

        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible float-alert" role="alert">
                <i class="bi bi-${icon} me-2"></i>
                ${this.escapeHtml(message)}
                <button type="button" class="btn-close" onclick="Utils.closeAlert('${alertId}')"></button>
            </div>
        `;

        container.append(alertHtml);

        // 自动消失
        if (duration > 0) {
            setTimeout(() => {
                this.closeAlert(alertId);
            }, duration);
        }
    },

    /**
     * 关闭指定的浮动提示
     * @param {string} alertId 提示框ID
     */
    closeAlert: function(alertId) {
        const alertElement = $(`#${alertId}`);
        if (alertElement.length > 0) {
            // 添加淡出动画
            alertElement.addClass('fade-out');

            // 动画完成后移除元素
            setTimeout(() => {
                alertElement.remove();
            }, 300);
        }
    },
    
    /**
     * 根据消息类型获取图标
     */
    getIconByType: function(type) {
        const icons = {
            success: 'check-circle-fill',
            danger: 'exclamation-triangle-fill',
            warning: 'exclamation-triangle-fill',
            info: 'info-circle-fill'
        };
        return icons[type] || 'info-circle-fill';
    },
    
    /**
     * 确认对话框
     * @param {string} message 确认消息
     * @param {function} callback 确认后的回调函数
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },
    
    /**
     * 格式化日期时间
     * @param {string|Date} date 日期
     * @param {string} format 格式，默认 'YYYY-MM-DD HH:mm:ss'
     */
    formatDateTime: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '-';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '-';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 格式化JSON字符串
     * @param {string|object} json JSON字符串或对象
     */
    formatJSON: function(json) {
        try {
            if (typeof json === 'string') {
                json = JSON.parse(json);
            }
            return JSON.stringify(json, null, 2);
        } catch (e) {
            return json;
        }
    },
    

    
    /**
     * 获取状态徽章HTML
     * @param {boolean} enabled 是否启用
     */
    getStatusBadge: function(enabled) {
        if (enabled) {
            return '<span class="badge bg-success status-badge">启用</span>';
        } else {
            return '<span class="badge bg-secondary status-badge">禁用</span>';
        }
    },
    
    /**
     * 截断文本
     * @param {string} text 文本
     * @param {number} maxLength 最大长度
     */
    truncateText: function(text, maxLength = 50) {
        if (!text) return '-';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    },
    
    /**
     * 转义HTML
     * @param {string} text 文本
     */
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    

    
    /**
     * 防抖函数
     * @param {function} func 函数
     * @param {number} wait 等待时间
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    

};
