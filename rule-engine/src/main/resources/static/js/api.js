/**
 * API调用封装
 * 封装所有后端API接口调用
 */

// API基础配置
const API_BASE = '/api/engine';

// 全局API对象
window.API = {
    
    /**
     * 通用AJAX请求方法
     * @param {object} options 请求选项
     */
    request: function(options) {
        const customSuccess = options.success;
        const customError = options.error;

        const defaultOptions = {
            type: 'GET',
            dataType: 'json',
            timeout: 30000,
            success: function(data) {
                if (customSuccess) {
                    customSuccess(data);
                }
            },
            error: function(xhr, status, error) {
                let message = '请求失败';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.status === 0) {
                    message = '网络连接失败';
                } else if (xhr.status === 404) {
                    message = '接口不存在';
                } else if (xhr.status === 500) {
                    message = '服务器内部错误';
                } else {
                    message = `请求失败: ${error}`;
                }

                Utils.showMessage(message, 'danger');

                if (customError) {
                    customError(xhr, status, error);
                }
            }
        };

        // 移除自定义的success和error，避免冲突
        const cleanOptions = $.extend({}, options);
        delete cleanOptions.success;
        delete cleanOptions.error;

        return $.ajax($.extend(defaultOptions, cleanOptions));
    },
    
    // ==================== 规则管理API ====================
    
    /**
     * 获取所有规则
     * @param {object} options 选项参数（包含查询参数和回调）
     */
    getRules: function(options = {}) {
        // 如果options包含success/error回调，说明是新的调用方式
        if (options.success || options.error) {
            return this.request($.extend({
                url: `${API_BASE}/rule`,
                data: options.params || {}
            }, options));
        } else {
            // 兼容旧的调用方式，返回jQuery Promise
            return this.request({
                url: `${API_BASE}/rule`,
                data: options
            });
        }
    },
    
    /**
     * 根据ID获取规则
     * @param {number} ruleId 规则ID
     * @param {object} options 额外选项
     */
    getRule: function(ruleId, options = {}) {
        return this.request($.extend({
            url: `${API_BASE}/rule/${ruleId}`
        }, options));
    },
    

};
