# MQTT配置
mqtt:
  client-id-prefix: ${MQTT_CLIENT_PREFIX:rule-engine-mqtt-client}
  server-uris: ${MQTT_SERVER_URIS:tcp://192.168.3.142:1883}
  username: ${MQTT_SERVER_USER:"inxvision"}
  password: ${MQTT_SERVER_PASSWORD:"inxvision@123"}
  keep-alive-interval: 60
  completion-timeout: 30
  # 传输质量,默认1
  qos: 1
  # 是否不保持session,默认true
  clean-session: false
  # 是否自动连接,默认true
  automatic-reconnect: true
  # 订阅主题列表
  sub-topics: ${MQTT_SUB_TOPICS:inx/gw/status/#,inx/gw/response/#}
