<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.inxaiot.ruleengine.storage.mapper.GlobalCalendarMapper">

    <!-- 结果映射 -->
    <resultMap id="GlobalCalendarResultMap" type="com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="biz_id" property="bizId" jdbcType="VARCHAR"/>
        <result column="exclude_month_days" property="excludeMonthDays" jdbcType="VARCHAR"/>
        <result column="month_date_ranges" property="monthDateRanges" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, biz_id, exclude_month_days, month_date_ranges, create_time, update_time
    </sql>

    <!-- 插入全局日历记录 -->
    <insert id="insertGlobalCalendar" parameterType="com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity">
        INSERT INTO global_calendar (
            biz_id, exclude_month_days, month_date_ranges
        ) VALUES (
            #{bizId}, #{excludeMonthDays}, #{monthDateRanges}
        )
    </insert>

    <!-- 更新全局日历记录 -->
    <update id="updateGlobalCalendar" parameterType="com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity">
        UPDATE global_calendar SET
            exclude_month_days = #{excludeMonthDays},
            month_date_ranges = #{monthDateRanges},
            update_time = CURRENT_TIMESTAMP
        WHERE biz_id = #{bizId}
    </update>

    <!-- 根据业务ID查询全局日历记录 -->
    <select id="findGlobalCalendarByBizId" parameterType="java.lang.String" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE biz_id = #{bizId}
    </select>

    <!-- 查询默认全局日历记录 -->
    <select id="findDefaultGlobalCalendar" resultMap="GlobalCalendarResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM global_calendar
        WHERE biz_id = 'default'
        LIMIT 1
    </select>

    <!-- 检查是否存在全局日历记录 -->
    <select id="existsGlobalCalendar" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM global_calendar WHERE biz_id = #{bizId}
    </select>

</mapper>
