<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.inxaiot.ruleengine.storage.mapper.RuleDefinitionMapper">

    <!-- 结果映射 -->
    <resultMap id="RuleDefinitionResultMap" type="com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="biz_id" property="bizId" jdbcType="VARCHAR"/>
        <result column="group_id" property="groupId" jdbcType="VARCHAR"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="time_conditions" property="timeConditions" jdbcType="VARCHAR"/>
        <result column="logic" property="logic" jdbcType="VARCHAR"/>
        <result column="trigger_conditions" property="triggerCondition" jdbcType="VARCHAR"/>
        <result column="actions" property="actions" jdbcType="VARCHAR"/>
        <result column="deactivation_actions" property="deactivationActions" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, rule_name, biz_id, group_id,
        priority, enabled, time_conditions,logic, trigger_conditions, actions,deactivation_actions, description,
        create_time, update_time
    </sql>

    <!-- 插入规则定义 -->
    <insert id="insertRule" parameterType="com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity">
        INSERT INTO rule_definition (
            id, rule_name, biz_id, group_id,
            priority, enabled, time_conditions, logic, trigger_conditions, actions, deactivation_actions,description
        ) VALUES (
            #{id}, #{ruleName},#{bizId}, #{groupId},#{priority}, #{enabled}, #{timeConditions},#{logic}, #{triggerCondition}, #{actions}, #{deactivationActions},#{description}
        )
    </insert>

    <!-- 根据ID更新规则定义 -->
    <update id="updateRuleById" parameterType="com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity">
        UPDATE rule_definition
        <set>
            <if test="ruleName != null and ruleName != ''">
                rule_name = #{ruleName},
            </if>
            <if test="priority != null">
                priority = #{priority},
            </if>
            <if test="enabled != null">
                enabled = #{enabled},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
            logic = #{logic},
            time_conditions = #{timeConditions},
            trigger_conditions = #{triggerCondition},
            actions = #{actions},
            deactivation_actions = #{deactivationActions},
            description = #{description},
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除规则定义 -->
    <delete id="deleteRuleById" parameterType="java.lang.Long">
        DELETE FROM rule_definition WHERE id = #{id}
    </delete>

    <!-- 根据ID查询规则定义 -->
    <select id="findRuleById" parameterType="java.lang.Long" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE id = #{id}
    </select>

    <!-- 查询所有规则定义 -->
    <select id="findAllRules" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 查询所有启用的规则定义 -->
    <select id="findAllEnabledRules" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE enabled = 1
        ORDER BY priority ASC, create_time DESC
    </select>


    <!-- 根据业务ID查询规则定义 -->
    <select id="findRuleByBizId" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE biz_id = #{bizId}
    </select>

    <!-- 根据分组ID查询规则定义 -->
    <select id="findRulesByGroupId" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE group_id = #{groupId}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据分组ID列表查询规则定义 -->
    <select id="findRulesByGroupIds" parameterType="java.util.List" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE group_id in
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        ORDER BY priority ASC, create_time DESC
    </select>


    <!-- 批量插入规则定义 -->
    <insert id="batchInsertRules" parameterType="java.util.List">
        INSERT INTO rule_definition (
            id, rule_name, biz_id, group_id,priority, enabled, time_conditions, logic, trigger_conditions, actions, deactivation_actions,description
        ) VALUES
        <foreach collection="rules" item="rule" separator=",">
            (#{rule.id}, #{rule.ruleName},
             #{rule.bizId}, #{rule.groupId}, #{rule.priority}, #{rule.enabled}, 
             #{rule.timeConditions}, #{rule.logic}, #{rule.triggerCondition}, #{rule.actions},#{rule.deactivationActions}, #{rule.description})
        </foreach>
    </insert>

    <!-- 批量更新规则启用状态 -->
    <update id="batchUpdateRuleEnabledByIds">
        UPDATE rule_definition SET enabled = #{enabled}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量删除规则定义 -->
    <delete id="batchDeleteRulesByIds">
        DELETE FROM rule_definition
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据分组ID批量删除规则定义 -->
    <delete id="batchDeleteRulesByGroupId" parameterType="java.lang.String">
        DELETE FROM rule_definition WHERE group_id = #{groupId}
    </delete>

    <!-- 统计规则总数 -->
    <select id="countAllRules" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM rule_definition
    </select>

    <!-- 统计启用的规则数 -->
    <select id="countEnabledRules" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM rule_definition WHERE enabled = 1
    </select>



</mapper>
