server:
  port: 6001
  servlet:
    context-path: /

spring:
  application:
    name: iot-rule-engine

  # 配置文件导入
  profiles:
    include: mqtt
  
  # 数据源配置 - 支持MySQL和SQLite切换
  datasource:
    # mysql采用：DB_DRIVER_CLASS=com.mysql.cj.jdbc.Driver
    driver-class-name: ${DB_DRIVER_CLASS:org.sqlite.JDBC}
    # mysql采用：DB_URL=*************************************************************************************************************************
    url: ${DB_URL:jdbc:sqlite:${rule.engine.data.path}/rules_engine.db}
    username: ${DB_USERNAME:}
    password: ${DB_PASSWORD:}
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.inxaiot.ruleengine.storage.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000

# 规则引擎配置
rule:
  engine:
    # 基础标识配置
    id: edge-rule-engine
    region:
      id: region-001
    building:
      id: building-A
    version: 1.0.0
    timezone: Asia/Shanghai
    locale: zh_CN
    debug: false

    # 数据存储路径
    data:
      path: ${DATA_PATH:/data/rule-engine/db}
    # 日志路径
    log:
      path:  ${DATA_PATH:/data/rule-engine/logs}
      action-enable: true              # 是否启用动作执行详细日志
    # 状态管理配置
    state:
      # 设备状态缓存大小
      cache-size: 10000
      # 状态清理间隔(分钟)
      cleanup-interval: 60
    # 时间条件配置
    time:
      # 全局日历刷新间隔(小时)
      calendar-refresh-interval: 24
      # 时区
      timezone: Asia/Shanghai

    # 动作执行配置
    action-execution:
      timeout-seconds: 23         # 执行超时时间（秒）
      max-retry-count: 3          # 最大重试次数
      queue-cleanup-interval: 300 # 队列清理间隔（秒）
      enable-priority-control: true # 是否启用优先级控制
      critical-action-timeout: 23   # 关键动作超时时间（秒），线上建议改成timeout-seconds的3倍，这样可以重试三次

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
