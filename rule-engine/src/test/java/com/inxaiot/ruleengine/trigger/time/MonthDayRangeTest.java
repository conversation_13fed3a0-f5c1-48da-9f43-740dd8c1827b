package com.inxaiot.ruleengine.trigger.time;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MonthDayRange测试类
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-07-07
 */
class MonthDayRangeTest {

    @Test
    void testContains_NormalRange() {
        // 测试正常范围：6月1日到8月31日
        MonthDayRange range = new MonthDayRange("06-01", "08-31", "夏季");
        
        assertTrue(range.contains("06-01"));  // 边界开始
        assertTrue(range.contains("07-15"));  // 中间
        assertTrue(range.contains("08-31"));  // 边界结束
        assertFalse(range.contains("05-31")); // 之前
        assertFalse(range.contains("09-01")); // 之后
    }

    @Test
    void testContains_CrossYearRange() {
        // 测试跨年范围：12月1日到2月28日
        MonthDayRange range = new MonthDayRange("12-01", "02-28", "冬季");
        
        assertTrue(range.contains("12-01"));  // 边界开始
        assertTrue(range.contains("12-25"));  // 年末
        assertTrue(range.contains("01-01"));  // 年初
        assertTrue(range.contains("02-28"));  // 边界结束
        assertFalse(range.contains("11-30")); // 之前
        assertFalse(range.contains("03-01")); // 之后
        assertFalse(range.contains("06-15")); // 中间月份
    }

    @Test
    void testIsValid() {
        assertTrue(new MonthDayRange("01-01", "12-31").isValid());
        assertTrue(new MonthDayRange("02-14", "02-14").isValid());
        assertTrue(new MonthDayRange("12-01", "02-28").isValid()); // 跨年
        
        assertFalse(new MonthDayRange("13-01", "12-31").isValid()); // 无效月份
        assertFalse(new MonthDayRange("01-32", "12-31").isValid()); // 无效日期
        assertFalse(new MonthDayRange("02-30", "12-31").isValid()); // 2月30日
        assertFalse(new MonthDayRange(null, "12-31").isValid());
        assertFalse(new MonthDayRange("01-01", null).isValid());
    }

    @Test
    void testIsCrossYear() {
        assertFalse(new MonthDayRange("06-01", "08-31").isCrossYear()); // 正常范围
        assertTrue(new MonthDayRange("12-01", "02-28").isCrossYear());  // 跨年范围
        assertFalse(new MonthDayRange("01-01", "01-01").isCrossYear()); // 同一天
        assertFalse(new MonthDayRange("01-01", "12-31").isCrossYear()); // 全年
    }

    @Test
    void testInvalidFormat() {
        MonthDayRange range = new MonthDayRange("invalid", "08-31");
        assertFalse(range.isValid());
        assertFalse(range.contains("07-15"));
        
        range = new MonthDayRange("06-01", "invalid");
        assertFalse(range.isValid());
        assertFalse(range.contains("07-15"));
    }

    @Test
    void testEqualsAndHashCode() {
        MonthDayRange range1 = new MonthDayRange("06-01", "08-31", "夏季");
        MonthDayRange range2 = new MonthDayRange("06-01", "08-31", "夏季");
        MonthDayRange range3 = new MonthDayRange("06-01", "08-31", "夏天");
        MonthDayRange range4 = new MonthDayRange("07-01", "08-31", "夏季");
        
        assertEquals(range1, range2);
        assertEquals(range1.hashCode(), range2.hashCode());
        
        assertNotEquals(range1, range3); // 不同描述
        assertNotEquals(range1, range4); // 不同日期
    }

    @Test
    void testToString() {
        MonthDayRange range = new MonthDayRange("12-01", "02-28", "冬季");
        String str = range.toString();
        
        assertTrue(str.contains("12-01"));
        assertTrue(str.contains("02-28"));
        assertTrue(str.contains("冬季"));
        assertTrue(str.contains("crossYear=true"));
    }
}
