package com.inxaiot.ruleengine.trigger.time;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JSON序列化测试类
 * 验证@JsonIgnore注解是否正常工作
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-07-07
 */
class JsonSerializationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testMonthDayRangeJsonSerialization() throws Exception {
        // 创建MonthDayRange对象
        MonthDayRange range = new MonthDayRange("12-01", "02-28", "冬季跨年");

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(range);
        System.out.println("MonthDayRange JSON: " + json);

        // 验证不包含@JsonIgnore标记的方法返回值
        assertFalse(json.contains("\"valid\""));
        assertFalse(json.contains("\"crossYear\""));
        
        // 验证包含正确的字段
        assertTrue(json.contains("\"startDate\":\"12-01\""));
        assertTrue(json.contains("\"endDate\":\"02-28\""));
        assertTrue(json.contains("\"description\":\"冬季跨年\""));

        // 反序列化验证
        MonthDayRange deserializedRange = objectMapper.readValue(json, MonthDayRange.class);
        assertEquals("12-01", deserializedRange.getStartDate());
        assertEquals("02-28", deserializedRange.getEndDate());
        assertEquals("冬季跨年", deserializedRange.getDescription());
        
        // 验证方法仍然正常工作
        assertTrue(deserializedRange.isCrossYear());
        assertTrue(deserializedRange.isValid());
        assertTrue(deserializedRange.contains("12-15"));
        assertTrue(deserializedRange.contains("01-15"));
    }

    @Test
    void testGlobalCalendarJsonSerialization() throws Exception {
        // 创建GlobalCalendar对象
        GlobalCalendar calendar = new GlobalCalendar();
        calendar.setExcludeMonthDays(Arrays.asList("01-01", "12-25"));
        
        Map<String, List<MonthDayRange>> dateRanges = new HashMap<>();
        dateRanges.put("SUMMER", Arrays.asList(new MonthDayRange("06-01", "08-31", "夏季")));
        dateRanges.put("WINTER", Arrays.asList(
            new MonthDayRange("12-01", "12-31", "冬季前半段"),
            new MonthDayRange("01-01", "02-28", "冬季后半段")
        ));
        calendar.setMonthDateRanges(dateRanges);

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(calendar);
        System.out.println("GlobalCalendar JSON: " + json);

        // 验证不包含MonthDayRange的@JsonIgnore方法
        assertFalse(json.contains("\"valid\""));
        assertFalse(json.contains("\"crossYear\""));
        
        // 验证包含正确的字段
        assertTrue(json.contains("\"excludeMonthDays\""));
        assertTrue(json.contains("\"dateRanges\""));
        assertTrue(json.contains("\"SUMMER\""));
        assertTrue(json.contains("\"WINTER\""));

        // 反序列化验证
        GlobalCalendar deserializedCalendar = objectMapper.readValue(json, GlobalCalendar.class);
        assertEquals(2, deserializedCalendar.getExcludeMonthDays().size());
        assertTrue(deserializedCalendar.getExcludeMonthDays().contains("01-01"));
        assertTrue(deserializedCalendar.getExcludeMonthDays().contains("12-25"));
        
        assertEquals(2, deserializedCalendar.getMonthDateRanges().size());
        assertTrue(deserializedCalendar.getMonthDateRanges().containsKey("SUMMER"));
        assertTrue(deserializedCalendar.getMonthDateRanges().containsKey("WINTER"));
        
        // 验证方法仍然正常工作
        assertTrue(deserializedCalendar.isExcludedMonthDay("01-01"));
        assertTrue(deserializedCalendar.isInDateRange("SUMMER", "07-15"));
        assertTrue(deserializedCalendar.isInDateRange("WINTER", "12-15"));
        assertTrue(deserializedCalendar.isInDateRange("WINTER", "01-15"));
    }

    @Test
    void testJsonStructureForDatabase() throws Exception {
        // 模拟数据库存储的JSON结构
        GlobalCalendar calendar = new GlobalCalendar();
        calendar.setExcludeMonthDays(Arrays.asList("01-01", "05-01", "10-01", "12-25"));
        
        Map<String, List<MonthDayRange>> dateRanges = new HashMap<>();
        dateRanges.put("SUMMER", Arrays.asList(new MonthDayRange("06-01", "08-31", "夏季")));
        dateRanges.put("WINTER", Arrays.asList(
            new MonthDayRange("12-01", "12-31", "冬季前半段"),
            new MonthDayRange("01-01", "02-28", "冬季后半段")
        ));
        dateRanges.put("HOLIDAY", Arrays.asList(new MonthDayRange("02-10", "02-17", "春节假期")));
        calendar.setMonthDateRanges(dateRanges);

        // 序列化
        String excludeMonthDaysJson = objectMapper.writeValueAsString(calendar.getExcludeMonthDays());
        String dateRangesJson = objectMapper.writeValueAsString(calendar.getMonthDateRanges());
        
        System.out.println("ExcludeMonthDays JSON: " + excludeMonthDaysJson);
        System.out.println("DateRanges JSON: " + dateRangesJson);

        // 验证JSON结构正确
        assertTrue(excludeMonthDaysJson.startsWith("["));
        assertTrue(excludeMonthDaysJson.endsWith("]"));
        assertTrue(dateRangesJson.startsWith("{"));
        assertTrue(dateRangesJson.endsWith("}"));
        
        // 验证不包含不需要的字段
        assertFalse(dateRangesJson.contains("\"valid\""));
        assertFalse(dateRangesJson.contains("\"crossYear\""));
        
        // 验证包含必要的字段
        assertTrue(dateRangesJson.contains("\"startDate\""));
        assertTrue(dateRangesJson.contains("\"endDate\""));
        assertTrue(dateRangesJson.contains("\"description\""));
    }
}
