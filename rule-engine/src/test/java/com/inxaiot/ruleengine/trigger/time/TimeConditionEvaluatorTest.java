package com.inxaiot.ruleengine.trigger.time;

import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.storage.GlobalCalendarService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TimeConditionEvaluator 单元测试
 * 测试时间条件评估器的核心功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
public class TimeConditionEvaluatorTest {

    @Mock
    private GlobalCalendarService globalCalendarService;

    private TimeConditionEvaluator timeConditionEvaluator;

    @BeforeEach
    void setUp() {
        // 模拟全局日历数据
        GlobalCalendar calendar = TestDataFactory.createGlobalCalendar();
        lenient().when(globalCalendarService.loadGlobalCalendar()).thenReturn(calendar);

        timeConditionEvaluator = new TimeConditionEvaluator(globalCalendarService);
    }

    /**
     * 测试Cron表达式评估 - 工作日上午8点
     */
    @Test
    void testCronExpressionEvaluation_WorkdayMorning() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日（周四）上午8点
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试Cron表达式评估 - 周末时间不匹配
     */
    @Test
    void testCronExpressionEvaluation_WeekendNotMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月21日（周六）上午8点
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试工作日条件评估 - 工作日
     */
    @Test
    void testWorkDayEvaluation_Workday() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日（周四）
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试工作日条件评估 - 非工作日
     */
    @Test
    void testWorkDayEvaluation_NonWorkday() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月21日（周六）
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试季节条件评估 - 夏季
     */
    @Test
    void testSeasonEvaluation_Summer() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setSeason("SUMMER");
        condition.setLogic(Operators.Logic.AND);

        // 2024年7月15日（夏季，在TestDataFactory设置的6月1日-8月31日范围内）
        LocalDateTime testTime = LocalDateTime.of(2024, 7, 15, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试季节条件评估 - 非夏季
     */
    @Test
    void testSeasonEvaluation_NotSummer() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setSeason("SUMMER");
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月15日（冬季）
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 15, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试包含日期条件 - 匹配
     */
    @Test
    void testIncludeDateEvaluation_Match() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setIncludeDates(Arrays.asList(LocalDate.of(2024, 12, 19)));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试排除日期条件 - 被排除
     */
    @Test
    void testExcludeDateEvaluation_Excluded() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setExcludeDates(Arrays.asList(LocalDate.of(2024, 12, 19)));
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日（周四）上午8点 - 应该匹配Cron但被排除日期阻止
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试节假日条件 - 节假日
     */
    @Test
    void testHolidayEvaluation_Holiday() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * *"));
        condition.setLogic(Operators.Logic.AND);

        // 2024年1月1日（元旦，节假日，在TestDataFactory中设置）
        LocalDateTime testTime = LocalDateTime.of(2024, 1, 1, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        // 节假日应该被排除，即使时间匹配
        assertFalse(result);
    }

    /**
     * 测试多条件AND逻辑 - 所有条件满足
     */
    @Test
    void testMultipleConditions_AndLogic_AllMet() {
        // Given
        TimeCondition condition1 = new TimeCondition();
        condition1.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        condition1.setLogic(Operators.Logic.AND);

        TimeCondition condition2 = new TimeCondition();
        condition2.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition2.setLogic(Operators.Logic.AND);

        List<TimeCondition> conditions = Arrays.asList(condition1, condition2);

        // 2024年12月19日（周四）上午10点
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试多条件AND逻辑 - 部分条件不满足
     */
    @Test
    void testMultipleConditions_AndLogic_PartialMet() {
        // Given
        TimeCondition condition1 = new TimeCondition();
        condition1.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        condition1.setLogic(Operators.Logic.AND);

        TimeCondition condition2 = new TimeCondition();
        condition2.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition2.setLogic(Operators.Logic.AND);

        List<TimeCondition> conditions = Arrays.asList(condition1, condition2);

        // 2024年12月21日（周六）上午10点 - Cron匹配但工作日不匹配
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试多条件OR逻辑 - 任一条件满足
     */
    @Test
    void testMultipleConditions_OrLogic_AnyMet() {
        // Given
        TimeCondition condition1 = new TimeCondition();
        condition1.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition1.setLogic(Operators.Logic.OR);

        TimeCondition condition2 = new TimeCondition();
        condition2.setIncludeDates(Arrays.asList(LocalDate.of(2024, 12, 21)));
        condition2.setLogic(Operators.Logic.OR);

        List<TimeCondition> conditions = Arrays.asList(condition1, condition2);

        // 2024年12月21日（周六）上午10点 - Cron不匹配但包含日期匹配
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试空条件列表
     */
    @Test
    void testEmptyConditions() {
        // Given
        List<TimeCondition> conditions = Arrays.asList();
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result); // 空条件应该返回true
    }

    /**
     * 测试null条件列表
     */
    @Test
    void testNullConditions() {
        // Given
        List<TimeCondition> conditions = null;
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result); // null条件应该返回true
    }

    /**
     * 测试时间条件优先级 - 包含日期优先级最高
     */
    @Test
    void testTimeConditionPriority_IncludeDateHighest() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setIncludeDates(Arrays.asList(LocalDate.of(2024, 12, 21))); // 包含周六
        condition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY")); // 只包含工作日
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月21日（周六）- 包含日期匹配但工作日不匹配
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result); // 包含日期优先级最高，应该返回true
    }

    /**
     * 测试起止时间列表条件 - 单个时间段匹配
     */
    @Test
    void testDateRangeEvaluation_SingleRangeMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.DateRange dateRange = new TimeCondition.DateRange(
            LocalDate.of(2024, 12, 1),
            LocalDate.of(2024, 12, 31),
            "December 2024"
        );
        condition.setDateRanges(Arrays.asList(dateRange));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日 - 在时间段内
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试起止时间列表条件 - 单个时间段不匹配
     */
    @Test
    void testDateRangeEvaluation_SingleRangeNotMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.DateRange dateRange = new TimeCondition.DateRange(
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2024, 1, 31)
        );
        condition.setDateRanges(Arrays.asList(dateRange));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日 - 不在时间段内
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试起止时间列表条件 - 多个时间段OR关系，其中一个匹配
     */
    @Test
    void testDateRangeEvaluation_MultipleRangesOrMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.DateRange range1 = new TimeCondition.DateRange(
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2024, 1, 31),
            "January 2024"
        );
        TimeCondition.DateRange range2 = new TimeCondition.DateRange(
            LocalDate.of(2024, 12, 1),
            LocalDate.of(2024, 12, 31),
            "December 2024"
        );
        condition.setDateRanges(Arrays.asList(range1, range2));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日 - 匹配第二个时间段
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试起止时间列表条件 - 多个时间段都不匹配
     */
    @Test
    void testDateRangeEvaluation_MultipleRangesNoMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.DateRange range1 = new TimeCondition.DateRange(
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2024, 1, 31)
        );
        TimeCondition.DateRange range2 = new TimeCondition.DateRange(
            LocalDate.of(2024, 6, 1),
            LocalDate.of(2024, 6, 30)
        );
        condition.setDateRanges(Arrays.asList(range1, range2));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日 - 不匹配任何时间段
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试起止时间列表条件 - 边界日期测试（包含边界）
     */
    @Test
    void testDateRangeEvaluation_BoundaryDates() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.DateRange dateRange = new TimeCondition.DateRange(
            LocalDate.of(2024, 12, 1),
            LocalDate.of(2024, 12, 31)
        );
        condition.setDateRanges(Arrays.asList(dateRange));
        condition.setLogic(Operators.Logic.AND);

        // When & Then - 测试起始日期
        LocalDateTime startTime = LocalDateTime.of(2024, 12, 1, 10, 0, 0);
        boolean startResult = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), startTime);
        assertTrue(startResult, "Start date should be included");

        // When & Then - 测试结束日期
        LocalDateTime endTime = LocalDateTime.of(2024, 12, 31, 10, 0, 0);
        boolean endResult = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), endTime);
        assertTrue(endResult, "End date should be included");
    }

    /**
     * 测试起止时间列表条件 - 无效时间段
     */
    @Test
    void testDateRangeEvaluation_InvalidRange() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.DateRange invalidRange = new TimeCondition.DateRange(
            LocalDate.of(2024, 12, 31),
            LocalDate.of(2024, 12, 1) // 起始日期晚于结束日期
        );
        condition.setDateRanges(Arrays.asList(invalidRange));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月19日
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result); // 无效时间段应该不匹配
    }

    /**
     * 测试月日范围条件 - 单个范围匹配
     */
    @Test
    void testMonthDayRangeEvaluation_SingleRangeMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.MonthDayRange monthDayRange = new TimeCondition.MonthDayRange("06-01", "08-31", "夏季");
        condition.setMonthDayRanges(Arrays.asList(monthDayRange));
        condition.setLogic(Operators.Logic.AND);

        // 2024年7月15日 - 在夏季范围内
        LocalDateTime testTime = LocalDateTime.of(2024, 7, 15, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试月日范围条件 - 单个范围不匹配
     */
    @Test
    void testMonthDayRangeEvaluation_SingleRangeNotMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.MonthDayRange monthDayRange = new TimeCondition.MonthDayRange("06-01", "08-31", "夏季");
        condition.setMonthDayRanges(Arrays.asList(monthDayRange));
        condition.setLogic(Operators.Logic.AND);

        // 2024年12月15日 - 不在夏季范围内
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 15, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试月日范围条件 - 跨年范围匹配
     */
    @Test
    void testMonthDayRangeEvaluation_CrossYearMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.MonthDayRange monthDayRange = new TimeCondition.MonthDayRange("12-01", "02-28", "冬季");
        condition.setMonthDayRanges(Arrays.asList(monthDayRange));
        condition.setLogic(Operators.Logic.AND);

        // 测试12月（跨年范围的前半部分）
        LocalDateTime testTime1 = LocalDateTime.of(2024, 12, 15, 10, 0, 0);
        boolean result1 = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime1);
        assertTrue(result1);

        // 测试1月（跨年范围的后半部分）
        LocalDateTime testTime2 = LocalDateTime.of(2025, 1, 15, 10, 0, 0);
        boolean result2 = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime2);
        assertTrue(result2);

        // 测试2月（跨年范围的后半部分）
        LocalDateTime testTime3 = LocalDateTime.of(2025, 2, 15, 10, 0, 0);
        boolean result3 = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime3);
        assertTrue(result3);
    }

    /**
     * 测试月日范围条件 - 跨年范围不匹配
     */
    @Test
    void testMonthDayRangeEvaluation_CrossYearNotMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.MonthDayRange monthDayRange = new TimeCondition.MonthDayRange("12-01", "02-28", "冬季");
        condition.setMonthDayRanges(Arrays.asList(monthDayRange));
        condition.setLogic(Operators.Logic.AND);

        // 测试7月（不在跨年冬季范围内）
        LocalDateTime testTime = LocalDateTime.of(2024, 7, 15, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试月日范围条件 - 多个范围OR关系
     */
    @Test
    void testMonthDayRangeEvaluation_MultipleRangesOR() {
        // Given
        TimeCondition condition = new TimeCondition();
        List<TimeCondition.MonthDayRange> monthDayRanges = Arrays.asList(
            new TimeCondition.MonthDayRange("03-01", "05-31", "春季"),
            new TimeCondition.MonthDayRange("09-01", "11-30", "秋季")
        );
        condition.setMonthDayRanges(monthDayRanges);
        condition.setLogic(Operators.Logic.AND);

        // 测试春季
        LocalDateTime springTime = LocalDateTime.of(2024, 4, 15, 10, 0, 0);
        boolean springResult = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), springTime);
        assertTrue(springResult);

        // 测试秋季
        LocalDateTime autumnTime = LocalDateTime.of(2024, 10, 15, 10, 0, 0);
        boolean autumnResult = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), autumnTime);
        assertTrue(autumnResult);

        // 测试夏季（不在范围内）
        LocalDateTime summerTime = LocalDateTime.of(2024, 7, 15, 10, 0, 0);
        boolean summerResult = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), summerTime);
        assertFalse(summerResult);
    }

    /**
     * 测试月日范围条件 - 年度重复性
     */
    @Test
    void testMonthDayRangeEvaluation_YearlyRepeat() {
        // Given
        TimeCondition condition = new TimeCondition();
        TimeCondition.MonthDayRange monthDayRange = new TimeCondition.MonthDayRange("06-01", "08-31", "夏季");
        condition.setMonthDayRanges(Arrays.asList(monthDayRange));
        condition.setLogic(Operators.Logic.AND);

        // 测试不同年份的同一月日
        LocalDateTime testTime2024 = LocalDateTime.of(2024, 7, 15, 10, 0, 0);
        boolean result2024 = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime2024);
        assertTrue(result2024);

        LocalDateTime testTime2025 = LocalDateTime.of(2025, 7, 15, 10, 0, 0);
        boolean result2025 = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime2025);
        assertTrue(result2025);

        LocalDateTime testTime2026 = LocalDateTime.of(2026, 7, 15, 10, 0, 0);
        boolean result2026 = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime2026);
        assertTrue(result2026);
    }
}
