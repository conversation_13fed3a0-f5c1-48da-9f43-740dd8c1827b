package com.inxaiot.ruleengine.trigger.time;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GlobalCalendar测试类
 * 
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2025-07-07
 */
class GlobalCalendarTest {

    private GlobalCalendar globalCalendar;

    @BeforeEach
    void setUp() {
        globalCalendar = new GlobalCalendar();
    }

    @Test
    void testExcludeMonthDays() {
        // 设置排除月日
        List<String> excludeMonthDays = Arrays.asList("01-01", "05-01", "10-01", "12-25");
        globalCalendar.setExcludeMonthDays(excludeMonthDays);

        // 测试排除月日检查
        assertTrue(globalCalendar.isExcludedMonthDay("01-01"));
        assertTrue(globalCalendar.isExcludedMonthDay("12-25"));
        assertFalse(globalCalendar.isExcludedMonthDay("06-15"));

        // 测试日期检查（isHoliday方法）
        assertTrue(globalCalendar.isHoliday(LocalDate.of(2024, 1, 1)));
        assertTrue(globalCalendar.isHoliday(LocalDate.of(2025, 12, 25)));
        assertFalse(globalCalendar.isHoliday(LocalDate.of(2024, 6, 15)));
    }

    @Test
    void testDateRanges() {
        // 设置夏季时间段
        MonthDayRange summerRange = new MonthDayRange("06-01", "08-31", "夏季");
        globalCalendar.addDateRange("SUMMER", summerRange);

        // 设置冬季时间段（跨年）
        MonthDayRange winterRange1 = new MonthDayRange("12-01", "12-31", "冬季前半段");
        MonthDayRange winterRange2 = new MonthDayRange("01-01", "02-28", "冬季后半段");
        globalCalendar.addDateRange("WINTER", winterRange1);
        globalCalendar.addDateRange("WINTER", winterRange2);

        // 测试夏季
        assertTrue(globalCalendar.isInDateRange("SUMMER", "06-01"));
        assertTrue(globalCalendar.isInDateRange("SUMMER", "07-15"));
        assertTrue(globalCalendar.isInDateRange("SUMMER", "08-31"));
        assertFalse(globalCalendar.isInDateRange("SUMMER", "05-31"));
        assertFalse(globalCalendar.isInDateRange("SUMMER", "09-01"));

        // 测试冬季（跨年）
        assertTrue(globalCalendar.isInDateRange("WINTER", "12-01"));
        assertTrue(globalCalendar.isInDateRange("WINTER", "12-25"));
        assertTrue(globalCalendar.isInDateRange("WINTER", "01-01"));
        assertTrue(globalCalendar.isInDateRange("WINTER", "02-28"));
        assertFalse(globalCalendar.isInDateRange("WINTER", "11-30"));
        assertFalse(globalCalendar.isInDateRange("WINTER", "03-01"));

        // 测试不存在的时间段
        assertFalse(globalCalendar.isInDateRange("SPRING", "04-15"));
    }

    @Test
    void testIsInSeason() {
        // 设置季节时间段
        MonthDayRange summerRange = new MonthDayRange("06-01", "08-31", "夏季");
        globalCalendar.addDateRange("SUMMER", summerRange);

        MonthDayRange winterRange = new MonthDayRange("12-01", "02-28", "冬季");
        globalCalendar.addDateRange("WINTER", winterRange);

        // 测试季节检查
        assertTrue(globalCalendar.isInSeason(LocalDate.of(2024, 7, 15), "SUMMER"));
        assertFalse(globalCalendar.isInSeason(LocalDate.of(2024, 4, 15), "SUMMER"));

        assertTrue(globalCalendar.isInSeason(LocalDate.of(2024, 12, 25), "WINTER"));
        assertTrue(globalCalendar.isInSeason(LocalDate.of(2024, 1, 15), "WINTER"));
        assertFalse(globalCalendar.isInSeason(LocalDate.of(2024, 6, 15), "WINTER"));

        // 测试ALL季节
        assertTrue(globalCalendar.isInSeason(LocalDate.of(2024, 6, 15), "ALL"));
        assertTrue(globalCalendar.isInSeason(LocalDate.of(2024, 12, 25), "ALL"));

        // 测试null季节
        assertTrue(globalCalendar.isInSeason(LocalDate.of(2024, 6, 15), null));
    }

    @Test
    void testGetAvailableDateRangeNames() {
        // 初始状态应该为空
        assertTrue(globalCalendar.getAvailableDateRangeNames().isEmpty());

        // 添加时间段
        globalCalendar.addDateRange("SUMMER", new MonthDayRange("06-01", "08-31"));
        globalCalendar.addDateRange("WINTER", new MonthDayRange("12-01", "02-28"));

        // 检查可用时间段名称
        assertEquals(2, globalCalendar.getAvailableDateRangeNames().size());
        assertTrue(globalCalendar.getAvailableDateRangeNames().contains("SUMMER"));
        assertTrue(globalCalendar.getAvailableDateRangeNames().contains("WINTER"));
    }

    @Test
    void testAddAndRemoveOperations() {
        // 测试添加排除月日
        globalCalendar.addExcludeMonthDay("01-01");
        globalCalendar.addExcludeMonthDay("12-25");
        assertEquals(2, globalCalendar.getExcludeMonthDays().size());

        // 测试移除排除月日
        globalCalendar.removeExcludeMonthDay("01-01");
        assertEquals(1, globalCalendar.getExcludeMonthDays().size());
        assertFalse(globalCalendar.isExcludedMonthDay("01-01"));
        assertTrue(globalCalendar.isExcludedMonthDay("12-25"));

        // 测试设置时间段
        List<MonthDayRange> summerRanges = Arrays.asList(
            new MonthDayRange("06-01", "08-31", "夏季")
        );
        globalCalendar.setDateRange("SUMMER", summerRanges);
        assertTrue(globalCalendar.isInDateRange("SUMMER", "07-15"));

        // 测试移除时间段
        globalCalendar.removeDateRange("SUMMER");
        assertFalse(globalCalendar.isInDateRange("SUMMER", "07-15"));
    }

    @Test
    void testIsEmpty() {
        // 初始状态应该为空
        assertTrue(globalCalendar.isEmpty());

        // 添加排除月日后不应该为空
        globalCalendar.addExcludeMonthDay("01-01");
        assertFalse(globalCalendar.isEmpty());

        // 清空后应该为空
        globalCalendar.setExcludeMonthDays(Arrays.asList());
        assertTrue(globalCalendar.isEmpty());

        // 添加时间段后不应该为空
        globalCalendar.addDateRange("SUMMER", new MonthDayRange("06-01", "08-31"));
        assertFalse(globalCalendar.isEmpty());
    }

    @Test
    void testGetConfigCount() {
        assertEquals(0, globalCalendar.getConfigCount());

        // 添加排除月日
        globalCalendar.addExcludeMonthDay("01-01");
        globalCalendar.addExcludeMonthDay("12-25");
        assertEquals(2, globalCalendar.getConfigCount());

        // 添加时间段
        globalCalendar.addDateRange("SUMMER", new MonthDayRange("06-01", "08-31"));
        globalCalendar.addDateRange("WINTER", new MonthDayRange("12-01", "02-28"));
        assertEquals(4, globalCalendar.getConfigCount()); // 2个排除月日 + 2个时间段
    }

    @Test
    void testDateRangeWithLocalDate() {
        // 设置时间段
        MonthDayRange summerRange = new MonthDayRange("06-01", "08-31", "夏季");
        globalCalendar.addDateRange("SUMMER", summerRange);

        // 测试LocalDate版本的isInDateRange方法
        assertTrue(globalCalendar.isInDateRange("SUMMER", LocalDate.of(2024, 6, 1)));
        assertTrue(globalCalendar.isInDateRange("SUMMER", LocalDate.of(2024, 7, 15)));
        assertTrue(globalCalendar.isInDateRange("SUMMER", LocalDate.of(2024, 8, 31)));
        assertFalse(globalCalendar.isInDateRange("SUMMER", LocalDate.of(2024, 5, 31)));
        assertFalse(globalCalendar.isInDateRange("SUMMER", LocalDate.of(2024, 9, 1)));
    }

    @Test
    void testNullHandling() {
        // 测试null值处理
        assertFalse(globalCalendar.isExcludedMonthDay(null));
        assertFalse(globalCalendar.isHoliday(null));
        assertFalse(globalCalendar.isInDateRange(null, "07-15"));
        assertFalse(globalCalendar.isInDateRange("SUMMER", (String) null));
        assertFalse(globalCalendar.isInDateRange("SUMMER", (LocalDate) null));

        // 测试添加null值
        globalCalendar.addExcludeMonthDay(null);
        globalCalendar.addDateRange("SUMMER", null);
        globalCalendar.addDateRange(null, new MonthDayRange("06-01", "08-31"));
        
        // 应该没有添加任何内容
        assertTrue(globalCalendar.isEmpty());
    }

    @Test
    void testToString() {
        globalCalendar.addExcludeMonthDay("01-01");
        globalCalendar.addDateRange("SUMMER", new MonthDayRange("06-01", "08-31"));
        
        String str = globalCalendar.toString();
        assertTrue(str.contains("excludeMonthDays=1"));
        assertTrue(str.contains("dateRanges=1"));
        assertTrue(str.contains("configCount=2"));
    }
}
