package com.inxaiot.ruleengine.core.engine;

import com.inxaiot.ruleengine.RuleEngineTestConfig;
import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.core.FactKey;
import com.inxaiot.ruleengine.core.action.ActionExecutor;
import com.inxaiot.ruleengine.core.adapter.RuleAdapterService;
import com.inxaiot.ruleengine.core.analyzer.DependencyAnalyzer;
import com.inxaiot.ruleengine.core.analyzer.FactsBuilder;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.storage.RuleService;
import com.inxaiot.ruleengine.common.context.SystemContextService;
import org.jeasy.rules.api.Facts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RuleEngineServiceImpl unit test
 * Test core functionality of rule engine service
 */
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@Import(RuleEngineTestConfig.class)
public class RuleEngineServiceImplTest {

    @Mock
    private RuleService ruleService;

    @Mock
    private RuleAdapterService ruleAdapterService;

    @Mock
    private SystemContextService systemContextService;

    @Mock
    private ThreadPoolTaskExecutor ruleEvaluationExecutor;

    @Mock
    private ActionExecutor actionExecutor;

    @Mock
    private DependencyAnalyzer dependencyAnalyzer;

    @Mock
    private FactsBuilder factsBuilder;

    @Mock
    private StateManager stateManager;

    private RuleEngineServiceImpl ruleEngineService;

    @BeforeEach
    void setUp() {
        ruleEngineService = new RuleEngineServiceImpl(
            ruleService,
            ruleAdapterService,
            systemContextService,
            ruleEvaluationExecutor,
            actionExecutor,
            dependencyAnalyzer,
            factsBuilder,
            stateManager
        );
    }

    /**
     * Test processing device event - normal flow
     */
    @Test
    void testProcessDeviceEvent_Success() {
        // Given
        String deviceCode = "temp_sensor_001";
        String pointId = "temperature";
        Object value = 30.0;

        // Mock async executor to execute task directly (sync test)
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock finding related rules
        RuleDefinition rule = TestDataFactory.createSimpleEventDrivenRule();
        when(ruleService.findAllEnabledRulesRelevantToDevice(deviceCode))
            .thenReturn(Arrays.asList(rule));

        // Mock dependency analyzer to return true for rule relevance
        when(dependencyAnalyzer.isRuleRelatedToDevice(eq(rule), eq(deviceCode), eq(pointId)))
            .thenReturn(true);

        // Mock Facts building
        Facts facts = new Facts();
        facts.put("deviceCode", deviceCode);
        facts.put("pointId", pointId);
        facts.put("value", value);
        when(factsBuilder.buildCompleteFactsForRule(eq(rule), eq(deviceCode), eq(pointId), eq(value)))
            .thenReturn(facts);

        // When
        ruleEngineService.processDeviceValue(deviceCode, pointId, value);

        // Then
        verify(ruleEvaluationExecutor).execute(any(Runnable.class));
        verify(ruleService).findAllEnabledRulesRelevantToDevice(deviceCode);
        verify(factsBuilder).buildCompleteFactsForRule(eq(rule), eq(deviceCode), eq(pointId), eq(value));
    }

    /**
     * Test processing device event - no relevant rules
     */
    @Test
    void testProcessDeviceEvent_NoRelevantRules() {
        // Given
        String deviceCode = "unknown_device";
        String pointId = "unknown_point";
        Object value = 25.0;

        // Mock async executor to execute task directly
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock no related rules found
        when(ruleService.findAllEnabledRulesRelevantToDevice(deviceCode))
            .thenReturn(Arrays.asList());

        // When
        ruleEngineService.processDeviceValue(deviceCode, pointId, value);

        // Then
        verify(ruleEvaluationExecutor).execute(any(Runnable.class));
        verify(ruleService).findAllEnabledRulesRelevantToDevice(deviceCode);
        // Should not call Facts builder
        verify(factsBuilder, never()).buildCompleteFactsForRule(any(), any(), any(), any());
    }

    /**
     * Test handling state change event - VALUE_CHANGED type
     */
    @Test
    void testHandleStateChangeEvent_ValueChanged() {
        // Given
        StateChangeEvent event = TestDataFactory.createStateChangeEvent(
            "temp_sensor_001", "temperature", 25.0, 30.0);

        // Mock async executor to execute task directly
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock finding related rules
        RuleDefinition rule = TestDataFactory.createSimpleEventDrivenRule();
        when(ruleService.findAllEnabledRulesRelevantToDevice(event.getDeviceCode()))
            .thenReturn(Arrays.asList(rule));

        // Mock dependency analyzer
        when(dependencyAnalyzer.isRuleRelatedToDevice(eq(rule), eq(event.getDeviceCode()), eq(event.getPointId())))
            .thenReturn(true);

        // Mock Facts building
        Facts facts = new Facts();
        when(factsBuilder.buildCompleteFactsForRule(any(), any(), any(), any()))
            .thenReturn(facts);

        // When
        ruleEngineService.handleStateChangeEvent(event);

        // Then
        verify(ruleService).findAllEnabledRulesRelevantToDevice(event.getDeviceCode());
        verify(factsBuilder).buildCompleteFactsForRule(any(), eq(event.getDeviceCode()), 
            eq(event.getPointId()), eq(event.getCurrentValue()));
    }

    /**
     * Test triggering rule activation
     */
    @Test
    void testTriggerRuleActivation() throws InterruptedException {
        // Given
        long ruleId = 1L;
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId(ruleId);

        when(ruleService.findRuleById(ruleId)).thenReturn(rule);
        when(factsBuilder.buildCompleteFactsForRule(any(), any(), any(), any())).thenReturn(new Facts());

        // Mock the executor to actually execute the runnable
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // When
        ruleEngineService.triggerRuleActivation(ruleId, FactKey.TRIGGERED_EVENT_TYPE_TIME);

        // Then
        verify(ruleService).findRuleById(ruleId);
        verify(factsBuilder).buildCompleteFactsForRule(eq(rule), isNull(), isNull(), isNull());
    }

    /**
     * Test triggering rule deactivation
     */
    @Test
    void testTriggerRuleDeactivation() throws InterruptedException {
        // Given
        long ruleId = 1L;
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId(ruleId);
        // Create a simple deactivation action
        ActionDefinition deactivationAction = new ActionDefinition();
        deactivationAction.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        deactivationAction.setEnabled(true);
        rule.setDeactivationActions(Arrays.asList(deactivationAction));

        when(ruleService.findRuleById(ruleId)).thenReturn(rule);

        // Mock the executor to actually execute the runnable
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // When
        ruleEngineService.triggerRuleDeactivation(ruleId, FactKey.TRIGGERED_EVENT_TYPE_TIME);

        // Then
        verify(ruleService).findRuleById(ruleId);
    }

    /**
     * Test exception handling - exception during rule processing
     */
    @Test
    void testProcessDeviceEvent_ExceptionHandling() {
        // Given
        String deviceCode = "temp_sensor_001";
        String pointId = "temperature";
        Object value = 30.0;

        // Mock async executor to execute task directly
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(ruleEvaluationExecutor).execute(any(Runnable.class));

        // Mock exception when finding rules
        when(ruleService.findAllEnabledRulesRelevantToDevice(deviceCode))
            .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then - should not throw exception, should be handled internally
        assertDoesNotThrow(() -> {
            ruleEngineService.processDeviceValue(deviceCode, pointId, value);
        });

        verify(ruleService).findAllEnabledRulesRelevantToDevice(deviceCode);
    }
}
