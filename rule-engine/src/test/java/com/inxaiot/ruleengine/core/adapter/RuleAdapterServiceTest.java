package com.inxaiot.ruleengine.core.adapter;

import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.core.FactKey;
import com.inxaiot.ruleengine.core.action.ActionExecutor;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.trigger.time.TimeConditionEvaluator;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RuleAdapterService unit test
 * Test core functionality of rule adapter
 */
@ExtendWith(MockitoExtension.class)
public class RuleAdapterServiceTest {

    @Mock
    private TimeConditionEvaluator timeConditionEvaluator;

    @Mock
    private ActionExecutor actionExecutor;

    private RuleAdapterService ruleAdapterService;

    @BeforeEach
    void setUp() {
        ruleAdapterService = new RuleAdapterService(timeConditionEvaluator, actionExecutor);
    }

    /**
     * Test adapting event-driven rule - normal flow
     */
    @Test
    void testAdaptEventDrivenRule_Success() {
        // Given
        RuleDefinition ruleDefinition = TestDataFactory.createSimpleEventDrivenRule();
        
        // When
        Rule adaptedRule = ruleAdapterService.adapt(ruleDefinition);

        // Then
        assertNotNull(adaptedRule);
        assertEquals(ruleDefinition.getRuleName(), adaptedRule.getName());
        assertEquals(ruleDefinition.getDescription(), adaptedRule.getDescription());
        assertEquals(ruleDefinition.getPriority(), adaptedRule.getPriority());
    }

    /**
     * Test adapting time-driven rule
     */
    @Test
    void testAdaptTimeDrivenRule_Success() {
        // Given
        RuleDefinition ruleDefinition = TestDataFactory.createTimeDrivenRule();
        
        // When
        Rule adaptedRule = ruleAdapterService.adapt(ruleDefinition);

        // Then
        assertNotNull(adaptedRule);
        assertEquals(ruleDefinition.getRuleName(), adaptedRule.getName());
        assertEquals(ruleDefinition.getDescription(), adaptedRule.getDescription());
        assertEquals(ruleDefinition.getPriority(), adaptedRule.getPriority());
    }

    /**
     * Test event-driven rule condition evaluation - time and device conditions met
     */
    @Test
    void testEventDrivenRuleCondition_TimeAndDeviceConditionsMet() {
        // Given
        RuleDefinition ruleDefinition = TestDataFactory.createSimpleEventDrivenRule();
        Rule adaptedRule = ruleAdapterService.adapt(ruleDefinition);

        // Mock time condition satisfied
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(true);

        // Create Facts, mock device condition satisfied
        Facts facts = new Facts();
        facts.put("eventType", "EVENT_DRIVEN");
        facts.put("temp_sensor_001.temperature", 30.0); // greater than 28 degrees
        facts.put("temp_sensor_001.temperature_dataType", "DOUBLE");
        facts.put("temp_sensor_001.temperature_updateTime", System.currentTimeMillis());

        // When
        boolean conditionMet = adaptedRule.evaluate(facts);

        // Then
        assertTrue(conditionMet);
        verify(timeConditionEvaluator).isTimeConditionMet(any(List.class), any(LocalDateTime.class));
    }

    /**
     * Test event-driven rule condition evaluation - time condition not met
     */
    @Test
    void testEventDrivenRuleCondition_TimeConditionNotMet() {
        // Given
        RuleDefinition ruleDefinition = TestDataFactory.createSimpleEventDrivenRule();
        Rule adaptedRule = ruleAdapterService.adapt(ruleDefinition);

        // Mock time condition not satisfied
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(false);

        // Create Facts
        Facts facts = new Facts();
        facts.put("eventType", "EVENT_DRIVEN");
        facts.put("temp_sensor_001_temperature", 30.0);

        // When
        boolean conditionMet = adaptedRule.evaluate(facts);

        // Then
        assertFalse(conditionMet);
        verify(timeConditionEvaluator).isTimeConditionMet(any(List.class), any(LocalDateTime.class));
    }

    /**
     * Test time-driven rule condition evaluation - time trigger event exists
     */
    @Test
    void testTimeDrivenRuleCondition_TimeTriggerEventExists() {
        // Given
        RuleDefinition ruleDefinition = TestDataFactory.createTimeDrivenRule();
        Rule adaptedRule = ruleAdapterService.adapt(ruleDefinition);

        // Note: Time-driven rules don't use timeConditionEvaluator directly

        // Create Facts, mock time trigger event
        Facts facts = new Facts();
        facts.put(FactKey.TRIGGERED_EVENT_TYPE, FactKey.TRIGGERED_EVENT_TYPE_TIME);
        facts.put(FactKey.TRIGGERED_RULE_ID, ruleDefinition.getRuleId());

        // When
        boolean conditionMet = adaptedRule.evaluate(facts);

        // Then
        assertTrue(conditionMet);
        // Note: Time-driven rules don't call timeConditionEvaluator directly,
        // they only check for time trigger event existence
    }

    /**
     * Test disabled rule condition evaluation
     */
    @Test
    void testDisabledRuleCondition() {
        // Given
        RuleDefinition ruleDefinition = TestDataFactory.createSimpleEventDrivenRule();
        ruleDefinition.setEnabled(false); // disable rule
        Rule adaptedRule = ruleAdapterService.adapt(ruleDefinition);

        // Create Facts
        Facts facts = new Facts();
        facts.put("eventType", "EVENT_DRIVEN");

        // When
        boolean conditionMet = adaptedRule.evaluate(facts);

        // Then
        assertFalse(conditionMet);
        // Disabled rules should not call time condition evaluator
        verify(timeConditionEvaluator, never()).isTimeConditionMet(any(List.class), any());
    }

    /**
     * Test rule action execution
     */
    @Test
    void testRuleActionExecution() {
        // Given
        RuleDefinition ruleDefinition = TestDataFactory.createSimpleEventDrivenRule();
        Rule adaptedRule = ruleAdapterService.adapt(ruleDefinition);

        Facts facts = new Facts();
        facts.put("triggeredRuleId", ruleDefinition.getRuleId());

        // When
        try {
            adaptedRule.execute(facts);
        } catch (Exception e) {
            fail("Rule execution should not throw exception: " + e.getMessage());
        }

        // Then
        // Verify action executor is called
        verify(actionExecutor, times(ruleDefinition.getActions().size()))
            .executeAction(any(ActionDefinition.class), eq(facts));
    }
}
