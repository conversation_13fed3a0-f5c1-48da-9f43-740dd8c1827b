package com.inxaiot.ruleengine.storage;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity;
import com.inxaiot.ruleengine.storage.mapper.GlobalCalendarMapper;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.MonthDayRange;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GlobalCalendarService测试类
 *
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2025-07-07
 */
@ExtendWith(MockitoExtension.class)
class GlobalCalendarServiceTest {

    @Mock
    private GlobalCalendarMapper globalCalendarMapper;

    @Mock
    private ObjectMapper objectMapper;

    private GlobalCalendarService globalCalendarService;

    @BeforeEach
    void setUp() {
        globalCalendarService = new GlobalCalendarService(globalCalendarMapper, objectMapper);
    }

    @Test
    void testLoadGlobalCalendar_WhenEntityExists() throws Exception {
        // 准备测试数据
        String bizId = "default";
        GlobalCalendarEntity entity = new GlobalCalendarEntity(bizId);
        entity.setExcludeMonthDays("[\"01-01\",\"12-25\"]");
        
        Map<String, List<MonthDayRange>> dateRanges = new HashMap<>();
        dateRanges.put("SUMMER", Arrays.asList(new MonthDayRange("06-01", "08-31", "夏季")));
        entity.setMonthDateRanges("{\"SUMMER\":[{\"startDate\":\"06-01\",\"endDate\":\"08-31\",\"description\":\"夏季\"}]}");

        // Mock mapper返回
        when(globalCalendarMapper.findGlobalCalendarByBizId(bizId)).thenReturn(entity);
        
        // Mock JSON解析
        List<String> excludeMonthDays = Arrays.asList("01-01", "12-25");
        when(objectMapper.readValue(eq("[\"01-01\",\"12-25\"]"), any(TypeReference.class)))
            .thenReturn(excludeMonthDays);
        
        when(objectMapper.readValue(eq("{\"SUMMER\":[{\"startDate\":\"06-01\",\"endDate\":\"08-31\",\"description\":\"夏季\"}]}"), any(TypeReference.class)))
            .thenReturn(dateRanges);

        // 执行测试
        GlobalCalendar result = globalCalendarService.loadGlobalCalendar();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getExcludeMonthDays().size());
        assertTrue(result.getExcludeMonthDays().contains("01-01"));
        assertTrue(result.getExcludeMonthDays().contains("12-25"));
        assertEquals(1, result.getMonthDateRanges().size());
        assertTrue(result.getMonthDateRanges().containsKey("SUMMER"));

        // 验证mapper调用
        verify(globalCalendarMapper).findGlobalCalendarByBizId(bizId);
    }

    @Test
    void testLoadGlobalCalendar_WhenEntityNotExists() {
        // Mock mapper返回null
        when(globalCalendarMapper.findGlobalCalendarByBizId("default")).thenReturn(null);

        // 执行测试
        GlobalCalendar result = globalCalendarService.loadGlobalCalendar();

        // 验证结果 - 应该返回空的日历对象
        assertNotNull(result);
        assertTrue(result.getExcludeMonthDays().isEmpty());
        assertTrue(result.getMonthDateRanges().isEmpty());
    }

    @Test
    void testSaveGlobalCalendar_WhenEntityExists() throws Exception {
        // 准备测试数据
        GlobalCalendar globalCalendar = new GlobalCalendar();
        globalCalendar.setExcludeMonthDays(Arrays.asList("01-01", "12-25"));
        
        Map<String, List<MonthDayRange>> dateRanges = new HashMap<>();
        dateRanges.put("SUMMER", Arrays.asList(new MonthDayRange("06-01", "08-31", "夏季")));
        globalCalendar.setMonthDateRanges(dateRanges);

        // Mock mapper返回
        when(globalCalendarMapper.existsGlobalCalendar("default")).thenReturn(true);
        
        // Mock JSON序列化
        when(objectMapper.writeValueAsString(globalCalendar.getExcludeMonthDays()))
            .thenReturn("[\"01-01\",\"12-25\"]");
        when(objectMapper.writeValueAsString(globalCalendar.getMonthDateRanges()))
            .thenReturn("{\"SUMMER\":[{\"startDate\":\"06-01\",\"endDate\":\"08-31\",\"description\":\"夏季\"}]}");

        // 执行测试
        assertDoesNotThrow(() -> globalCalendarService.saveGlobalCalendar(globalCalendar));

        // 验证mapper调用
        verify(globalCalendarMapper).existsGlobalCalendar("default");
        verify(globalCalendarMapper).updateGlobalCalendar(any(GlobalCalendarEntity.class));
        verify(globalCalendarMapper, never()).insertGlobalCalendar(any(GlobalCalendarEntity.class));
    }

    @Test
    void testSaveGlobalCalendar_WhenEntityNotExists() throws Exception {
        // 准备测试数据
        GlobalCalendar globalCalendar = new GlobalCalendar();
        globalCalendar.setExcludeMonthDays(Arrays.asList("01-01"));

        // Mock mapper返回
        when(globalCalendarMapper.existsGlobalCalendar("default")).thenReturn(false);
        
        // Mock JSON序列化
        when(objectMapper.writeValueAsString(globalCalendar.getExcludeMonthDays()))
            .thenReturn("[\"01-01\"]");
        when(objectMapper.writeValueAsString(globalCalendar.getMonthDateRanges()))
            .thenReturn("{}");

        // 执行测试
        assertDoesNotThrow(() -> globalCalendarService.saveGlobalCalendar(globalCalendar));

        // 验证mapper调用
        verify(globalCalendarMapper).existsGlobalCalendar("default");
        verify(globalCalendarMapper).insertGlobalCalendar(any(GlobalCalendarEntity.class));
        verify(globalCalendarMapper, never()).updateGlobalCalendar(any(GlobalCalendarEntity.class));
    }

    @Test
    void testRefreshCalendarCache() {
        // 执行测试
        assertDoesNotThrow(() -> globalCalendarService.refreshCalendarCache());
    }

    @Test
    void testGetSystemConfig() {
        // 执行测试
        Map<String, Object> config = globalCalendarService.getSystemConfig();

        // 验证结果
        assertNotNull(config);
        assertEquals("Asia/Shanghai", config.get("timezone"));
        assertEquals("zh_CN", config.get("locale"));
        assertEquals("yyyy-MM-dd", config.get("dateFormat"));
        assertEquals("HH:mm:ss", config.get("timeFormat"));
    }

    @Test
    void testLoadGlobalCalendarWithBizId() throws Exception {
        // 准备测试数据
        String bizId = "test_biz";
        GlobalCalendarEntity entity = new GlobalCalendarEntity(bizId);
        entity.setExcludeMonthDays("[\"05-01\"]");
        entity.setMonthDateRanges("{}");

        // Mock mapper返回
        when(globalCalendarMapper.findGlobalCalendarByBizId(bizId)).thenReturn(entity);
        
        // Mock JSON解析
        when(objectMapper.readValue(eq("[\"05-01\"]"), any(TypeReference.class)))
            .thenReturn(Arrays.asList("05-01"));
        when(objectMapper.readValue(eq("{}"), any(TypeReference.class)))
            .thenReturn(new HashMap<>());

        // 执行测试
        GlobalCalendar result = globalCalendarService.loadGlobalCalendar(bizId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getExcludeMonthDays().size());
        assertTrue(result.getExcludeMonthDays().contains("05-01"));
        assertTrue(result.getMonthDateRanges().isEmpty());

        // 验证mapper调用
        verify(globalCalendarMapper).findGlobalCalendarByBizId(bizId);
    }

    @Test
    void testSaveGlobalCalendarWithBizId() throws Exception {
        // 准备测试数据
        String bizId = "test_biz";
        GlobalCalendar globalCalendar = new GlobalCalendar();
        globalCalendar.setExcludeMonthDays(Arrays.asList("10-01"));

        // Mock mapper返回
        when(globalCalendarMapper.existsGlobalCalendar(bizId)).thenReturn(false);
        
        // Mock JSON序列化
        when(objectMapper.writeValueAsString(globalCalendar.getExcludeMonthDays()))
            .thenReturn("[\"10-01\"]");
        when(objectMapper.writeValueAsString(globalCalendar.getMonthDateRanges()))
            .thenReturn("{}");

        // 执行测试
        assertDoesNotThrow(() -> globalCalendarService.saveGlobalCalendar(globalCalendar, bizId));

        // 验证mapper调用
        verify(globalCalendarMapper).existsGlobalCalendar(bizId);
        verify(globalCalendarMapper).insertGlobalCalendar(any(GlobalCalendarEntity.class));
    }
}
