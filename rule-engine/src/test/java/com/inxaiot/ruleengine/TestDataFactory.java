package com.inxaiot.ruleengine;

import com.inxaiot.ruleengine.core.definition.*;
import com.inxaiot.ruleengine.device.state.StateCondition;
import com.inxaiot.ruleengine.device.state.DevicePointState;
import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.MonthDayRange;

import java.time.LocalDate;
import java.util.*;

/**
 * 测试数据工厂类
 * 提供各种测试用的数据对象，包括规则定义、全局日历、月日时间段等
 *
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2025-07-07
 */
public class TestDataFactory {

    /**
     * 创建简单的事件驱动规则
     */
    public static RuleDefinition createSimpleEventDrivenRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId(1L);
        rule.setRuleName("测试规则-温度控制");
        rule.setBizId("test_biz");
        rule.setPriority(1);
        rule.setEnabled(true);

        // 设置时间条件
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        timeCondition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        timeCondition.setLogic(Operators.Logic.AND);
        rule.setTimeConditions(Arrays.asList(timeCondition));

        // 设置触发条件
        TriggerCondition triggerCondition = new TriggerCondition();
        triggerCondition.setLogic(Operators.Logic.AND);

        DeviceCondition deviceCondition = new DeviceCondition();
        deviceCondition.setSourceDeviceCode("temp_sensor_001");
        deviceCondition.setPointId("temperature");
        deviceCondition.setOperator(Operators.Basic.GREATER_THAN);
        deviceCondition.setValue(28.0);
        deviceCondition.setDurationSeconds(5);

        triggerCondition.setConditions(Arrays.asList(deviceCondition));
        rule.setTriggerCondition(triggerCondition);

        // 设置动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        Map<String, Object> params = new HashMap<>();
        params.put("command", "turn_on");
        params.put("temperature", 24);
        action.setParams(params);
        action.setEnabled(true);

        rule.setActions(Arrays.asList(action));

        return rule;
    }

    /**
     * 创建时间驱动规则
     */
    public static RuleDefinition createTimeDrivenRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId(2L);
        rule.setRuleName("测试规则-定时开灯");
        rule.setBizId("test_biz");
        rule.setPriority(2);
        rule.setEnabled(true);

        // 设置时间条件
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        timeCondition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        timeCondition.setLogic(Operators.Logic.AND);
        rule.setTimeConditions(Arrays.asList(timeCondition));

        // 设置动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        Map<String, Object> params = new HashMap<>();
        params.put("command", "turn_on");
        action.setParams(params);
        action.setEnabled(true);

        rule.setActions(Arrays.asList(action));

        return rule;
    }

    /**
     * 创建多条件规则
     */
    public static RuleDefinition createMultiConditionRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId(3L);
        rule.setRuleName("测试规则-多条件");
        rule.setBizId("test_biz");
        rule.setPriority(1);
        rule.setEnabled(true);

        // 设置触发条件
        TriggerCondition triggerCondition = new TriggerCondition();
        triggerCondition.setLogic(Operators.Logic.AND);

        // 温度条件
        DeviceCondition tempCondition = new DeviceCondition();
        tempCondition.setSourceDeviceCode("temp_sensor_001");
        tempCondition.setPointId("temperature");
        tempCondition.setOperator(Operators.Basic.GREATER_THAN);
        tempCondition.setValue(30.0);
        tempCondition.setDurationSeconds(10);

        // 占用条件
        DeviceCondition occupancyCondition = new DeviceCondition();
        occupancyCondition.setSourceDeviceCode("occupancy_sensor_001");
        occupancyCondition.setPointId("occupancy_status");
        occupancyCondition.setOperator(Operators.Basic.EQUALS);
        occupancyCondition.setValue("OCCUPIED");
        occupancyCondition.setDurationSeconds(0);

        triggerCondition.setConditions(Arrays.asList(tempCondition, occupancyCondition));
        rule.setTriggerCondition(triggerCondition);

        // 设置动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        Map<String, Object> params = new HashMap<>();
        params.put("command", "turn_on");
        params.put("temperature", 22);
        action.setParams(params);
        action.setEnabled(true);

        rule.setActions(Arrays.asList(action));

        return rule;
    }

    /**
     * 创建设备点位状态
     */
    public static DevicePointState createDevicePointState(String deviceCode, String pointId, Object value) {
        DevicePointState state = new DevicePointState(deviceCode, pointId);
        state.updateValue(value);
        return state;
    }

    /**
     * 创建状态条件
     */
    public static StateCondition createStateCondition(String deviceCode, String pointId, String operator, Object value, long setDurationSeconds) {
        StateCondition condition = new StateCondition();
        condition.setConditionId(UUID.randomUUID().toString());
        condition.setDeviceCode(deviceCode);
        condition.setPointId(pointId);
        condition.setOperator(operator);
        condition.setValue(value);
        condition.setDurationSeconds(setDurationSeconds);
        condition.setEnabled(true);
        return condition;
    }

    /**
     * 创建状态变化事件
     */
    public static StateChangeEvent createStateChangeEvent(String deviceCode, String pointId, Object oldValue, Object newValue) {
        return StateChangeEvent.createValueChangeEvent(deviceCode, pointId, oldValue, newValue);
    }

    /**
     * 创建全局日历
     */
    public static GlobalCalendar createGlobalCalendar() {
        GlobalCalendar calendar = new GlobalCalendar();

        // 设置排除月日
        calendar.setExcludeMonthDays(Arrays.asList(
            "01-01",  // 元旦
            "05-01",  // 劳动节
            "10-01"   // 国庆节
        ));

        // 设置夏季时间段
        MonthDayRange summerRange = new MonthDayRange("06-01", "08-31", "夏季");
        calendar.addDateRange("SUMMER", summerRange);

        // 设置冬季时间段（跨年）
        MonthDayRange winterRange1 = new MonthDayRange("12-01", "12-31", "冬季前半段");
        MonthDayRange winterRange2 = new MonthDayRange("01-01", "02-28", "冬季后半段");
        calendar.addDateRange("WINTER", winterRange1);
        calendar.addDateRange("WINTER", winterRange2);

        // 设置春季时间段
        MonthDayRange springRange = new MonthDayRange("03-01", "05-31", "春季");
        calendar.addDateRange("SPRING", springRange);

        // 设置秋季时间段
        MonthDayRange autumnRange = new MonthDayRange("09-01", "11-30", "秋季");
        calendar.addDateRange("AUTUMN", autumnRange);

        return calendar;
    }

    /**
     * 创建简单的全局日历（只有排除月日）
     */
    public static GlobalCalendar createSimpleGlobalCalendar() {
        GlobalCalendar calendar = new GlobalCalendar();
        calendar.setExcludeMonthDays(Arrays.asList("01-01", "12-25"));
        return calendar;
    }

    /**
     * 创建包含年度重复月日的时间条件
     */
    public static TimeCondition createTimeConditionWithMonthDays() {
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 18 ? * *"));
        timeCondition.setIncludeMonthDays(Arrays.asList("02-14", "05-01", "10-01", "12-25"));
        timeCondition.setExcludeMonthDays(Arrays.asList("04-01"));
        timeCondition.setLogic(Operators.Logic.AND);
        timeCondition.setEnabled(true);
        return timeCondition;
    }

    /**
     * 创建月日时间段
     */
    public static MonthDayRange createMonthDayRange(String startDate, String endDate, String description) {
        return new MonthDayRange(startDate, endDate, description);
    }

    /**
     * 创建跨年月日时间段
     */
    public static MonthDayRange createCrossYearMonthDayRange() {
        return new MonthDayRange("12-01", "02-28", "冬季跨年时间段");
    }

    /**
     * 创建节假日特殊照明控制规则
     */
    public static RuleDefinition createHolidayLightingRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId(4L);
        rule.setRuleName("节假日特殊照明控制");
        rule.setBizId("holiday_lighting");
        rule.setPriority(1);
        rule.setEnabled(true);

        // 设置时间条件
        TimeCondition timeCondition = createTimeConditionWithMonthDays();
        rule.setTimeConditions(Arrays.asList(timeCondition));

        // 设置动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        Map<String, Object> params = new HashMap<>();
        params.put("command", "set_holiday_mode");
        params.put("brightness", 100);
        params.put("color", "festive");
        action.setParams(params);
        action.setEnabled(true);

        rule.setActions(Arrays.asList(action));

        return rule;
    }
}
