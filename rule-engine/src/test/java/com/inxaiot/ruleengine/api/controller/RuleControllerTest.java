package com.inxaiot.ruleengine.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inxaiot.ruleengine.RuleEngineTestConfig;
import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.api.manager.RuleManager;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * RuleController单元测试
 * 测试规则管理API的所有接口方法
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-28
 */
@ExtendWith(MockitoExtension.class)
@WebMvcTest(controllers = RuleController.class, excludeAutoConfiguration = {
    org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
    org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class
})
public class RuleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RuleManager ruleManager;

    @Autowired
    private ObjectMapper objectMapper;

    private RuleDefinition testRule;
    private List<RuleDefinition> testRules;

    @BeforeEach
    void setUp() {
        testRule = TestDataFactory.createSimpleEventDrivenRule();
        testRule.setRuleId(1L);
        testRule.setBizId("test_biz_001");
        testRule.setGroupId("test_group_001");

        RuleDefinition testRule2 = TestDataFactory.createTimeDrivenRule();
        testRule2.setRuleId(2L);
        testRule2.setBizId("test_biz_002");
        testRule2.setGroupId("test_group_001");

        testRules = Arrays.asList(testRule, testRule2);
    }

    // ==================== 查询接口测试 ====================

    /**
     * 测试根据规则ID获取规则 - 成功场景
     */
    @Test
    void testGetRule_Success() throws Exception {
        // Given
        when(ruleManager.findRuleById(1L)).thenReturn(testRule);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.ruleId").value(1))
                .andExpect(jsonPath("$.data.ruleName").value(testRule.getRuleName()))
                .andExpect(jsonPath("$.timestamp").exists());

        verify(ruleManager).findRuleById(1L);
    }

    /**
     * 测试根据规则ID获取规则 - 规则不存在
     */
    @Test
    void testGetRule_NotFound() throws Exception {
        // Given
        when(ruleManager.findRuleById(999L)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(ruleManager).findRuleById(999L);
    }

    /**
     * 测试根据规则ID获取规则 - 异常场景
     */
    @Test
    void testGetRule_Exception() throws Exception {
        // Given
        when(ruleManager.findRuleById(1L)).thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        mockMvc.perform(get("/api/engine/rule/1"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("Error getting rule")));

        verify(ruleManager).findRuleById(1L);
    }

    /**
     * 测试根据业务ID获取规则 - 成功场景
     */
    @Test
    void testGetRuleByBizId_Success() throws Exception {
        // Given
        when(ruleManager.findRuleByBizId("test_biz_001")).thenReturn(testRule);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/bizId/test_biz_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.bizId").value("test_biz_001"));

        verify(ruleManager).findRuleByBizId("test_biz_001");
    }

    /**
     * 测试根据分组ID获取规则 - 成功场景
     */
    @Test
    void testGetRulesByGroupId_Success() throws Exception {
        // Given
        when(ruleManager.findRulesByGroupId("test_group_001")).thenReturn(testRules);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/groupId/test_group_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data", hasSize(2)));

        verify(ruleManager).findRulesByGroupId("test_group_001");
    }

    /**
     * 测试获取所有规则 - 无参数
     */
    @Test
    void testGetAllRules_NoParams() throws Exception {
        // Given
        when(ruleManager.findAllRules()).thenReturn(testRules);

        // When & Then
        mockMvc.perform(get("/api/engine/rule"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data", hasSize(2)));

        verify(ruleManager).findAllRules();
    }

    /**
     * 测试获取所有规则 - 按启用状态过滤
     */
    @Test
    void testGetAllRules_FilterByEnabled() throws Exception {
        // Given
        when(ruleManager.findAllEnabledRules()).thenReturn(Collections.singletonList(testRule));

        // When & Then
        mockMvc.perform(get("/api/engine/rule").param("enabled", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data", hasSize(1)));

        verify(ruleManager).findAllEnabledRules();
    }

    /**
     * 测试获取所有规则 - 按设备代码过滤
     */
    @Test
    void testGetAllRules_FilterByDeviceCode() throws Exception {
        // Given
        when(ruleManager.findAllEnabledRulesRelevantToDevice("device_001")).thenReturn(Collections.singletonList(testRule));

        // When & Then
        mockMvc.perform(get("/api/engine/rule").param("deviceCode", "device_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data", hasSize(1)));

        verify(ruleManager).findAllEnabledRulesRelevantToDevice("device_001");
    }

    /**
     * 测试获取规则统计信息
     */
    @Test
    void testGetRuleStatistics_Success() throws Exception {
        // Given
        when(ruleManager.countAllRules()).thenReturn(10);
        when(ruleManager.countEnabledRules()).thenReturn(8);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalRules").value(10))
                .andExpect(jsonPath("$.data.enabledRules").value(8))
                .andExpect(jsonPath("$.data.timestamp").exists());

        verify(ruleManager).countAllRules();
        verify(ruleManager).countEnabledRules();
    }

    // ==================== 规则状态管理测试 ====================

    /**
     * 测试启用规则 - 成功场景
     */
    @Test
    void testEnableRule_Success() throws Exception {
        // Given
        when(ruleManager.enableRule(1L)).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/enable/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule enabled successfully")));

        verify(ruleManager).enableRule(1L);
    }

    /**
     * 测试启用规则 - 规则不存在
     */
    @Test
    void testEnableRule_NotFound() throws Exception {
        // Given
        when(ruleManager.enableRule(999L)).thenReturn(false);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/enable/999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule not found")));

        verify(ruleManager).enableRule(999L);
    }

    /**
     * 测试禁用规则 - 成功场景
     */
    @Test
    void testDisableRule_Success() throws Exception {
        // Given
        when(ruleManager.disableRule(1L)).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/disable/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule disabled successfully")));

        verify(ruleManager).disableRule(1L);
    }

    /**
     * 测试根据业务ID启用规则 - 成功场景
     */
    @Test
    void testEnableRuleByBizId_Success() throws Exception {
        // Given
        when(ruleManager.findRuleByBizId("test_biz_001")).thenReturn(testRule);
        when(ruleManager.enableRule(1L)).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/enable/bizId/test_biz_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule enabled successfully")));

        verify(ruleManager).findRuleByBizId("test_biz_001");
        verify(ruleManager).enableRule(1L);
    }

    /**
     * 测试根据业务ID启用规则 - 规则不存在
     */
    @Test
    void testEnableRuleByBizId_NotFound() throws Exception {
        // Given
        when(ruleManager.findRuleByBizId("nonexistent")).thenReturn(null);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/enable/bizId/nonexistent"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule not found")));

        verify(ruleManager).findRuleByBizId("nonexistent");
        verify(ruleManager, never()).enableRule(anyLong());
    }

    /**
     * 测试根据分组ID启用规则 - 成功场景
     */
    @Test
    void testEnableRulesByGroupId_Success() throws Exception {
        // Given
        when(ruleManager.enableRulesByGroupId("test_group_001")).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/enable/group/test_group_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rules enabled successfully")));

        verify(ruleManager).enableRulesByGroupId("test_group_001");
    }

    /**
     * 测试根据分组ID禁用规则 - 成功场景
     */
    @Test
    void testDisableRulesByGroupId_Success() throws Exception {
        // Given
        when(ruleManager.disableRulesByGroupId("test_group_001")).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/disable/group/test_group_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rules disabled successfully")));

        verify(ruleManager).disableRulesByGroupId("test_group_001");
    }

    // ==================== 删除接口测试 ====================

    /**
     * 测试删除规则 - 成功场景
     */
    @Test
    void testDeleteRule_Success() throws Exception {
        // Given
        when(ruleManager.deleteRuleById(1L)).thenReturn(true);

        // When & Then
        mockMvc.perform(delete("/api/engine/rule/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule deleted successfully")));

        verify(ruleManager).deleteRuleById(1L);
    }

    /**
     * 测试删除规则 - 异常场景
     */
    @Test
    void testDeleteRule_Exception() throws Exception {
        // Given
        when(ruleManager.deleteRuleById(1L)).thenThrow(new RuntimeException("Database error"));

        // When & Then
        mockMvc.perform(delete("/api/engine/rule/1"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("Error deleting rule")));

        verify(ruleManager).deleteRuleById(1L);
    }

    /**
     * 测试根据业务ID删除规则 - 成功场景
     */
    @Test
    void testDeleteRuleByBizId_Success() throws Exception {
        // Given
        when(ruleManager.deleteRuleByBizId("test_biz_001")).thenReturn(true);

        // When & Then
        mockMvc.perform(delete("/api/engine/rule/bizId/test_biz_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule deleted successfully")));

        verify(ruleManager).deleteRuleByBizId("test_biz_001");
    }

    /**
     * 测试根据分组ID删除规则 - 成功场景
     */
    @Test
    void testDeleteRulesByGroupId_Success() throws Exception {
        // Given
        when(ruleManager.deleteRulesByGroupId("test_group_001")).thenReturn(true);

        // When & Then
        mockMvc.perform(delete("/api/engine/rule/group/test_group_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rules deleted successfully")));

        verify(ruleManager).deleteRulesByGroupId("test_group_001");
    }

    // ==================== 创建和更新接口测试 ====================

    /**
     * 测试添加或更新规则 - 成功场景（新增）
     */
    @Test
    void testAddOrUpdateRule_CreateSuccess() throws Exception {
        // Given
        RuleDefinition newRule = TestDataFactory.createSimpleEventDrivenRule();
        newRule.setBizId("new_biz_001");
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class))).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newRule)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule saved successfully")));

        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 成功场景（更新）
     */
    @Test
    void testAddOrUpdateRule_UpdateSuccess() throws Exception {
        // Given
        testRule.setRuleName("Updated Rule Name");
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class))).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRule)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule saved successfully")));

        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 参数验证失败（ID和BizID都为空）
     */
    @Test
    void testAddOrUpdateRule_ValidationFailed() throws Exception {
        // Given
        RuleDefinition invalidRule = TestDataFactory.createSimpleEventDrivenRule();
        invalidRule.setRuleId(null);
        invalidRule.setBizId(null);

        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRule)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("Rule ID and Biz ID cannot all be empty")));

        verify(ruleManager, never()).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 保存失败
     */
    @Test
    void testAddOrUpdateRule_SaveFailed() throws Exception {
        // Given
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class))).thenReturn(false);

        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRule)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule save failed")));

        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 异常场景
     */
    @Test
    void testAddOrUpdateRule_Exception() throws Exception {
        // Given
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRule)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("Error saving rule")));

        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 无效JSON格式
     */
    @Test
    void testAddOrUpdateRule_InvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json}"))
                .andExpect(status().isBadRequest());

        verify(ruleManager, never()).saveOrUpdateRule(any(RuleDefinition.class));
    }

    // ==================== 批量操作测试 ====================

    /**
     * 测试批量添加规则 - 成功场景
     */
    @Test
    void testAddRules_Success() throws Exception {
        // Given
        when(ruleManager.batchSaveRules(anyList())).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRules)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rules saved successfully: 2")));

        verify(ruleManager).batchSaveRules(anyList());
    }

    /**
     * 测试批量添加规则 - 异常场景
     */
    @Test
    void testAddRules_Exception() throws Exception {
        // Given
        when(ruleManager.batchSaveRules(anyList())).thenThrow(new RuntimeException("Batch save failed"));

        // When & Then
        mockMvc.perform(post("/api/engine/rule/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRules)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("Error saving rules")));

        verify(ruleManager).batchSaveRules(anyList());
    }

    /**
     * 测试批量更新规则 - 成功场景
     */
    @Test
    void testUpdateRules_Success() throws Exception {
        // Given
        when(ruleManager.batchUpdateRules(anyList())).thenReturn(true);

        // When & Then
        mockMvc.perform(put("/api/engine/rule/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRules)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rules updated successfully: 2")));

        verify(ruleManager).batchUpdateRules(anyList());
    }

    /**
     * 测试批量更新规则 - 异常场景
     */
    @Test
    void testUpdateRules_Exception() throws Exception {
        // Given
        when(ruleManager.batchUpdateRules(anyList())).thenThrow(new RuntimeException("Batch update failed"));

        // When & Then
        mockMvc.perform(put("/api/engine/rule/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRules)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("Error updating rules")));

        verify(ruleManager).batchUpdateRules(anyList());
    }

    // ==================== 管理接口测试 ====================

    /**
     * 测试刷新规则缓存 - 成功场景
     */
    @Test
    void testRefreshRules_Success() throws Exception {
        // Given
        doNothing().when(ruleManager).refreshRules();

        // When & Then
        mockMvc.perform(post("/api/engine/rule/refresh"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rule cache refreshed successfully")));

        verify(ruleManager).refreshRules();
    }

    /**
     * 测试刷新规则缓存 - 异常场景
     */
    @Test
    void testRefreshRules_Exception() throws Exception {
        // Given
        doThrow(new RuntimeException("Cache refresh failed")).when(ruleManager).refreshRules();

        // When & Then
        mockMvc.perform(post("/api/engine/rule/refresh"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("Error refreshing rule cache")));

        verify(ruleManager).refreshRules();
    }

    // ==================== 边界条件和异常测试 ====================

    /**
     * 测试路径参数为空的情况
     */
    @Test
    void testGetRuleByBizId_EmptyBizId() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/engine/rule/bizId/"))
                .andExpect(status().isNotFound()); // Spring MVC会返回404
    }

    /**
     * 测试路径参数包含特殊字符
     */
    @Test
    void testGetRuleByBizId_SpecialCharacters() throws Exception {
        // Given
        String specialBizId = "test@#$%^&*()_+";
        when(ruleManager.findRuleByBizId(specialBizId)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/bizId/" + specialBizId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(ruleManager).findRuleByBizId(specialBizId);
    }

    /**
     * 测试查询参数为空字符串的情况
     */
    @Test
    void testGetAllRules_EmptyStringParams() throws Exception {
        // Given
        when(ruleManager.findAllRules()).thenReturn(testRules);

        // When & Then
        mockMvc.perform(get("/api/engine/rule")
                .param("deviceCode", "")
                .param("bizId", "")
                .param("groupId", ""))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data", hasSize(2)));

        verify(ruleManager).findAllRules();
    }

    /**
     * 测试并发访问场景
     */
    @Test
    void testConcurrentAccess() throws Exception {
        // Given
        when(ruleManager.findRuleById(1L)).thenReturn(testRule);

        // When & Then - 模拟并发请求
        for (int i = 0; i < 5; i++) {
            mockMvc.perform(get("/api/engine/rule/1"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));
        }

        verify(ruleManager, times(5)).findRuleById(1L);
    }

    // ==================== 性能和压力测试 ====================

    /**
     * 测试大数据量查询
     */
    @Test
    void testGetAllRules_LargeDataSet() throws Exception {
        // Given - 创建大量测试数据
        List<RuleDefinition> largeRuleList = Collections.nCopies(1000, testRule);
        when(ruleManager.findAllRules()).thenReturn(largeRuleList);

        // When & Then
        mockMvc.perform(get("/api/engine/rule"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data", hasSize(1000)));

        verify(ruleManager).findAllRules();
    }

    /**
     * 测试批量操作大数据量
     */
    @Test
    void testBatchSaveRules_LargeDataSet() throws Exception {
        // Given
        List<RuleDefinition> largeRuleList = Collections.nCopies(100, testRule);
        when(ruleManager.batchSaveRules(anyList())).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/engine/rule/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(largeRuleList)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(containsString("Rules saved successfully: 100")));

        verify(ruleManager).batchSaveRules(anyList());
    }

    // ==================== 安全性测试 ====================

    /**
     * 测试SQL注入攻击防护
     */
    @Test
    void testSqlInjectionProtection() throws Exception {
        // Given
        String maliciousBizId = "'; DROP TABLE rules; --";
        when(ruleManager.findRuleByBizId(maliciousBizId)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/bizId/" + maliciousBizId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(ruleManager).findRuleByBizId(maliciousBizId);
    }

    /**
     * 测试XSS攻击防护
     */
    @Test
    void testXssProtection() throws Exception {
        // Given
        String xssPayload = "<script>alert('xss')</script>";
        when(ruleManager.findRuleByBizId(xssPayload)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/bizId/" + xssPayload))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(ruleManager).findRuleByBizId(xssPayload);
    }

    // ==================== 响应格式验证测试 ====================

    /**
     * 测试ApiResponse格式的一致性
     */
    @Test
    void testApiResponseFormat_Consistency() throws Exception {
        // Given
        when(ruleManager.findRuleById(1L)).thenReturn(testRule);

        // When & Then
        mockMvc.perform(get("/api/engine/rule/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.timestamp").exists())
                .andExpect(jsonPath("$.timestamp").isNumber());
    }

    /**
     * 测试错误响应格式的一致性
     */
    @Test
    void testErrorResponseFormat_Consistency() throws Exception {
        // Given
        when(ruleManager.findRuleById(1L)).thenThrow(new RuntimeException("Test error"));

        // When & Then
        mockMvc.perform(get("/api/engine/rule/1"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.data").isEmpty())
                .andExpect(jsonPath("$.timestamp").exists());
    }

    // ==================== HTTP方法测试 ====================

    /**
     * 测试不支持的HTTP方法
     */
    @Test
    void testUnsupportedHttpMethod() throws Exception {
        // When & Then
        mockMvc.perform(patch("/api/engine/rule/1"))
                .andExpect(status().isMethodNotAllowed());
    }

    /**
     * 测试OPTIONS请求（CORS预检）
     */
    @Test
    void testOptionsRequest() throws Exception {
        // When & Then
        mockMvc.perform(options("/api/engine/rule/1")
                .header("Origin", "http://localhost:3000")
                .header("Access-Control-Request-Method", "GET"))
                .andExpect(status().isOk());
    }

    // ==================== 内容类型测试 ====================

    /**
     * 测试不支持的Content-Type
     */
    @Test
    void testUnsupportedContentType() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .contentType(MediaType.TEXT_PLAIN)
                .content("plain text content"))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * 测试缺少Content-Type的POST请求
     */
    @Test
    void testMissingContentType() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/engine/rule")
                .content(objectMapper.writeValueAsString(testRule)))
                .andExpect(status().isUnsupportedMediaType());
    }
}
