package com.inxaiot.ruleengine.api.controller;

import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.api.ApiResponse;
import com.inxaiot.ruleengine.api.manager.RuleManager;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RuleController纯单元测试
 * 不依赖Spring上下文，专注于业务逻辑测试
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-28
 */
@ExtendWith(MockitoExtension.class)
public class RuleControllerUnitTest {

    @Mock
    private RuleManager ruleManager;

    @InjectMocks
    private RuleController ruleController;

    private RuleDefinition testRule;
    private List<RuleDefinition> testRules;

    @BeforeEach
    void setUp() {
        testRule = TestDataFactory.createSimpleEventDrivenRule();
        testRule.setRuleId(1L);
        testRule.setBizId("test_biz_001");
        testRule.setGroupId("test_group_001");

        RuleDefinition testRule2 = TestDataFactory.createTimeDrivenRule();
        testRule2.setRuleId(2L);
        testRule2.setBizId("test_biz_002");
        testRule2.setGroupId("test_group_001");

        testRules = Arrays.asList(testRule, testRule2);
    }

    // ==================== 查询接口测试 ====================

    /**
     * 测试根据规则ID获取规则 - 成功场景
     */
    @Test
    void testGetRule_Success() {
        // Given
        when(ruleManager.findRuleById(1L)).thenReturn(testRule);

        // When
        ResponseEntity<ApiResponse<RuleDefinition>> response = ruleController.getRule(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertEquals(testRule, response.getBody().getData());
        verify(ruleManager).findRuleById(1L);
    }

    /**
     * 测试根据规则ID获取规则 - 规则不存在
     */
    @Test
    void testGetRule_NotFound() {
        // Given
        when(ruleManager.findRuleById(999L)).thenReturn(null);

        // When
        ResponseEntity<ApiResponse<RuleDefinition>> response = ruleController.getRule(999L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertNull(response.getBody().getData());
        verify(ruleManager).findRuleById(999L);
    }

    /**
     * 测试根据规则ID获取规则 - 异常场景
     */
    @Test
    void testGetRule_Exception() {
        // Given
        when(ruleManager.findRuleById(1L)).thenThrow(new RuntimeException("Database connection failed"));

        // When
        ResponseEntity<ApiResponse<RuleDefinition>> response = ruleController.getRule(1L);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Error getting rule"));
        verify(ruleManager).findRuleById(1L);
    }

    /**
     * 测试根据业务ID获取规则 - 成功场景
     */
    @Test
    void testGetRuleByBizId_Success() {
        // Given
        when(ruleManager.findRuleByBizId("test_biz_001")).thenReturn(testRule);

        // When
        ResponseEntity<ApiResponse<RuleDefinition>> response = ruleController.getRuleByBizId("test_biz_001");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertEquals(testRule, response.getBody().getData());
        verify(ruleManager).findRuleByBizId("test_biz_001");
    }

    /**
     * 测试根据分组ID获取规则 - 成功场景
     */
    @Test
    void testGetRulesByGroupId_Success() {
        // Given
        when(ruleManager.findRulesByGroupId("test_group_001")).thenReturn(testRules);

        // When
        ResponseEntity<ApiResponse<List<RuleDefinition>>> response = ruleController.getRulesByGroupId("test_group_001");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertEquals(testRules, response.getBody().getData());
        assertEquals(2, response.getBody().getData().size());
        verify(ruleManager).findRulesByGroupId("test_group_001");
    }

    /**
     * 测试获取所有规则 - 无参数
     */
    @Test
    void testGetAllRules_NoParams() {
        // Given
        when(ruleManager.findAllRules()).thenReturn(testRules);

        // When
        ResponseEntity<ApiResponse<List<RuleDefinition>>> response = 
            ruleController.getAllRules(null,null, null, null, null);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertEquals(testRules, response.getBody().getData());
        verify(ruleManager).findAllRules();
    }

    /**
     * 测试获取所有规则 - 按启用状态过滤
     */
    @Test
    void testGetAllRules_FilterByEnabled() {
        // Given
        when(ruleManager.findAllEnabledRules()).thenReturn(Collections.singletonList(testRule));

        // When
        ResponseEntity<ApiResponse<List<RuleDefinition>>> response = 
            ruleController.getAllRules(true, null, null, null,null);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertEquals(1, response.getBody().getData().size());
        verify(ruleManager).findAllEnabledRules();
    }

    /**
     * 测试获取规则统计信息
     */
    @Test
    void testGetRuleStatistics_Success() {
        // Given
        when(ruleManager.countAllRules()).thenReturn(10);
        when(ruleManager.countEnabledRules()).thenReturn(8);

        // When
        ResponseEntity<ApiResponse<Map<String, Object>>> response =
            ruleController.getRuleStatistics();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertEquals(10, response.getBody().getData().get("totalRules"));
        assertEquals(8, response.getBody().getData().get("enabledRules"));
        assertNotNull(response.getBody().getData().get("timestamp"));
        verify(ruleManager).countAllRules();
        verify(ruleManager).countEnabledRules();
    }

    // ==================== 规则状态管理测试 ====================

    /**
     * 测试启用规则 - 成功场景
     */
    @Test
    void testEnableRule_Success() {
        // Given
        when(ruleManager.enableRule(1L)).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.enableRule(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule enabled successfully"));
        verify(ruleManager).enableRule(1L);
    }

    /**
     * 测试启用规则 - 规则不存在
     */
    @Test
    void testEnableRule_NotFound() {
        // Given
        when(ruleManager.enableRule(999L)).thenReturn(false);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.enableRule(999L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule not found"));
        verify(ruleManager).enableRule(999L);
    }

    /**
     * 测试禁用规则 - 成功场景
     */
    @Test
    void testDisableRule_Success() {
        // Given
        when(ruleManager.disableRule(1L)).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.disableRule(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule disabled successfully"));
        verify(ruleManager).disableRule(1L);
    }

    /**
     * 测试根据业务ID启用规则 - 成功场景
     */
    @Test
    void testEnableRuleByBizId_Success() {
        // Given
        when(ruleManager.findRuleByBizId("test_biz_001")).thenReturn(testRule);
        when(ruleManager.enableRule(1L)).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.enableRuleByBizId("test_biz_001");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule enabled successfully"));
        verify(ruleManager).findRuleByBizId("test_biz_001");
        verify(ruleManager).enableRule(1L);
    }

    /**
     * 测试根据业务ID启用规则 - 规则不存在
     */
    @Test
    void testEnableRuleByBizId_NotFound() {
        // Given
        when(ruleManager.findRuleByBizId("nonexistent")).thenReturn(null);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.enableRuleByBizId("nonexistent");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule not found"));
        verify(ruleManager).findRuleByBizId("nonexistent");
        verify(ruleManager, never()).enableRule(anyLong());
    }

    /**
     * 测试根据分组ID启用规则 - 成功场景
     */
    @Test
    void testEnableRulesByGroupId_Success() {
        // Given
        when(ruleManager.enableRulesByGroupId("test_group_001")).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.enableRulesByGroupId("test_group_001");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rules enabled successfully"));
        verify(ruleManager).enableRulesByGroupId("test_group_001");
    }

    /**
     * 测试根据分组ID禁用规则 - 成功场景
     */
    @Test
    void testDisableRulesByGroupId_Success() {
        // Given
        when(ruleManager.disableRulesByGroupId("test_group_001")).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.disableRulesByGroupId("test_group_001");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rules disabled successfully"));
        verify(ruleManager).disableRulesByGroupId("test_group_001");
    }

    // ==================== 删除接口测试 ====================

    /**
     * 测试删除规则 - 成功场景
     */
    @Test
    void testDeleteRule_Success() {
        // Given
        when(ruleManager.deleteRuleById(1L)).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.deleteRule(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule deleted successfully"));
        verify(ruleManager).deleteRuleById(1L);
    }

    /**
     * 测试删除规则 - 异常场景
     */
    @Test
    void testDeleteRule_Exception() {
        // Given
        when(ruleManager.deleteRuleById(1L)).thenThrow(new RuntimeException("Database error"));

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.deleteRule(1L);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Error deleting rule"));
        verify(ruleManager).deleteRuleById(1L);
    }

    /**
     * 测试根据业务ID删除规则 - 成功场景
     */
    @Test
    void testDeleteRuleByBizId_Success() {
        // Given
        when(ruleManager.deleteRuleByBizId("test_biz_001")).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.deleteRuleByBizId("test_biz_001");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule deleted successfully"));
        verify(ruleManager).deleteRuleByBizId("test_biz_001");
    }

    /**
     * 测试根据分组ID删除规则 - 成功场景
     */
    @Test
    void testDeleteRulesByGroupId_Success() {
        // Given
        when(ruleManager.deleteRulesByGroupId("test_group_001")).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.deleteRulesByGroupId("test_group_001");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rules deleted successfully"));
        verify(ruleManager).deleteRulesByGroupId("test_group_001");
    }

    // ==================== 创建和更新接口测试 ====================

    /**
     * 测试添加或更新规则 - 成功场景（新增）
     */
    @Test
    void testAddOrUpdateRule_CreateSuccess() {
        // Given
        RuleDefinition newRule = TestDataFactory.createSimpleEventDrivenRule();
        newRule.setBizId("new_biz_001");
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class))).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.addOrUpdateRule(newRule);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule saved successfully"));
        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 成功场景（更新）
     */
    @Test
    void testAddOrUpdateRule_UpdateSuccess() {
        // Given
        testRule.setRuleName("Updated Rule Name");
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class))).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.addOrUpdateRule(testRule);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule saved successfully"));
        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 参数验证失败（ID和BizID都为空）
     */
    @Test
    void testAddOrUpdateRule_ValidationFailed() {
        // Given
        RuleDefinition invalidRule = TestDataFactory.createSimpleEventDrivenRule();
        invalidRule.setRuleId(null);
        invalidRule.setBizId(null);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.addOrUpdateRule(invalidRule);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule ID and Biz ID cannot all be empty"));
        verify(ruleManager, never()).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 保存失败
     */
    @Test
    void testAddOrUpdateRule_SaveFailed() {
        // Given
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class))).thenReturn(false);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.addOrUpdateRule(testRule);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule save failed"));
        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    /**
     * 测试添加或更新规则 - 异常场景
     */
    @Test
    void testAddOrUpdateRule_Exception() {
        // Given
        when(ruleManager.saveOrUpdateRule(any(RuleDefinition.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.addOrUpdateRule(testRule);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Error saving rule"));
        verify(ruleManager).saveOrUpdateRule(any(RuleDefinition.class));
    }

    // ==================== 批量操作测试 ====================

    /**
     * 测试批量添加规则 - 成功场景
     */
    @Test
    void testAddRules_Success() {
        // Given
        when(ruleManager.batchSaveRules(anyList())).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.addRules(testRules);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rules saved successfully: 2"));
        verify(ruleManager).batchSaveRules(anyList());
    }

    /**
     * 测试批量添加规则 - 异常场景
     */
    @Test
    void testAddRules_Exception() {
        // Given
        when(ruleManager.batchSaveRules(anyList())).thenThrow(new RuntimeException("Batch save failed"));

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.addRules(testRules);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Error saving rules"));
        verify(ruleManager).batchSaveRules(anyList());
    }

    /**
     * 测试批量更新规则 - 成功场景
     */
    @Test
    void testUpdateRules_Success() {
        // Given
        when(ruleManager.batchUpdateRules(anyList())).thenReturn(true);

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.updateRules(testRules);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rules updated successfully: 2"));
        verify(ruleManager).batchUpdateRules(anyList());
    }

    /**
     * 测试批量更新规则 - 异常场景
     */
    @Test
    void testUpdateRules_Exception() {
        // Given
        when(ruleManager.batchUpdateRules(anyList())).thenThrow(new RuntimeException("Batch update failed"));

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.updateRules(testRules);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Error updating rules"));
        verify(ruleManager).batchUpdateRules(anyList());
    }

    // ==================== 管理接口测试 ====================

    /**
     * 测试刷新规则缓存 - 成功场景
     */
    @Test
    void testRefreshRules_Success() {
        // Given
        doNothing().when(ruleManager).refreshRules();

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.refreshRules();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Rule cache refreshed successfully"));
        verify(ruleManager).refreshRules();
    }

    /**
     * 测试刷新规则缓存 - 异常场景
     */
    @Test
    void testRefreshRules_Exception() {
        // Given
        doThrow(new RuntimeException("Cache refresh failed")).when(ruleManager).refreshRules();

        // When
        ResponseEntity<ApiResponse<String>> response = ruleController.refreshRules();

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Error refreshing rule cache"));
        verify(ruleManager).refreshRules();
    }

    // ==================== 边界条件和异常测试 ====================

    /**
     * 测试空参数处理
     */
    @Test
    void testNullParameterHandling() {
        // Given
        when(ruleManager.findRuleByBizId(null)).thenReturn(null);

        // When
        ResponseEntity<ApiResponse<RuleDefinition>> response = ruleController.getRuleByBizId(null);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertNull(response.getBody().getData());
        verify(ruleManager).findRuleByBizId(null);
    }

    /**
     * 测试空字符串参数处理
     */
    @Test
    void testEmptyStringParameterHandling() {
        // Given
        when(ruleManager.findRuleByBizId("")).thenReturn(null);

        // When
        ResponseEntity<ApiResponse<RuleDefinition>> response = ruleController.getRuleByBizId("");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertNull(response.getBody().getData());
        verify(ruleManager).findRuleByBizId("");
    }

    /**
     * 测试特殊字符参数处理
     */
    @Test
    void testSpecialCharacterParameterHandling() {
        // Given
        String specialBizId = "test@#$%^&*()_+";
        when(ruleManager.findRuleByBizId(specialBizId)).thenReturn(null);

        // When
        ResponseEntity<ApiResponse<RuleDefinition>> response = ruleController.getRuleByBizId(specialBizId);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertNull(response.getBody().getData());
        verify(ruleManager).findRuleByBizId(specialBizId);
    }

    /**
     * 测试空列表返回
     */
    @Test
    void testEmptyListReturn() {
        // Given
        when(ruleManager.findRulesByGroupId("empty_group")).thenReturn(Collections.emptyList());

        // When
        ResponseEntity<ApiResponse<List<RuleDefinition>>> response =
            ruleController.getRulesByGroupId("empty_group");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isSuccess());
        assertNotNull(response.getBody().getData());
        assertTrue(response.getBody().getData().isEmpty());
        verify(ruleManager).findRulesByGroupId("empty_group");
    }
}
