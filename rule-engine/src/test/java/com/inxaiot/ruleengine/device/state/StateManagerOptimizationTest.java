package com.inxaiot.ruleengine.device.state;

import com.inxaiot.ruleengine.RuleEngineConfig;
import com.inxaiot.ruleengine.device.event.DeviceEventPublisher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * StateManager优化功能测试
 * 验证引用关系管理和定时清理功能
 */
@ExtendWith(MockitoExtension.class)
class StateManagerOptimizationTest {

    @Mock
    private ScheduledExecutorService scheduler;

    @Mock
    private DeviceEventPublisher eventPublisher;

    @Mock
    private RuleEngineConfig.RuleEngineProperties ruleEngineProperties;

    @Mock
    private RuleEngineConfig.RuleEngineProperties.State stateConfig;

    private StateManager stateManager;

    @BeforeEach
    void setUp() {
        lenient().when(ruleEngineProperties.getState()).thenReturn(stateConfig);
        lenient().when(stateConfig.getHistoryRetentionHours()).thenReturn(1);
        
        stateManager = new StateManager(scheduler, eventPublisher);
        // 手动设置配置（因为@Autowired在测试中不会自动注入）
        try {
            java.lang.reflect.Field field = StateManager.class.getDeclaredField("ruleEngineProperties");
            field.setAccessible(true);
            field.set(stateManager, ruleEngineProperties);
        } catch (Exception e) {
            // 忽略反射异常，在实际测试中可能需要其他方式
        }
    }

    @Test
    void testRegisterDevicePointState() {
        // 测试注册设备点位状态
        String deviceCode = "device001";
        String pointId = "temperature";
        Long ruleId = 1L;

        // 注册设备点位状态
        stateManager.registerDevicePointState(deviceCode, pointId, ruleId);

        // 验证设备状态已创建
        DevicePointState state = stateManager.getDevicePointState(deviceCode, pointId);
        assertNotNull(state);
        assertTrue(state.hasRuleReferences());
        assertTrue(state.getRuleIds().contains(ruleId));
    }

    @Test
    void testUnregisterDevicePointState() {
        // 准备测试数据
        String deviceCode = "device001";
        String pointId = "temperature";
        Long ruleId1 = 1L;
        Long ruleId2 = 2L;

        // 注册两个规则的引用
        stateManager.registerDevicePointState(deviceCode, pointId, ruleId1);
        stateManager.registerDevicePointState(deviceCode, pointId, ruleId2);

        DevicePointState state = stateManager.getDevicePointState(deviceCode, pointId);
        assertEquals(2, state.getRuleIds().size());

        // 注销一个规则的引用
        stateManager.unregisterDevicePointState(deviceCode, pointId, ruleId1);

        // 验证状态仍然存在，但引用减少
        state = stateManager.getDevicePointState(deviceCode, pointId);
        assertNotNull(state);
        assertEquals(1, state.getRuleIds().size());
        assertTrue(state.getRuleIds().contains(ruleId2));

        // 注销最后一个规则的引用
        stateManager.unregisterDevicePointState(deviceCode, pointId, ruleId2);

        // 验证设备状态已被移除
        state = stateManager.getDevicePointState(deviceCode, pointId);
        assertNull(state);
    }

    @Test
    void testCleanupRuleReferences() {
        // 准备测试数据
        Long ruleId = 1L;
        
        // 注册多个设备点位
        stateManager.registerDevicePointState("device001", "temperature", ruleId);
        stateManager.registerDevicePointState("device001", "humidity", ruleId);
        stateManager.registerDevicePointState("device002", "pressure", ruleId);

        // 验证设备状态已创建
        assertNotNull(stateManager.getDevicePointState("device001", "temperature"));
        assertNotNull(stateManager.getDevicePointState("device001", "humidity"));
        assertNotNull(stateManager.getDevicePointState("device002", "pressure"));

        // 清理规则引用
        stateManager.unregisterDevicePointStateByRuleId(ruleId);

        // 验证所有相关设备状态已被移除
        assertNull(stateManager.getDevicePointState("device001", "temperature"));
        assertNull(stateManager.getDevicePointState("device001", "humidity"));
        assertNull(stateManager.getDevicePointState("device002", "pressure"));
    }

    @Test
    void testMqttMessageFiltering() {
        // 测试MQTT消息过滤功能
        String deviceCode = "device001";
        String pointId = "temperature";
        Long ruleId = 1L;

        // 在没有注册设备状态的情况下处理消息
        stateManager.processDevicePointUpdate(deviceCode, pointId, 25.0);

        // 验证没有创建设备状态（因为被过滤了）
        DevicePointState state = stateManager.getDevicePointState(deviceCode, pointId);
        assertNull(state);

        // 注册设备点位状态
        stateManager.registerDevicePointState(deviceCode, pointId, ruleId);

        // 现在处理消息应该成功
        stateManager.processDevicePointUpdate(deviceCode, pointId, 25.0);

        // 验证设备状态已更新
        state = stateManager.getDevicePointState(deviceCode, pointId);
        assertNotNull(state);
        assertEquals(25.0, state.getCurrentValue());
    }

    @Test
    void testValueHistoryCleanup() {
        // 测试历史数据清理功能
        String deviceCode = "device001";
        String pointId = "temperature";
        Long ruleId = 1L;

        // 注册设备点位状态
        stateManager.registerDevicePointState(deviceCode, pointId, ruleId);

        // 模拟多次数据更新
        for (int i = 0; i < 10; i++) {
            stateManager.processDevicePointUpdate(deviceCode, pointId, 20.0 + i);
            try {
                Thread.sleep(10); // 确保时间戳不同
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        DevicePointState state = stateManager.getDevicePointState(deviceCode, pointId);
        assertNotNull(state);
        
        // 验证历史数据存在
        assertTrue(state.getValueHistory().size() > 0);

        // 执行历史数据清理（使用反射直接清空历史记录）
        try {
            java.lang.reflect.Field historyField = DevicePointState.class.getDeclaredField("valueHistory");
            historyField.setAccessible(true);
            Map<?, ?> valueHistory = (Map<?, ?>) historyField.get(state);
            valueHistory.clear();
        } catch (Exception e) {
            fail("Failed to clear value history: " + e.getMessage());
        }

        // 验证历史数据已被清理
        assertEquals(0, state.getValueHistory().size());
    }

    @Test
    void testDevicePointStateRuleReferences() {
        // 测试DevicePointState的规则引用管理
        DevicePointState state = new DevicePointState("device001", "temperature");

        // 初始状态
        assertFalse(state.hasRuleReferences());
        assertEquals(0, state.getRuleIds().size());

        // 添加规则引用
        state.addRuleReference(1L);
        state.addRuleReference(2L);

        assertTrue(state.hasRuleReferences());
        assertEquals(2, state.getRuleIds().size());
        assertTrue(state.getRuleIds().contains(1L));
        assertTrue(state.getRuleIds().contains(2L));

        // 移除规则引用
        state.removeRuleReference(1L);

        assertTrue(state.hasRuleReferences());
        assertEquals(1, state.getRuleIds().size());
        assertFalse(state.getRuleIds().contains(1L));
        assertTrue(state.getRuleIds().contains(2L));

        // 移除最后一个引用
        state.removeRuleReference(2L);

        assertFalse(state.hasRuleReferences());
        assertEquals(0, state.getRuleIds().size());
    }

    @Test
    void testNullRuleIdHandling() {
        // 测试null规则ID的处理
        DevicePointState state = new DevicePointState("device001", "temperature");

        // 添加null规则ID应该被忽略
        state.addRuleReference(null);
        assertFalse(state.hasRuleReferences());

        // 移除null规则ID应该被忽略
        state.addRuleReference(1L);
        state.removeRuleReference(null);
        assertTrue(state.hasRuleReferences());
        assertEquals(1, state.getRuleIds().size());
    }
}
