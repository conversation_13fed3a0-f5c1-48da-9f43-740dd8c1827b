# 测试环境配置文件
spring:
  profiles:
    active: test
  
  # 数据库配置 - 使用内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false

  # H2控制台（测试时可启用）
  h2:
    console:
      enabled: false

# 日志配置
logging:
  level:
    com.inxaiot.ruleengine: DEBUG
    org.springframework: WARN
    org.hibernate: WARN
    org.jeasy.rules: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 规则引擎配置
rule-engine:
  # 线程池配置
  thread-pool:
    rule-evaluation:
      core-size: 2
      max-size: 4
      queue-capacity: 100
      thread-name-prefix: "test-rule-eval-"
    
    action-execution:
      core-size: 2
      max-size: 4
      queue-capacity: 100
      thread-name-prefix: "test-action-exec-"
    
    device-state:
      pool-size: 1
      thread-name-prefix: "test-device-state-"
    
    time-trigger:
      pool-size: 1
      thread-name-prefix: "test-time-trigger-"
  
  # 业务配置
  business:
    duplicate-prevention-window-minutes: 30
    state-cleanup-interval-hours: 24
    max-state-age-hours: 168  # 7天
    
  # 测试专用配置
  test:
    mock-external-services: true
    fast-execution: true
    disable-scheduling: true
