# 规则引擎处理流程图

## 项目概述

本项目是基于EasyRule的物联网规则引擎，主要流程为：**事实状态 → 规则评估 → 动作执行**，其中规则评估和动作执行采用异步线程处理，提高事实状态处理效率。

## 核心架构图

```mermaid
graph TB
    subgraph "数据源层"
        MQTT[MQTT设备数据]
        Timer[时间触发器]
        API[API接口]
    end
    
    subgraph "事件处理层"
        MqttListener[MQTT监听器]
        TimeTriggerService[时间触发服务]
        DeviceStateManager[设备状态管理器]
    end
    
    subgraph "规则引擎核心"
        RuleEngineTrigger[规则引擎触发服务]
        RuleAdapter[规则适配器]
        TimeEvaluator[时间条件评估器]
        EasyRules[EasyRules引擎]
    end
    
    subgraph "执行层"
        ActionExecutor[动作执行器]
        DeviceControl[设备控制]
        MessageSend[消息发送]
        APICall[API调用]
    end
    
    subgraph "存储层"
        RuleStorage[规则存储]
        GlobalCalendar[全局日历]
        StateStorage[状态存储]
    end
    
    MQTT --> MqttListener
    Timer --> TimeTriggerService
    API --> RuleEngineTrigger
    
    MqttListener --> DeviceStateManager
    MqttListener --> RuleEngineTrigger
    TimeTriggerService --> RuleEngineTrigger
    DeviceStateManager --> RuleEngineTrigger
    
    RuleEngineTrigger --> RuleAdapter
    RuleAdapter --> TimeEvaluator
    RuleAdapter --> EasyRules
    TimeEvaluator --> GlobalCalendar
    
    EasyRules --> ActionExecutor
    ActionExecutor --> DeviceControl
    ActionExecutor --> MessageSend
    ActionExecutor --> APICall
    
    RuleAdapter --> RuleStorage
    TimeEvaluator --> RuleStorage
```

## 1. 事件驱动 - 单点触发判断

```mermaid
sequenceDiagram
    participant Device as 设备
    participant MQTT as MQTT监听器
    participant StateManager as 设备状态管理器
    participant TriggerService as 规则引擎触发服务
    participant Adapter as 规则适配器
    participant TimeEvaluator as 时间条件评估器
    participant EasyRules as EasyRules引擎
    participant ActionExecutor as 动作执行器
    
    Device->>MQTT: 上报传感器数据
    Note over MQTT: 解析Topic和Payload<br/>提取deviceCode, pointId, value
    
    MQTT->>StateManager: processDevicePointUpdate()
    Note over StateManager: 更新设备点位状态<br/>记录状态变化
    
    MQTT->>TriggerService: processDeviceEvent()
    Note over TriggerService: 异步提交到规则评估线程池<br/>立即返回，不阻塞MQTT
    
    activate TriggerService
    TriggerService->>TriggerService: 查询相关规则
    Note over TriggerService: 根据deviceCode查找<br/>EVENT_DRIVEN类型的规则
    
    TriggerService->>Adapter: adapt(RuleDefinition)
    Note over Adapter: 将规则定义转换为<br/>EasyRules的Rule对象
    
    Adapter->>TimeEvaluator: isTimeConditionMet()
    Note over TimeEvaluator: 检查时间守卫条件<br/>Cron表达式、工作日、季节等
    TimeEvaluator-->>Adapter: true/false
    
    Note over Adapter: 评估设备触发条件<br/>操作符比较、AND/OR逻辑
    
    Adapter-->>TriggerService: Easy Rules对象
    TriggerService->>EasyRules: fire(rules, facts)
    
    alt 规则条件满足
        EasyRules->>ActionExecutor: executeAction()
        Note over ActionExecutor: 异步执行动作<br/>设备控制/消息发送/API调用
        ActionExecutor-->>Device: 控制指令
    end
    deactivate TriggerService
```

## 2. 事件驱动 - 持续时长触发

```mermaid
sequenceDiagram
    participant Device as 设备
    participant MQTT as MQTT监听器
    participant StateManager as 设备状态管理器
    participant Scheduler as 定时调度器
    participant TriggerService as 规则引擎触发服务
    participant EasyRules as EasyRules引擎
    participant ActionExecutor as 动作执行器
    
    Device->>MQTT: 上报状态变化<br/>(如：occupancy=UNOCCUPIED)
    MQTT->>StateManager: processDevicePointUpdate()
    
    Note over StateManager: 检测到状态变化<br/>创建状态条件监控
    StateManager->>StateManager: 创建StateConditionMonitor
    Note over StateManager: 配置持续时间条件<br/>(如：无人状态持续15分钟)
    
    StateManager->>Scheduler: schedule(timeout task)
    Note over Scheduler: 启动定时任务<br/>等待指定时长
    
    alt 状态保持不变
        Scheduler->>StateManager: 超时回调
        StateManager->>StateManager: 验证条件仍然满足
        StateManager->>TriggerService: triggerRulesForDeviceTimeout()
        
        Note over TriggerService: 创建超时事实<br/>UnoccupiedTimeoutFact_deviceCode_pointId
        TriggerService->>EasyRules: fire(rules, facts)
        
        Note over EasyRules: 规则适配器检查<br/>UNOCCUPIED_FOR_MINUTES操作符
        EasyRules->>ActionExecutor: executeAction()
        ActionExecutor-->>Device: 执行动作<br/>(如：关闭照明)
        
    else 状态发生变化
        Device->>MQTT: 新状态数据<br/>(如：occupancy=OCCUPIED)
        MQTT->>StateManager: processDevicePointUpdate()
        StateManager->>Scheduler: 取消定时任务
        Note over StateManager: 状态变化，取消超时监控
    end
```

## 3. 时间驱动 - 单时间点触发

```mermaid
sequenceDiagram
    participant Timer as 系统定时器
    participant TimeTrigger as 时间触发服务
    participant TimeEvaluator as 时间条件评估器
    participant TriggerService as 规则引擎触发服务
    participant Adapter as 规则适配器
    participant EasyRules as EasyRules引擎
    participant ActionExecutor as 动作执行器
    participant Device as 目标设备

    Timer->>TimeTrigger: 每分钟执行检查
    Note over TimeTrigger: checkForTimeTriggers()

    TimeTrigger->>TimeTrigger: 查询TIME_DRIVEN规则
    Note over TimeTrigger: 过滤出时间驱动类型的规则

    loop 遍历每个时间驱动规则
        TimeTrigger->>TimeTrigger: inferTriggerMode()
        Note over TimeTrigger: 根据Cron表达式推断<br/>POINT模式 vs RANGE模式

        TimeTrigger->>TimeEvaluator: isTimeConditionMet()
        Note over TimeEvaluator: 评估复杂时间条件<br/>Cron、工作日、季节、个性化日历
        TimeEvaluator-->>TimeTrigger: true/false

        alt 推断为POINT模式 && 时间条件满足
            Note over TimeTrigger: 无状态处理<br/>满足条件即触发
            TimeTrigger->>TriggerService: triggerRuleActivation(ruleId)

            Note over TriggerService: 异步提交到动作执行线程池
            TriggerService->>TriggerService: 创建时间触发事实
            Note over TriggerService: timeTriggerEvent=true<br/>triggeredRuleId=ruleId

            TriggerService->>Adapter: adapt(RuleDefinition)
            Note over Adapter: 时间驱动规则的条件判断<br/>只检查时间触发事实

            Adapter-->>TriggerService: Easy Rules对象
            TriggerService->>EasyRules: fire(rules, facts)
            EasyRules->>ActionExecutor: executeAction()
            ActionExecutor-->>Device: 执行动作
        end
    end
```

## 4. 时间驱动 - 时间段触发

```mermaid
sequenceDiagram
    participant Timer as 系统定时器
    participant TimeTrigger as 时间触发服务
    participant TimeEvaluator as 时间条件评估器
    participant TriggerService as 规则引擎触发服务
    participant EasyRules as EasyRules引擎
    participant ActionExecutor as 动作执行器
    participant Device as 目标设备

    Note over TimeTrigger: 维护规则状态映射<br/>previousTimeConditionState

    Timer->>TimeTrigger: 每分钟执行检查
    TimeTrigger->>TimeTrigger: 查询TIME_DRIVEN规则

    loop 遍历每个时间驱动规则
        TimeTrigger->>TimeTrigger: inferTriggerMode()
        Note over TimeTrigger: 根据Cron表达式推断为RANGE模式<br/>(包含时间范围表达式)

        TimeTrigger->>TimeEvaluator: isTimeConditionMet()
        TimeEvaluator-->>TimeTrigger: isNowActive

        TimeTrigger->>TimeTrigger: 获取上次状态
        Note over TimeTrigger: wasPreviouslyActive = <br/>previousTimeConditionState.get(ruleId)

        alt 进入时间段 (isNowActive=true && wasPreviouslyActive=false)
            Note over TimeTrigger: 时间段开始，触发激活
            TimeTrigger->>TriggerService: triggerRuleActivation(ruleId)
            TriggerService->>EasyRules: fire(rules, facts)
            EasyRules->>ActionExecutor: executeAction()
            Note over ActionExecutor: 执行激活动作<br/>(actions字段)
            ActionExecutor-->>Device: 激活设备
            TimeTrigger->>TimeTrigger: 更新状态为true

        else 离开时间段 (isNowActive=false && wasPreviouslyActive=true)
            Note over TimeTrigger: 时间段结束，触发失活
            TimeTrigger->>TriggerService: triggerRuleDeactivation(ruleId)
            Note over TriggerService: 直接执行失活动作<br/>不经过EasyRules条件判断
            TriggerService->>ActionExecutor: executeAction()
            Note over ActionExecutor: 执行失活动作<br/>(deactivationActions字段)
            ActionExecutor-->>Device: 失活设备
            TimeTrigger->>TimeTrigger: 更新状态为false

        else 状态无变化
            Note over TimeTrigger: 状态保持不变<br/>无需触发
        end
    end
```

## 异步处理架构

```mermaid
graph TB
    subgraph "事件源层 (同步快速返回)"
        MQTT[MQTT消息]
        Timer[定时器]
        API[API调用]
    end

    subgraph "异步边界1: 规则评估线程池"
        RuleEvalPool[规则评估线程池<br/>CPU密集型<br/>核心数线程]
    end

    subgraph "异步边界2: 动作执行线程池"
        ActionExecPool[动作执行线程池<br/>IO密集型<br/>更多线程]
    end

    subgraph "执行结果"
        DeviceControl[设备控制]
        MessageSend[消息发送]
        APICall[API调用]
    end

    MQTT -->|立即返回| RuleEvalPool
    Timer -->|立即返回| RuleEvalPool
    API -->|立即返回| RuleEvalPool

    RuleEvalPool -->|规则条件判断| ActionExecPool
    ActionExecPool --> DeviceControl
    ActionExecPool --> MessageSend
    ActionExecPool --> APICall

    style MQTT fill:#e1f5fe
    style Timer fill:#e1f5fe
    style API fill:#e1f5fe
    style RuleEvalPool fill:#fff3e0
    style ActionExecPool fill:#f3e5f5
```

## 核心组件职责

| 组件 | 职责 | 处理方式 |
|------|------|----------|
| **MqttMessageListener** | 接收设备数据，解析Topic和Payload | 同步解析，异步转发 |
| **DeviceStateManager** | 管理设备状态，处理持续时间条件 | 状态化管理，定时器调度 |
| **TimeTriggerService** | 时间驱动规则的主动调度器 | 每分钟轮询，状态推断 |
| **TimeConditionEvaluator** | 复杂时间条件的被动评估器 | 按需评估，支持多层日历 |
| **RuleEngineTriggerService** | 规则触发的统一入口 | 异步编排，线程池管理 |
| **RuleAdapterService** | 规则定义到EasyRules的适配器 | 条件逻辑适配，类型区分 |
| **ActionExecutorService** | 动作执行的异步处理器 | 多类型动作，异步执行 |

## 数据流向总结

1. **事件驱动流程**：设备数据 → MQTT监听 → 状态管理 → 规则触发 → 条件评估 → 动作执行
2. **时间驱动流程**：定时器 → 时间触发 → 条件评估 → 规则触发 → 动作执行
3. **异步处理**：事件源快速返回 → 规则评估线程池 → 动作执行线程池 → 最终执行
4. **状态管理**：设备状态持续监控 → 超时检测 → 条件满足触发
