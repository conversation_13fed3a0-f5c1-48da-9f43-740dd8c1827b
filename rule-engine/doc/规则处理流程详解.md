# 规则引擎处理流程详解

## 项目概述

本项目是基于Easy Rules 4.1.0的物联网规则引擎，核心处理流程为：**事实状态 → 规则评估 → 动作执行**。系统采用异步处理架构，规则评估和动作执行使用独立线程池，确保事件源快速响应。

## 核心架构图

```mermaid
graph TB
    subgraph "数据源层"
        MQTT[MQTT设备数据]
        Timer[时间触发器]
        API[API接口]
    end
    
    subgraph "事件处理层"
        ML[MqttListener]
        TSS[TimeSchedulerService]
        SM[StateManager]
        DEP[DeviceEventPublisher]
    end
    
    subgraph "规则引擎核心"
        RES[RuleEngineService]
        RAS[RuleAdapterService]
        FB[FactsBuilder]
        DA[DependencyAnalyzer]
        TCE[TimeConditionEvaluator]
    end
    
    subgraph "执行层"
        AE[ActionExecutor]
        DC[设备控制]
        MS[消息发送]
        AC[API调用]
    end
    
    subgraph "存储层"
        RS[RuleService]
        GCS[GlobalCalendarService]
        DB[(SQLite)]
    end
    
    MQTT --> ML
    Timer --> TSS
    API --> RES
    
    ML --> SM
    ML --> RES
    TSS --> RES
    SM --> DEP
    DEP --> RES
    
    RES --> FB
    FB --> DA
    RES --> RAS
    RAS --> TCE
    RAS --> AE
    
    AE --> DC
    AE --> MS
    AE --> AC
    
    RAS --> RS
    TCE --> GCS
    RS --> DB
    GCS --> DB
```

## 1. 事件驱动规则处理流程

### 1.1 单点触发处理

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant ML as MqttListener
    participant SM as StateManager
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService
    participant FB as FactsBuilder
    participant DA as DependencyAnalyzer
    participant RAS as RuleAdapterService
    participant TCE as TimeConditionEvaluator
    participant AE as ActionExecutor

    Device->>ML: 上报传感器数据
    Note over ML: 解析Topic和Payload<br/>提取deviceCode, pointId, value

    ML->>SM: processDevicePointUpdate()
    Note over SM: MQTT消息过滤<br/>更新设备点位状态<br/>记录状态变化

    SM->>SM: checkStateConditions()
    Note over SM: 检查相关的状态条件监控

    SM->>DEP: publishDeviceStateChange()
    Note over DEP: 发布设备状态变化事件<br/>解耦StateManager和RuleEngineService

    DEP->>RES: handleStateChangeEvent() [@EventListener]
    Note over RES: 异步处理事件<br/>使用@Async("ruleEvaluationExecutor")

    activate RES
    RES->>RES: findRulesRelatedToDevice()
    Note over RES: 根据deviceCode查找<br/>EVENT_DRIVEN类型的规则

    loop 每个相关规则
        RES->>FB: buildCompleteFactsForRule()
        FB->>DA: extractRequiredDevicePoints()
        Note over DA: 分析规则依赖的所有设备点位
        FB->>SM: 批量获取设备状态
        FB-->>RES: 完整Facts对象

        RES->>RAS: adapt(RuleDefinition)
        Note over RAS: 将规则定义转换为<br/>Easy Rules的Rule对象

        RAS->>TCE: isTimeConditionMet()
        Note over TCE: 检查时间守卫条件<br/>Cron表达式、工作日、季节等
        TCE-->>RAS: true/false

        Note over RAS: 评估设备触发条件<br/>操作符比较、AND/OR逻辑

        alt 规则条件满足
            RAS->>AE: executeAction()
            Note over AE: 异步执行动作<br/>设备控制/消息发送/API调用
            AE-->>Device: 控制指令
        end
    end
    deactivate RES
```

### 1.2 持续时长触发处理

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant ML as MqttListener
    participant SM as StateManager
    participant SCM as StateConditionMonitor
    participant Scheduler as 定时调度器
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService
    participant AE as ActionExecutor

    Device->>ML: 上报状态变化<br/>(如：temperature=32)
    ML->>SM: processDevicePointUpdate()

    SM->>SM: 检查设备点位是否已注册
    Note over SM: MQTT消息过滤<br/>只处理已注册的设备点位

    alt 设备点位未注册
        SM->>SM: 忽略消息
        Note over SM: 避免创建无用状态对象
    else 设备点位已注册
        SM->>SM: 更新设备状态
        Note over SM: 更新当前值和历史记录
    end

    Note over SM: 检测到状态变化<br/>检查相关StateCondition
    SM->>SCM: checkCondition()
    Note over SCM: 评估条件匹配<br/>(如：温度>30度)

    alt 条件匹配且无活跃监控
        SCM->>Scheduler: 启动持续时间定时器
        Note over SCM: 记录条件开始满足的时间<br/>(如：持续10分钟)
    end

    alt 持续时间内状态变化
        Device->>ML: 新状态数据<br/>(如：temperature=28)
        ML->>SM: processDevicePointUpdate()
        SM->>SCM: checkCondition()
        SCM->>Scheduler: 取消定时器
        Note over SCM: 条件不再满足，清除监控
    else 持续时间到期
        Scheduler->>SCM: 定时器触发
        SCM->>SM: handleConditionTimeout()
        SM->>DEP: publishDeviceTimeout()
        DEP->>RES: handleStateChangeEvent(CONDITION_TIMEOUT)
        Note over RES: 处理持续条件满足事件
        RES->>AE: executeAction()
        AE-->>Device: 执行动作<br/>(如：开启空调)
    end
```

## 2. 时间驱动规则处理流程

### 2.1 时间点触发处理（POINT模式）

```mermaid
sequenceDiagram
    participant Timer as 系统定时器
    participant TSS as TimeSchedulerService
    participant TCE as TimeConditionEvaluator
    participant RES as RuleEngineService
    participant RAS as RuleAdapterService
    participant AE as ActionExecutor
    participant Device as 目标设备

    Timer->>TSS: 每分钟执行检查
    Note over TSS: checkForTimeTriggers()

    TSS->>TSS: checkAndHandleDateChange()
    Note over TSS: 检查跨天处理

    alt 日期变化
        TSS->>TSS: 清空previousTimeConditionState
        Note over TSS: 清理所有时间条件状态<br/>避免跨天影响
    end

    TSS->>TSS: 查询TIME_DRIVEN规则
    Note over TSS: 过滤出时间驱动类型的规则

    loop 遍历每个时间驱动规则
        TSS->>TSS: inferTriggerMode()
        Note over TSS: 根据Cron表达式推断<br/>POINT模式 vs RANGE模式

        TSS->>TCE: isTimeConditionMet()
        Note over TCE: 评估复杂时间条件<br/>包含/排除日期、年度重复月日、季节、起止时间列表<br/>月日时间段列表、节假日、工作日、Cron表达式
        TCE-->>TSS: true/false

        alt 推断为POINT模式 && 时间条件满足
            Note over TSS: 无状态处理<br/>满足条件即触发
            TSS->>RES: triggerRuleActivation(ruleId)

            Note over RES: 创建时间触发Facts
            RES->>RES: 构建时间触发事实
            Note over RES: timeTriggerEvent=true<br/>triggeredRuleId=ruleId

            RES->>RAS: adapt(RuleDefinition)
            Note over RAS: 时间驱动规则的条件判断<br/>只检查时间触发事实

            RAS->>AE: executeAction()
            AE-->>Device: 执行动作
        end
    end
```

### 2.2 时间段触发处理（RANGE模式）

```mermaid
sequenceDiagram
    participant Timer as 系统定时器
    participant TSS as TimeSchedulerService
    participant TCE as TimeConditionEvaluator
    participant RES as RuleEngineService
    participant AE as ActionExecutor
    participant Device as 目标设备

    Note over TSS: 维护规则状态映射<br/>previousTimeConditionState

    Timer->>TSS: 每分钟执行检查

    TSS->>TSS: checkAndHandleDateChange()
    Note over TSS: 检查跨天处理

    alt 日期变化
        TSS->>TSS: 清空previousTimeConditionState
        Note over TSS: 清理所有时间条件状态<br/>避免跨天影响
    end

    TSS->>TSS: 查询TIME_DRIVEN规则

    loop 遍历每个时间驱动规则
        TSS->>TSS: inferTriggerMode()
        Note over TSS: 根据Cron表达式推断为RANGE模式<br/>(包含时间范围表达式)

        TSS->>TCE: isTimeConditionMet()
        TCE-->>TSS: isNowActive

        TSS->>TSS: processRangeMode(ruleId, isNowActive)
        TSS->>TSS: 获取上次状态
        Note over TSS: wasPreviouslyActive = <br/>previousTimeConditionState.get(ruleId)

        alt 首次检查 (wasPreviouslyActive == null)
            TSS->>TSS: 记录当前状态
            Note over TSS: previousTimeConditionState.put(ruleId, isNowActive)
            alt 当前满足条件
                TSS->>RES: triggerRuleActivation(ruleId)
                Note over TSS: 首次检查且当前活跃，触发激活
            end

        else 进入时间段 (isNowActive=true && wasPreviouslyActive=false)
            Note over TSS: 时间段开始，触发激活
            TSS->>RES: triggerRuleActivation(ruleId)
            RES->>AE: executeAction()
            Note over AE: 执行激活动作<br/>(actions字段)
            AE-->>Device: 激活设备
            TSS->>TSS: 更新状态为true

        else 离开时间段 (isNowActive=false && wasPreviouslyActive=true)
            Note over TSS: 时间段结束，触发失活
            TSS->>RES: triggerRuleDeactivation(ruleId)
            Note over RES: 直接执行失活动作<br/>不经过Easy Rules条件判断
            RES->>AE: executeAction()
            Note over AE: 执行失活动作<br/>(deactivationActions字段)
            AE-->>Device: 失活设备
            TSS->>TSS: 更新状态为false

        else 状态无变化
            Note over TSS: 状态保持不变<br/>无需触发
        end
    end
```

### 2.3 时间驱动规则的设备条件支持（新增功能）

时间驱动规则现在支持设备条件评估，实现了时间条件和设备条件的组合逻辑。同时支持 `subOperator` 字段，实现持续时间条件的灵活值比较：

```mermaid
sequenceDiagram
    participant Timer as 系统定时器
    participant TSS as TimeSchedulerService
    participant RES as RuleEngineService
    participant FB as FactsBuilder
    participant SM as StateManager
    participant RAS as RuleAdapterService
    participant AE as ActionExecutor
    participant Device as 目标设备

    Timer->>TSS: 定时触发(每分钟)
    TSS->>TSS: 检查时间驱动规则

    alt 时间条件满足
        TSS->>RES: triggerRuleActivation(ruleId, TIME_EVENT)
        activate RES

        Note over RES: 异步处理
        RES->>FB: buildCompleteFactsForRule(rule, null, null, null)
        activate FB

        FB->>SM: 获取规则依赖的所有设备状态
        SM-->>FB: 返回设备状态数据
        FB-->>RES: 返回完整Facts
        deactivate FB

        Note over RES: 添加触发上下文
        RES->>RES: facts.put(TRIGGERED_RULE_ID, ruleId)
        RES->>RES: facts.put(TRIGGERED_EVENT_TYPE, TIME_EVENT)
        RES->>RES: facts.put(TRIGGERED_TIME, timestamp)

        RES->>RAS: adapt(ruleDefinition)
        activate RAS

        Note over RAS: 时间驱动规则条件评估
        RAS->>RAS: 1. 检查时间触发事件匹配
        RAS->>RAS: 2. 评估设备条件(新增)

        alt 设备条件也满足
            RAS-->>RES: 条件满足，返回true
            RES->>AE: executeAction()
            AE-->>Device: 执行动作
        else 设备条件不满足
            RAS-->>RES: 条件不满足，返回false
            Note over RES: 跳过动作执行
        end

        deactivate RAS
        deactivate RES
    end
```

#### 业务场景示例

1. **智能空调控制**
   - **时间条件**：每天18:00触发
   - **设备条件**：温度传感器 > 28°C
   - **动作**：开启空调

2. **智能照明控制**
   - **时间条件**：每天19:00触发
   - **设备条件**：光照传感器 < 100lux
   - **动作**：开启照明

3. **安防模式切换**
   - **时间条件**：每天22:00触发
   - **设备条件**：所有门窗传感器状态为关闭
   - **动作**：启动安防模式

4. **持续状态监控**（使用subOperator）
   - **时间条件**：工作时间（9:00-18:00）
   - **设备条件**：温度 > 30°C 持续300秒（`operator: "STATES_KEEP_SECONDS", subOperator: "GREATER_THAN"`）
   - **动作**：发送高温告警

5. **范围持续监控**（使用subOperator）
   - **时间条件**：全天候
   - **设备条件**：CO2浓度介于400-800之间持续900秒（`operator: "STATES_KEEP_SECONDS", subOperator: "BETWEEN"`）
   - **动作**：启动通风系统

## 3. 核心组件详细实现

### 3.1 RuleEngineService - 规则引擎服务

#### 核心方法实现
```java
@Service
public class RuleEngineServiceImpl implements RuleEngineService {
    
    /**
     * 处理设备事件 - 异步处理入口
     */
    @Override
    public void processDeviceEvent(String deviceCode, String pointId, Object value) {
        // 异步提交规则评估任务，立即返回，不阻塞事件源
        ruleEvaluationExecutor.execute(() -> {
            processDeviceEventInternal(deviceCode, pointId, value);
        });
    }
    
    /**
     * 监听设备状态变化事件 - 事件驱动架构
     */
    @EventListener
    @Async("ruleEvaluationExecutor")
    public void handleStateChangeEvent(StateChangeEvent event) {
        // 根据事件类型分发处理
        switch (event.getEventType()) {
            case VALUE_CHANGED:
            case STATE_UPDATED:
                // 处理设备状态变化事件
                processDeviceEvent(event.getDeviceCode(), event.getPointId(), event.getNewValue());
                break;
            case CONDITION_TIMEOUT:
                // 处理设备超时事件
                triggerRulesForDeviceTimeout(event.getDeviceCode(), event.getPointId(),
                                           event.getDurationSeconds() != null ? event.getDurationSeconds() : 0L);
                break;
            case CONDITION_MET:
            case CONDITION_RESET:
                // 其他事件类型的处理
                logger.debug("Received condition event: {} for {}.{}",
                           event.getEventType(), event.getDeviceCode(), event.getPointId());
                break;
        }
    }
    
    /**
     * 时间触发规则激活（优化后）
     */
    @Override
    public void triggerRuleActivation(long ruleId, String eventType) {
        ruleEvaluationExecutor.execute(() -> {
            triggerRuleActivationInternal(ruleId, eventType);
        });
    }

    /**
     * 内部处理规则激活的方法
     */
    private void triggerRuleActivationInternal(long ruleId, String eventType) {
        try {
            RuleDefinition definition = ruleService.findRuleById(ruleId);
            if (definition == null || !definition.isEnabled()) {
                return;
            }

            // 构建与规则相关的所有facts（关键优化）
            Facts facts = factsBuilder.buildCompleteFactsForRule(definition, null, null, null);
            facts.put(FactKey.TRIGGERED_RULE_ID, ruleId);
            facts.put(FactKey.TRIGGERED_EVENT_TYPE, eventType);
            facts.put(FactKey.TRIGGERED_TIME, System.currentTimeMillis());
            addGlobalContextToFacts(facts);

            Rule easyRule = ruleAdapterService.adapt(definition);
            Rules rules = new Rules();
            rules.register(easyRule);

            easyRulesEngine.fire(rules, facts);

        } catch (Exception e) {
            logger.error("Error triggering rule activation for ruleId: {}", ruleId, e);
        }
    }
}
```

### 3.2 RuleAdapterService - 规则适配器

#### 核心适配逻辑
```java
@Service
public class RuleAdapterService {

    /**
     * 将RuleDefinition适配为Easy Rules的Rule对象
     */
    public Rule adapt(RuleDefinition ruleDefinition) {
        return new RuleBuilder()
                .name(ruleDefinition.getRuleId())
                .description(ruleDefinition.getRuleName())
                .priority(ruleDefinition.getPriority())
                .when(facts -> {
                    if (!ruleDefinition.isEnabled()) {
                        return false;
                    }

                    try {
                        // 1. 评估时间条件
                        boolean timeMet;
                        boolean isTimeTrigger = FactKey.TRIGGERED_EVENT_TYPE_TIME.equals(facts.get(FactKey.TRIGGERED_EVENT_TYPE));
                        if (isTimeTrigger) {
                            // 时间触发：检查是否为当前规则的时间触发事件
                            timeMet = ruleDefinition.getRuleId().equals(facts.get(FactKey.TRIGGERED_RULE_ID));
                            if (!timeMet) {
                                logger.trace("Time-driven rule {} not triggered by time event.", ruleDefinition.getRuleId());
                                return false;
                            }
                            logger.debug("Time-driven rule {} triggered by time event.", ruleDefinition.getRuleId());

                        // 1.2 事件驱动的规则
                        } else {
                            // 触发由设备事件发起，需要检查"时间守卫条件"
                            // 1. 评估时间条件（作为守卫条件）
                            boolean timeMet = timeConditionEvaluator.isTimeConditionMet(ruleDefinition.getTimeConditions(), LocalDateTime.now());
                            if (!timeMet) {
                                logger.trace("Event-driven rule {} time condition not met.", ruleDefinition.getRuleId());
                                return false;
                            }
                            logger.trace("Event-driven rule {} time condition met.", ruleDefinition.getRuleId());
                        }

                        // 2. 评估设备条件（时间驱动和事件驱动规则都需要）
                        boolean deviceConditionsMet = evaluateDeviceTriggerConditions(ruleDefinition, facts);
                        if (!deviceConditionsMet) {
                            logger.trace("Rule {} device conditions not met.", ruleDefinition.getRuleId());
                            return false;
                        }
                        logger.debug("Rule {} all conditions met.", ruleDefinition.getRuleId());
                        return true;

                    } catch (Exception e) {
                        logger.error("Error evaluating conditions for rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
                        return false;
                    }
                })
                .then(facts -> {
                    // 执行动作
                    if (ruleDefinition.getActions() != null) {
                        ruleDefinition.getActions().forEach(actionDef -> {
                            actionExecutor.executeAction(actionDef, facts);
                        });
                    }
                })
                .build();
    }

    /**
     * 评估设备触发条件
     */
    private boolean evaluateDeviceTriggerConditions(RuleDefinition rule, Facts facts) {
        TriggerCondition triggerCondition = rule.getTriggerCondition();
        if (triggerCondition == null || triggerCondition.getConditions().isEmpty()) {
            return true;
        }

        List<DeviceCondition> conditions = triggerCondition.getConditions();
        boolean result;

        if (triggerCondition.getLogic() == TriggerCondition.MatchLogic.ALL) {
            // AND逻辑：所有条件都必须满足
            result = conditions.stream().allMatch(condition ->
                evaluateSingleDeviceCondition(condition, facts));
        } else {
            // ANY逻辑：任一条件满足即可
            result = conditions.stream().anyMatch(condition ->
                evaluateSingleDeviceCondition(condition, facts));
        }

        return result;
    }

    /**
     * 评估单个设备条件
     * 支持subOperator字段，用于STATES_KEEP_SECONDS操作符的灵活值比较
     */
    private boolean evaluateSingleDeviceCondition(DeviceCondition condition, Facts facts) {
        // 获取设备状态
        String factKey = FactKey.getStateKey(condition.getSourceDeviceCode(), condition.getPointId());
        Object factValue = facts.get(factKey);

        if (factValue == null) {
            return false;
        }

        // 持续时间条件特殊处理
        if (Operators.Duration.STATES_KEEP_SECONDS.equals(condition.getOperator())) {
            // 1. 检查当前值是否满足条件（使用subOperator进行值比较）
            boolean valueMatches = ValueComparator.compare(
                factValue,
                condition.getSubOperator(), // 使用subOperator进行实际的值比较
                condition.getValue(),
                condition.getUpperValue(),
                condition.getDataType()
            );

            if (!valueMatches) {
                return false;
            }

            // 2. 检查持续时间是否满足
            String durationKey = FactKey.getStateKeepTimeFactKey(
                condition.getSourceDeviceCode(), condition.getPointId());
            Long currentDuration = (Long) facts.get(durationKey);

            if (currentDuration == null) {
                return false;
            }

            return currentDuration >= condition.getDurationSeconds();
        }

        // 常规条件直接比较
        return ValueComparator.compare(
            factValue,
            condition.getOperator(),
            condition.getValue(),
            condition.getUpperValue(),
            condition.getDataType()
        );
    }
}
```

### 3.3 FactsBuilder - Facts构建器

#### 完整Facts构建逻辑
```java
@Component
public class FactsBuilder {

    /**
     * 为规则评估构建完整的Facts对象
     */
    public Facts buildCompleteFactsForRule(RuleDefinition rule, String triggerDeviceCode,
                                         String triggerPointId, Object triggerValue) {
        Facts facts = new Facts();

        try {
            // 1. 添加触发信息
            addTriggerInfo(facts, triggerDeviceCode, triggerPointId, triggerValue);

            // 2. 分析规则依赖
            Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);

            // 3. 聚合所有相关设备状态（基于内存缓存）
            int addedStates = 0;
            int expiredStates = 0;
            int missingStates = 0;

            for (DevicePointRef ref : dependencies) {
                StateAddResult result = addDeviceStateToFacts(facts, ref);
                switch (result) {
                    case ADDED:
                        addedStates++;
                        break;
                    case EXPIRED:
                        expiredStates++;
                        break;
                    case MISSING:
                        missingStates++;
                        break;
                }
            }

            // 4. 添加全局上下文信息
            addGlobalContextToFacts(facts);

            logger.debug("Facts built for rule {}: {} added, {} expired, {} missing",
                        rule.getRuleId(), addedStates, expiredStates, missingStates);

        } catch (Exception e) {
            logger.error("Error building facts for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }

        return facts;
    }

    /**
     * 添加设备状态到Facts
     */
    private StateAddResult addDeviceStateToFacts(Facts facts, DevicePointRef ref) {
        DevicePointState state = stateManager.getDevicePointState(ref.getDeviceCode(), ref.getPointId());

        if (state == null) {
            // 设备状态不存在
            String missingKey = ref.getStateKey() + "_missing";
            facts.put(missingKey, true);
            return StateAddResult.MISSING;
        }

        if (isStateExpired(state, ref.getMaxAge())) {
            // 设备状态已过期
            String expiredKey = ref.getStateKey() + "_expired";
            facts.put(expiredKey, true);
            return StateAddResult.EXPIRED;
        }

        // 添加有效的设备状态
        facts.put(ref.getStateKey(), state.getCurrentValue());
        facts.put(ref.getStateKey() + "_dataType", state.getDataType());
        facts.put(ref.getStateKey() + "_updateTime", state.getLastUpdateTime());

        return StateAddResult.ADDED;
    }
}
```

### 3.4 StateManager - 设备状态管理器

#### 通用状态管理实现
```java
@Service
public class StateManager {

    // K: deviceCode_pointId, V: 点位状态对象
    private final Map<String, DevicePointState> devicePointStates = new ConcurrentHashMap<>();

    // K: conditionId, V: 状态条件监控
    private final Map<String, StateConditionMonitor> stateConditionMonitors = new ConcurrentHashMap<>();

    /**
     * 处理设备点位数据更新（优化版本）
     */
    public void processDevicePointUpdate(String deviceCode, String pointId, Object value, String dataType) {
        String stateKey = generateStateKey(deviceCode, pointId);

        // 优化：只处理已注册的设备点位
        DevicePointState pointState = devicePointStates.get(stateKey);
        if (pointState == null) {
            logger.debug("Ignoring update for unregistered device point: {}.{}", deviceCode, pointId);
            return;
        }
        DevicePointState pointState = devicePointStates.computeIfAbsent(stateKey,
            k -> new DevicePointState(deviceCode, pointId));

        // 记录旧值
        Object oldValue = pointState.getCurrentValue();

        // 更新点位状态
        pointState.updateValue(value, dataType);

        // 创建状态变化事件
        StateChangeEvent changeEvent = StateChangeEvent.createValueChangeEvent(
            deviceCode, pointId, oldValue, value, dataType);

        // 检查所有相关的状态条件
        checkStateConditions(deviceCode, pointId, pointState, changeEvent);

        // 发布事件而不是直接调用规则引擎
        eventPublisher.publishDeviceStateChange(deviceCode, pointId, value);
    }

    /**
     * 引用关系管理方法
     */
    public void registerDevicePointState(String deviceCode, String pointId, Long ruleId) {
        String stateKey = generateStateKey(deviceCode, pointId);
        DevicePointState pointState = devicePointStates.computeIfAbsent(stateKey,
            k -> new DevicePointState(deviceCode, pointId));
        pointState.addRuleReference(ruleId);
    }

    public void unregisterDevicePointState(String deviceCode, String pointId, Long ruleId) {
        String stateKey = generateStateKey(deviceCode, pointId);
        DevicePointState pointState = devicePointStates.get(stateKey);

        if (pointState != null) {
            pointState.removeRuleReference(ruleId);

            // 如果没有规则引用了，则移除设备状态
            if (!pointState.hasRuleReferences()) {
                devicePointStates.remove(stateKey);
            }
        }
    }

    /**
     * 定时清理历史数据
     */
    @Scheduled(fixedRateString = "#{${rule.engine.state.cleanup-interval-minutes:10} * 60 * 1000}")
    public void scheduledCleanupValueHistory() {
        int retentionHours = ruleEngineProperties.getState().getHistoryRetentionHours();
        devicePointStates.values().forEach(state -> state.cleanupOldHistory(retentionHours));
    }
            stateConditionMonitors.values().forEach(StateConditionMonitor::cancel);
            stateConditionMonitors.clear();

            // 更新检查日期
            lastCheckDate = today;

            logger.info("Date change handled: reset {} device states, cancelled {} condition monitors", deviceStateCount, monitorCount);
        }
    }

    /**
     * 检查状态条件监控
     */
    private void checkStateConditions(String deviceCode, String pointId, DevicePointState pointState) {
        // 遍历所有相关的状态条件监控
        stateConditionMonitors.values().stream()
            .filter(monitor -> monitor.isRelatedTo(deviceCode, pointId))
            .forEach(monitor -> monitor.checkCondition(pointState));
    }

    /**
     * 注册状态条件监控
     */
    public void registerStateCondition(StateCondition condition) {
        String conditionId = condition.getConditionId();

        StateConditionMonitor monitor = new StateConditionMonitor(condition, scheduler, eventPublisher);
        stateConditionMonitors.put(conditionId, monitor);

        logger.info("Registered state condition monitor: {} for device {}.{}",
                   conditionId, condition.getDeviceCode(), condition.getPointId());
    }
}
```

### 3.5 TimeSchedulerService - 时间调度服务

#### 时间触发核心逻辑
```java
@Service
public class TimeSchedulerService {

    /**
     * 存储RANGE模式规则的前一次时间条件状态
     */
    private final Map<String, Boolean> previousTimeConditionState = new ConcurrentHashMap<>();

    /**
     * 初始化时间触发器，启动定时调度
     */
    @PostConstruct
    private void initialize() {
        // 每分钟执行一次时间触发检查
        scheduler.scheduleAtFixedRate(this::checkForTimeTriggers, 1, 1, TimeUnit.MINUTES);
    }

    /**
     * 检查时间触发器
     */
    private void checkForTimeTriggers() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDate today = now.toLocalDate();

            logger.debug("Starting time trigger check at {}", now);

            // 检查是否跨天，如果跨天则清理状态
            checkAndHandleDateChange(today);

            // 获取所有包含时间条件的规则
            List<RuleDefinition> timeDrivenRules = ruleService.findAllEnabledRules().stream()
                    .filter(RuleDefinition::hasTimeConditions)
                    .collect(Collectors.toList());

            if (timeDrivenRules.isEmpty()) {
                logger.debug("No time-driven rules found, skipping check.");
                return;
            }

            logger.debug("Found {} time-driven rules to check", timeDrivenRules.size());

            for (RuleDefinition rule : timeDrivenRules) {
                try {
                    processTimeDrivenRule(rule, now);
                } catch (Exception e) {
                    logger.error("Error processing time-driven rule {}: {}", rule.getRuleId(), e.getMessage(), e);
                }
            }

            logger.debug("Completed time trigger check");

        } catch (Exception e) {
            logger.error("Error during time trigger check: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查并处理日期变化（跨天处理）
     */
    private void checkAndHandleDateChange(LocalDate today) {
        if (!today.equals(lastCheckDate)) {
            logger.info("Date changed from {} to {}, clearing time condition states", lastCheckDate, today);

            // 清理所有时间条件状态，避免跨天影响
            int clearedCount = previousTimeConditionState.size();
            previousTimeConditionState.clear();

            // 更新检查日期
            lastCheckDate = today;

            logger.info("Cleared {} time condition states due to date change", clearedCount);
        }
    }

    /**
     * 处理单个时间驱动规则
     */
    private void processTimeDrivenRule(RuleDefinition rule, LocalDateTime now) {
        String ruleId = rule.getRuleId();
        List<TimeCondition> timeConditions = rule.getTimeConditions();

        if (timeConditions == null || timeConditions.isEmpty()) {
            return;
        }

        // 推断触发模式
        InferredTriggerMode mode = inferTriggerMode(timeConditions);

        // 评估当前时间条件是否满足
        boolean isNowActive = timeConditionEvaluator.isTimeConditionMet(timeConditions, now);

        if (mode == InferredTriggerMode.POINT) {
            // 时间点模式：无状态，满足条件即触发
            if (isNowActive) {
                ruleEngineService.triggerRuleActivation(ruleId);
            }
        } else {
            // 时间段模式：有状态，检查状态变化
            processRangeMode(ruleId, isNowActive);
        }
    }

    /**
     * 处理RANGE模式的规则
     */
    private void processRangeMode(String ruleId, boolean isNowActive) {
        Boolean wasPreviouslyActive = previousTimeConditionState.get(ruleId);

        if (wasPreviouslyActive == null) {
            // 首次检查，记录当前状态
            previousTimeConditionState.put(ruleId, isNowActive);
            if (isNowActive) {
                logger.info("First check for RANGE mode rule {}: currently active, triggering activation", ruleId);
                ruleEngineService.triggerRuleActivation(ruleId);
            }
        } else {
            // 检查状态变化
            if (isNowActive && !wasPreviouslyActive) {
                // 从不活跃变为活跃：触发激活
                logger.info("RANGE mode rule {} became active, triggering activation", ruleId);
                ruleEngineService.triggerRuleActivation(ruleId);
            } else if (!isNowActive && wasPreviouslyActive) {
                // 从活跃变为不活跃：触发失活
                logger.info("RANGE mode rule {} became inactive, triggering deactivation", ruleId);
                ruleEngineService.triggerRuleDeactivation(ruleId);
            }

            // 更新状态
            previousTimeConditionState.put(ruleId, isNowActive);
        }
    }
}
```

## 4. 异步处理架构

### 4.1 异步处理层次图

```mermaid
graph TB
    subgraph "事件源层 (同步快速返回)"
        MQTT[MQTT消息]
        Timer[定时器]
        API[API调用]
    end

    subgraph "异步边界1: 规则评估线程池"
        REP[规则评估线程池<br/>CPU密集型<br/>核心数线程]
    end

    subgraph "异步边界2: 动作执行线程池"
        AEP[动作执行线程池<br/>IO密集型<br/>更多线程]
    end

    subgraph "执行结果"
        DC[设备控制]
        MS[消息发送]
        AC[API调用]
    end

    MQTT -->|立即返回| REP
    Timer -->|立即返回| REP
    API -->|立即返回| REP

    REP -->|规则条件判断| AEP
    AEP --> DC
    AEP --> MS
    AEP --> AC

    style MQTT fill:#e1f5fe
    style Timer fill:#e1f5fe
    style API fill:#e1f5fe
    style REP fill:#fff3e0
    style AEP fill:#f3e5f5
```

### 4.2 线程池配置

```java
@Configuration
public class RuleEngineConfig {

    /**
     * 规则评估线程池 - CPU密集型
     */
    @Bean("ruleEvaluationExecutor")
    public Executor ruleEvaluationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("RuleEval-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 动作执行线程池 - IO密集型
     */
    @Bean("actionExecutionExecutor")
    public Executor actionExecutionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 4);
        executor.setQueueCapacity(2000);
        executor.setThreadNamePrefix("ActionExec-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

## 5. 核心组件职责总结

| 组件 | 职责 | 处理方式 | 关键特性 |
|------|------|----------|----------|
| **MqttListener** | 接收设备数据，解析Topic和Payload | 同步解析，异步转发 | 快速响应，不阻塞 |
| **StateManager** | 管理设备状态，处理持续时间条件 | 引用关系管理，MQTT过滤，定时清理 | 精确生命周期管理 |
| **TimeSchedulerService** | 时间驱动规则的主动调度器 | 每分钟轮询，状态推断 | POINT/RANGE模式 |
| **TimeConditionEvaluator** | 复杂时间条件的被动评估器 | 按需评估，支持多层日历 | 优先级评估 |
| **RuleEngineService** | 规则触发的统一入口 | 异步编排，线程池管理 | 事件驱动架构 |
| **RuleAdapterService** | 规则定义到Easy Rules的适配器 | 条件逻辑适配，类型区分 | 双重条件判断 |
| **FactsBuilder** | Facts构建器，聚合规则评估数据 | 依赖分析，批量获取 | 性能优化 |
| **ActionExecutor** | 动作执行的异步处理器 | 多类型动作，异步执行 | 去重机制 |

## 6. 数据流向总结

### 6.1 事件驱动流程
```
设备数据 → MQTT监听 → 状态管理 → 事件发布 → 规则触发 → Facts构建 → 条件评估 → 动作执行
```

### 6.2 时间驱动流程
```
定时器 → 时间触发 → 条件评估 → 规则触发 → 动作执行
```

### 6.3 异步处理流程
```
事件源快速返回 → 规则评估线程池 → 动作执行线程池 → 最终执行
```

### 6.4 状态管理流程
```
设备状态持续监控 → 超时检测 → 条件满足触发 → 事件发布 → 规则执行
```

## 7. 关键设计模式

### 7.1 事件驱动架构
- **解耦设计**：StateManager通过DeviceEventPublisher与RuleEngineService解耦
- **异步处理**：使用Spring事件机制实现组件间异步通信
- **事件类型**：VALUE_CHANGE、CONDITION_TIMEOUT等不同事件类型

### 7.2 策略模式
- **触发类型策略**：EVENT_DRIVEN vs TIME_DRIVEN不同处理策略
- **时间模式策略**：POINT vs RANGE不同触发策略
- **动作类型策略**：DEVICE_CONTROL、SEND_MESSAGE、CALL_API等不同执行策略

### 7.3 适配器模式
- **规则适配**：RuleDefinition → Easy Rules Rule对象
- **条件适配**：复杂业务条件 → Easy Rules条件表达式
- **Facts适配**：设备状态 → Easy Rules Facts对象

### 7.4 观察者模式
- **状态监控**：StateConditionMonitor监控设备状态变化
- **事件监听**：@EventListener监听状态变化事件
- **定时观察**：TimeSchedulerService定时观察时间条件变化

## 8. 性能优化要点

### 8.1 内存优化
- **引用关系管理**：基于规则引用的精确生命周期管理
- **MQTT消息过滤**：只处理已注册的设备点位，避免无用状态创建
- **定时清理机制**：自动清理过期的历史数据，可配置保留时间窗口
- **状态连续性**：移除跨天重置，保持长期状态监控的连续性
- **批量操作**：Facts构建时批量获取设备状态

### 8.2 并发优化
- **ConcurrentHashMap**：线程安全的状态缓存
- **异步处理**：规则评估和动作执行完全异步化
- **线程池隔离**：CPU密集型和IO密集型任务分离

### 8.3 执行优化
- **去重机制**：30分钟窗口内的重复操作检测
- **依赖分析**：只获取规则实际需要的设备状态
- **条件短路**：时间条件不满足时快速返回

这套规则处理流程确保了系统的高性能、高可靠性和良好的扩展性，为物联网场景下的复杂规则处理提供了完整的解决方案。
```
```
