# 物联网规则引擎技术方案设计和实现

## 开发背景

### 项目起源

本项目是为物联网楼宇智能化场景开发的边缘侧规则引擎，主要服务于医院楼宇、办公楼宇等应用场景的智能化控制。系统采用三层架构：屏端(N个屏) → 业务服务端 → 执行引擎(N个引擎)，本项目实现的是边缘侧执行引擎部分。

### 解决的业务问题

- **复杂场景联动**：支持基于时间、设备状态、环境条件的多维度规则控制
- **实时响应需求**：设备控制响应时间要求在3-10秒内
- **边缘计算**：支持离线运行，减少对中心服务的依赖
- **规模化部署**：单个区域引擎管理1000条以内规则，支持5000-10000个设备点位

## 技术选型

### 核心框架选择

- **Easy Rules 4.1.0**：轻量级规则引擎，支持Java注解和流式API
- **Spring Boot 2.7.18**：提供依赖注入、异步处理、事件驱动等基础能力
- **SQLite 3.44.1.0**：嵌入式数据库，支持离线存储和快速查询
- **MyBatis 2.3.2**：数据访问层，提供灵活的SQL映射

### 选型依据

1. **轻量化要求**：边缘服务器资源有限(8-16G内存，4-8核CPU)
2. **离线能力**：支持断网情况下的规则执行
3. **开发效率**：团队对Java技术栈熟悉，Easy Rules学习成本低
4. **扩展性**：支持自定义操作符和动作类型

## 整体架构

### 系统架构图

```mermaid
graph TB
    subgraph "业务服务端"
        BS[业务服务端]
    end

    subgraph "边缘规则引擎"
        subgraph "API接口层"
            RC[RuleController]
            MC[MonitorController]
            CC[ConfigController]
        end

        subgraph "业务逻辑层"
            RM[RuleManager]
        end

        subgraph "规则引擎核心"
            RES[RuleEngineService]
            RAS[RuleAdapterService]
            AEC[ActionExecutionCoordinator]
            AE[ActionExecutor]
            AEM[ActionExecutionMonitor]
            FB[FactsBuilder]
            DA[DependencyAnalyzer]
        end

        subgraph "状态管理"
            SM[StateManager]
            DEP[DeviceEventPublisher]
            SC[StateCondition]
            SCM[StateConditionMonitor]
        end

        subgraph "时间处理"
            TCE[TimeConditionEvaluator]
            TSS[TimeSchedulerService]
            GC[GlobalCalendar]
        end

        subgraph "数据传输"
            ML[MqttListener]
        end

        subgraph "存储层"
            RS[RuleService]
            GCS[GlobalCalendarService]
            DB[(SQLite)]
        end
    end

    subgraph "外部系统"
        MQTT[MQTT Broker]
        DEVICES[IoT设备]
    end

    %% 主要数据流
    BS -->|推送规则| RC
    DEVICES -->|设备数据| MQTT
    MQTT -->|订阅消息| ML
    ML -->|状态更新| SM
    SM -->|发布事件| DEP
    DEP -->|状态变化| RES

    %% 规则执行流程
    RES -->|构建Facts| FB
    FB -->|分析依赖| DA
    RES -->|适配规则| RAS
    RAS -->|执行动作| AE
    RAS -->|时间评估| TCE

    %% 时间触发流程
    TSS -->|定时检查| TCE
    TSS -->|触发规则| RES

    %% API层调用业务管理层
    RC -->|CRUD请求| RM
    MC -->|查询数据| RS
    CC -->|配置管理| GCS

    %% 业务管理层调用服务层和状态管理
    RM -->|数据持久化| RS
    RM -->|注册设备状态| SM
    RM -->|注册StateCondition| SM
    SM -->|创建监控器| SCM

    %% 存储层
    RS -->|数据持久化| DB
    GCS -->|日历数据| DB

    %% 状态管理内部流程
    SM -->|检查条件| SCM
    SCM -->|超时触发| DEP
```

### 数据流程架构

#### 事件驱动规则执行流程

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant MQTT as MQTT Broker
    participant ML as MqttListener
    participant SM as StateManager
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService
    participant FB as FactsBuilder
    participant RAS as RuleAdapterService
    participant TCE as TimeConditionEvaluator
    participant AE as ActionExecutor

    Device->>MQTT: 发送设备数据
    MQTT->>ML: 推送消息
    ML->>SM: 更新设备状态
    SM->>DEP: 发布状态变化事件
    DEP->>RES: 异步处理事件
    RES->>FB: 构建完整Facts
    FB->>SM: 批量获取设备状态
    RES->>RAS: 适配规则条件
    RAS->>TCE: 评估时间条件
    TCE-->>RAS: 返回时间条件结果
    RAS-->>RES: 返回规则评估结果
    RES->>AE: 异步执行动作
    AE->>Device: 发送控制指令
```

#### 时间驱动规则执行流程

```mermaid
sequenceDiagram
    participant TSS as TimeSchedulerService
    participant TCE as TimeConditionEvaluator
    participant RES as RuleEngineService
    participant RAS as RuleAdapterService
    participant AE as ActionExecutor
    participant Device as IoT设备

    Note over TSS: 每分钟定时检查
    TSS->>TSS: 查找TIME_DRIVEN规则
    loop 每个时间驱动规则
        TSS->>TCE: 评估时间条件
        TCE-->>TSS: 返回条件满足状态
        alt 条件满足且状态变化
            TSS->>RES: triggerRuleActivation(ruleId)
            RES->>RAS: 适配规则
            RAS->>AE: 执行动作
            AE->>Device: 发送控制指令
        end
        alt RANGE模式规则失活
            TSS->>RES: triggerRuleDeactivation(ruleId)
            RES->>RAS: 适配规则
            RAS->>AE: 执行失活动作
            AE->>Device: 发送控制指令
        end
    end
```

## 核心功能模块详细设计

### 1. 规则生命周期管理

#### 功能描述

采用三层架构模式，实现规则的完整生命周期管理：

- **API层**：处理HTTP请求和响应
- **业务层**：处理业务逻辑和状态管理
- **数据层**：处理数据持久化操作

支持动态更新规则而不重启服务，确保系统的高可用性。

#### 三层架构设计

- **RuleController**：API接口层，负责接收HTTP请求和响应处理
- **RuleManager**：业务逻辑层，负责业务逻辑处理、内存状态管理（设备状态引用关系、状态条件监控）
- **RuleService**：数据访问层，负责数据持久化操作
- **RuleDefinitionEntity**：规则持久化实体

#### 调用关系说明

```
Client → RuleController → RuleManager → RuleService → Database
                            ↓
                       StateManager (状态管理)
```

- **Controller层**：处理HTTP请求，参数验证，响应格式化
- **Manager层**：业务逻辑处理，状态管理，规则生命周期管理
- **Service层**：数据持久化，数据库操作，事务管理

#### 调用时序图

```mermaid
sequenceDiagram
    participant Client as 业务服务端
    participant RC as RuleController
    participant RM as RuleManager
    participant RS as RuleService
    participant SM as StateManager
    participant DB as SQLite数据库
    participant RES as RuleEngineService

    Client->>RC: POST /api/rules (创建规则)
    RC->>RM: createRule(ruleDefinition)
    RM->>RS: saveRule(ruleDefinition)
    RS->>DB: INSERT rule_definition
    DB-->>RS: 返回规则ID
    RS-->>RM: 返回保存结果
    RM->>RM: 分析规则依赖的设备点位
    RM->>SM: registerDevicePointStatesForRule(rule)
    SM->>SM: 注册设备状态引用关系
    RM->>SM: registerStateConditionsForRule(rule)
    SM->>SM: 创建StateConditionMonitor
    RM->>RES: refreshRulesCache()
    RM-->>RC: 返回创建结果
    RC-->>Client: 返回创建结果
```

#### 数据状态流转

```mermaid
stateDiagram-v2
    [*] --> 草稿: 创建规则
    草稿 --> 启用: 启用规则
    启用 --> 禁用: 禁用规则
    禁用 --> 启用: 重新启用
    启用 --> 更新中: 更新规则
    更新中 --> 启用: 更新完成
    启用 --> 删除: 删除规则
    禁用 --> 删除: 删除规则
    删除 --> [*]
```

### 2. 规则执行引擎

#### 功能描述

基于Easy Rules封装的规则执行引擎，支持智能触发机制和灵活的条件逻辑组合，提供异步处理能力。

#### 条件逻辑组合

规则引擎支持时间条件和设备条件的灵活组合：

- **AND逻辑**: 时间条件和设备条件都必须满足才触发规则
- **OR逻辑**: 时间条件或设备条件满足其一即可触发规则

这种设计使得规则引擎能够处理复杂的业务场景，如：
- `(工作时间 AND 温度>25°C)` - 传统AND逻辑
- `(工作时间 OR 人体感应)` - 灵活OR逻辑，支持即时响应

#### 核心组件

- **RuleEngineServiceImpl**：规则引擎服务实现，智能处理时间触发和事件触发规则
- **RuleAdapterService**：规则适配器，将RuleDefinition转换为Easy Rules对象
- **FactsBuilder**：Facts构建器，聚合规则评估所需的设备状态
- **TimeSchedulerService**：时间调度服务，每分钟检查包含时间条件的规则并触发执行

#### 执行流程

```mermaid
flowchart TD
    A[事件触发] --> B{触发类型}
    B -->|设备事件| C[查找相关规则]
    B -->|时间触发| D[TimeSchedulerService定时检查]

    C --> E[构建完整Facts]
    E --> F[规则适配]

    D --> G[评估时间条件]
    G --> H{时间满足?}
    H -->|是| I[触发规则激活/失活]
    H -->|否| J[跳过执行]
    I --> F

    F --> K{规则条件评估}
    K -->|满足| L[异步执行动作]
    K -->|不满足| M[跳过执行]
    L --> N[记录执行日志]
    M --> N
    J --> N
    N --> O[处理完成]
```

### 3. 时间触发器实现

#### 功能描述

支持复杂的时间条件评估，包括Cron表达式、工作日、季节、个性化日历等多维度时间判断。

#### 核心类

- **TimeConditionEvaluator**：时间条件评估器
- **GlobalCalendar**：全局日历管理，采用单记录设计，支持排除月日和命名时间段定义
- **MonthDayRange**：月日时间段类，支持跨年时间段处理
- **TimeSchedulerService**：时间调度服务

#### 时间条件层次结构

```mermaid
graph TD
    A[时间条件评估] --> B[强制包含日期]
    A --> B2[年度重复包含月日]
    A --> C[强制排除日期]
    A --> C2[年度重复排除月日]
    A --> D[季节条件]
    A --> E[起止时间列表]
    A --> F[节假日检查]
    A --> G[工作日条件]
    A --> H[Cron表达式]

    B --> I{优先级1: 最高}
    B2 --> I2{优先级1.5: 年度包含}
    C --> J{优先级2: 次高}
    C2 --> J2{优先级2.5: 年度排除}
    D --> K{优先级5: 季节匹配}
    E --> L{优先级6: 时间段匹配}
    F --> M{优先级7: 节假日排除}
    G --> N{优先级8: 工作日匹配}
    H --> O{优先级9: 时间段匹配}
```

### 4. 设备状态持续触发机制

#### 功能描述

通用设备状态管理器，支持任意设备条件的持续时间判断，包括但不限于：

- 温度传感器：温度 > 30度 持续 600秒（10分钟）
- 占用传感器：占用状态 = "UNOCCUPIED" 持续 900秒（15分钟）
- 照度传感器：照度 < 100 持续 300秒（5分钟）
- 湿度传感器：湿度 > 60% 持续 1200秒（20分钟）
- 压力传感器：压力 < 1.0bar 持续 1800秒（30分钟）

#### 核心实现

- **StateManager**：通用设备状态管理器，采用基于引用关系的生命周期管理，实现MQTT消息过滤和定时清理
- **StateConditionMonitor**：状态条件监控器，负责监控逻辑和定时器管理
- **StateCondition**：状态条件定义，包含设备Code、点位ID、操作符、目标值、子操作符、持续时间，内置时间跟踪字段
- **DevicePointState**：设备点位状态，记录当前值、历史值、更新时间、持续时间，包含规则引用跟踪(ruleIds)

#### 架构优化特性

- **职责分离**：StateConditionMonitor负责监控逻辑，StateCondition负责时间跟踪
- **时间跟踪精确性**：解决设备值频繁变化导致持续时间重置的问题
- **引用关系管理**：每个设备状态跟踪引用它的规则ID列表，实现精确的生命周期管理
- **MQTT消息过滤**：只处理已注册的设备点位，避免为所有设备创建状态对象
- **定时清理机制**：自动清理过期的历史数据，可配置保留时间窗口
- **事件驱动解耦**：通过DeviceEventPublisher发布状态变化和超时事件

#### 通用状态监控机制

```mermaid
sequenceDiagram
    participant Device as 任意设备
    participant SM as StateManager
    participant SC as StateCondition
    participant SCM as StateConditionMonitor
    participant Scheduler as 定时器
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService

    Note over Device: 可以是温度、湿度、照度、占用等任意传感器
    Device->>SM: 状态变化(满足条件值)
    SM->>SCM: checkCondition(pointState)
    SCM->>SC: matches(currentValue, dataType)
    SC-->>SCM: 条件匹配结果

    alt 条件开始满足
        SCM->>SC: updateConditionState(true)
        Note over SC: 设置conditionStartTime = now()
        SCM->>Scheduler: 启动持续时间定时器
    end

    opt 持续时间内状态变化(不满足条件)
        Device->>SM: 状态变化(不满足条件)
        SM->>SCM: checkCondition(pointState)
        SCM->>SC: updateConditionState(false)
        Note over SC: 重置conditionStartTime = null
        SCM->>Scheduler: 取消定时器
    end

    opt 持续时间到期
        Scheduler->>SCM: 定时器触发
        SCM->>SC: isDurationSatisfied()
        SC-->>SCM: 持续时间满足
        SCM->>SM: handleConditionTimeout(condition)
        SM->>DEP: publishDeviceTimeout(...)
        DEP->>RES: 发布CONDITION_TIMEOUT事件
        RES->>RES: 触发相关规则评估
    end
```

### 5. 动作执行机制

#### 5.1 设计目标

动作执行机制是规则引擎的重要组成部分，负责在规则条件满足时执行相应的动作。设计目标包括：

- **优先级控制**：确保关键动作（如设备开关）优先执行
- **状态跟踪**：完整跟踪动作执行状态，支持监控和问题排查
- **可靠性保证**：通过超时重试机制确保动作执行成功
- **性能优化**：异步非阻塞执行，避免影响规则评估性能

#### 5.2 核心组件

```mermaid
graph TB
    subgraph "动作执行核心组件"
        AEC[ActionExecutionCoordinator<br/>执行协调器]
        AE[ActionExecutor<br/>动作执行器]
        AEM[ActionExecutionMonitor<br/>执行监控器]
        AEL[ActionExecutionLogger<br/>执行日志器]
    end

    subgraph "数据模型"
        AER[ActionExecutionRecord<br/>执行记录]
        AECtx[ActionExecutionContext<br/>执行上下文]
    end

    subgraph "外部接口"
        MQTT[MQTT消息]
        Monitor[监控接口]
    end

    AEC --> AE
    AEC --> AEM
    AEC --> AEL
    AEC --> AER
    AEC --> AECtx
    MQTT --> AEC
    Monitor --> AEC
```

#### 5.3 执行流程

1. **优先级排序**：按动作优先级排序，priority=1的动作优先执行
2. **关键动作执行**：同步执行关键动作，等待执行结果
3. **普通动作执行**：异步执行普通动作，提高并发性能
4. **状态跟踪**：维护执行状态队列，跟踪每个动作的执行情况
5. **反馈处理**：处理MQTT反馈消息，更新执行状态
6. **超时重试**：对超时动作进行自动重试，最多重试3次

#### 5.4 关键特性

- **非阻塞等待**：使用CompletableFuture替代Thread.sleep，避免忙等待
- **类型安全**：支持多种数据类型的动作参数，自动类型转换
- **重复操作防护**：30分钟窗口内防止重复执行相同操作
- **独立日志**：专门的动作执行日志文件，便于问题排查

### 6. 异步处理和线程池管理

#### 6.1 设计原则

- **分层异步**：规则评估和动作执行使用独立线程池
- **资源隔离**：CPU密集型和IO密集型任务分离
- **错误隔离**：动作执行失败不影响规则引擎核心
- **优先级控制**：关键动作优先执行，确保执行顺序
- **状态跟踪**：完整的动作执行状态跟踪和监控

#### 6.2 线程池配置

| 线程池名称                   | 用途   | 线程数配置    | 队列大小 |
| ----------------------- | ---- | -------- | ---- |
| ruleEvaluationExecutor  | 规则评估 | CPU核心数   | 1000 |
| actionExecutionExecutor | 动作执行 | CPU核心数*2 | 2000 |
| deviceStateScheduler    | 状态管理 | 2        | 无界队列 |
| timeTriggerScheduler    | 时间触发 | 1        | 无界队列 |

### 7. 监控和日志设计

#### 7.1 监控指标

- **规则执行统计**：成功/失败次数、平均执行时间
- **设备状态统计**：活跃设备数、状态更新频率
- **线程池监控**：队列长度、活跃线程数、拒绝任务数
- **内存使用**：规则缓存大小、设备状态缓存大小

#### 7.2 日志分级

- **ERROR**：系统错误、规则执行失败
- **WARN**：配置问题、性能警告
- **INFO**：规则触发、动作执行
- **DEBUG**：详细执行流程
- **TRACE**：最详细的调试信息

## 性能优化策略

### 1. 规则匹配优化

- **索引优化**：基于设备Code和规则类型建立索引
- **缓存策略**：热点规则内存缓存，减少数据库查询
- **批量处理**：相同设备的多个事件批量处理

### 2. 状态管理优化

- **内存管理**：定期清理过期状态，控制内存使用
- **并发优化**：使用ConcurrentHashMap减少锁竞争
- **批量查询**：多条件规则的设备状态批量获取

### 3. 异步处理优化

- **线程池调优**：根据实际负载调整线程池参数
- **队列监控**：监控队列长度，防止内存溢出
- **背压处理**：高负载时的限流和降级策略

## 已知风险和待解决问题

### 1. 技术风险

- **内存泄漏**：长时间运行可能导致设备状态缓存过大
- **时间同步**：跨天处理和时区变化可能影响时间条件评估
- **并发安全**：高并发场景下的状态一致性问题

### 2. 业务风险

- **规则冲突**：多条规则同时触发可能产生冲突
- **循环依赖**：规则间的循环触发可能导致无限循环
- **数据一致性**：设备状态与实际设备状态的同步延迟

### 3. 解决方案

- **监控告警**：建立完善的监控体系，及时发现问题
- **限流机制**：对规则执行频率进行限制
- **状态校验**：定期与设备进行状态同步校验
- **规则验证**：规则创建时进行冲突检测和循环依赖检查

## 扩展性设计

### 1. 操作符扩展

- **插件化设计**：支持自定义操作符的动态加载
- **类型安全**：强类型操作符定义，减少运行时错误

### 2. 动作类型扩展

- **策略模式**：不同动作类型使用不同的执行策略
- **异步执行**：所有动作类型统一异步执行框架

### 3. 数据源扩展

- **适配器模式**：支持MQTT、HTTP、TCP等多种数据源
- **协议解析**：可插拔的协议解析器

## 具体使用示例

### 1. 典型业务场景实现

#### 场景一：办公室智能照明控制

```json
{
  "ruleId": "office_lighting_001",
  "ruleName": "办公室无人15分钟关灯",

  "logic": "AND",
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 8-18 ? * ?"],
      "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
      "logic": "AND"
    }
  ],
  "triggerCondition": {
    "logic": "ALL",
    "conditions": [
      {
        "sourceDeviceCode": "occupancy_sensor_001",
        "pointId": "occupancy_status",
        "operator": "STATES_KEEP_SECONDS",
        "value": "UNOCCUPIED",
        "subOperator": "EQUALS",
        "durationSeconds": 900
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "turn_off"
      }
    }
  ]
}
```

#### 场景二：空调智能温控

```json
{
  "ruleId": "hvac_temp_control_001",
  "ruleName": "温度过高自动开启空调",

  "logic": "AND",
  "triggerCondition": {
    "logic": "ALL",
    "conditions": [
      {
        "sourceDeviceCode": "temp_sensor_001",
        "pointId": "temperature",
        "operator": "GREATER_THAN",
        "value": 28,
        "durationSeconds": 300
      },
      {
        "sourceDeviceCode": "occupancy_sensor_001",
        "pointId": "occupancy_status",
        "operator": "EQUALS",
        "value": "OCCUPIED"
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "turn_on",
        "temperature": 24,
        "mode": "cooling"
      }
    }
  ]
}
```

### 2. 复杂时间条件示例

#### 夏季工作日特殊时间段（包含年度重复月日）

```json
{
  "timeConditions": [
    {
      "timeCronExpressions": [
        "0 0 8-12 ? *",
        "0 0 14-18 ? *"
      ],
      "season": "summer",
      "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
      "excludeDates": ["2024-07-04", "2024-08-15"],
      "includeDates": ["2024-07-06"],
      "includeMonthDays": ["02-14", "05-01", "10-01"],
      "excludeMonthDays": ["04-01", "12-24"],
      "dateRanges": [
        {
          "startDate": "2024-06-01",
          "endDate": "2024-08-31",
          "description": "夏季时间段"
        }
      ],
      "monthDayRanges": [
        {
          "startDate": "06-01",
          "endDate": "08-31",
          "description": "夏季月日时间段"
        }
      ],
      "logic": "AND"
    }
  ]
}
```

### 3. TimeSchedulerService实现细节

#### 时间触发器核心逻辑

```java
@Service
public class TimeSchedulerService {

    @PostConstruct
    private void initialize() {
        // 每分钟执行一次时间触发检查
        scheduler.scheduleAtFixedRate(this::checkForTimeTriggers, 1, 1, TimeUnit.MINUTES);
    }

    private void checkForTimeTriggers() {
        LocalDateTime now = LocalDateTime.now();

        // 获取所有包含时间条件的规则
        List<RuleDefinition> timeDrivenRules = ruleService.findAllEnabledRules().stream()
                .filter(RuleDefinition::hasTimeConditions)
                .collect(Collectors.toList());

        for (RuleDefinition rule : timeDrivenRules) {
            processTimeDrivenRule(rule, now);
        }
    }

    private void processTimeDrivenRule(RuleDefinition rule, LocalDateTime now) {
        // 推断触发模式：POINT（时间点）或 RANGE（时间段）
        InferredTriggerMode mode = inferTriggerMode(rule.getTimeConditions());

        // 评估当前时间条件是否满足
        boolean isNowActive = timeConditionEvaluator.isTimeConditionMet(rule.getTimeConditions(), now);

        if (mode == InferredTriggerMode.POINT) {
            // 时间点模式：满足条件即触发
            if (isNowActive) {
                ruleEngineService.triggerRuleActivation(rule.getRuleId());
            }
        } else {
            // 时间段模式：检查状态变化，在进入/离开时间段时触发
            processRangeMode(rule.getRuleId(), isNowActive);
        }
    }
}
```

## 代码实现关键点

### 1. Facts构建机制优化

#### 时间触发规则的Facts构建
```java
// RuleEngineServiceImpl.triggerRuleActivationInternal()
private void triggerRuleActivationInternal(long ruleId, String eventType) {
    RuleDefinition definition = ruleService.findRuleById(ruleId);
    if (definition == null || !definition.isEnabled()) {
        return;
    }

    // 构建与规则相关的所有facts（关键优化点）
    Facts facts = factsBuilder.buildCompleteFactsForRule(definition, null, null, null);
    facts.put(FactKey.TRIGGERED_RULE_ID, ruleId);
    facts.put(FactKey.TRIGGERED_EVENT_TYPE, eventType);
    facts.put(FactKey.TRIGGERED_TIME, System.currentTimeMillis());
    addGlobalContextToFacts(facts);

    Rule easyRule = ruleAdapterService.adapt(definition);
    Rules rules = new Rules();
    rules.register(easyRule);

    easyRulesEngine.fire(rules, facts);
}
```

#### FactsBuilder增强功能
```java
@Service
public class FactsBuilder {

    public Facts buildCompleteFactsForRule(RuleDefinition rule, String triggerDeviceCode,
                                         String triggerPointId, Object triggerValue) {
        Facts facts = new Facts();

        // 1. 添加触发信息
        if (triggerDeviceCode != null && triggerPointId != null) {
            addTriggerInfo(facts, triggerDeviceCode, triggerPointId, triggerValue);
        }

        // 2. 分析规则依赖点位信息
        Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);

        // 3. 聚合所有相关设备状态（基于内存缓存）
        for (DevicePointRef ref : dependencies) {
            DevicePointState state = stateManager.getDevicePointState(ref.getDeviceCode(), ref.getPointId());
            if (state != null) {
                facts.put(FactKey.getStateKey(ref.getDeviceCode(), ref.getPointId()), state.getCurrentValue());
            }
        }

        // 4. 添加持续时间条件支持
        if (rule.getTriggerCondition() != null && rule.getTriggerCondition().getConditions() != null) {
            for (DeviceCondition condition : rule.getTriggerCondition().getConditions()) {
                if (Operators.Duration.STATES_KEEP_SECONDS.equals(condition.getOperator())) {
                    facts.put(FactKey.getStateKeepTimeFactKey(condition.getSourceDeviceCode(),
                             condition.getPointId()), condition.getDurationSeconds());
                }
            }
        }

        // 5. 添加全局上下文
        addGlobalContextToFacts(facts);

        return facts;
    }
}
```

### 2. 时间驱动规则设备条件支持

规则引擎现在支持时间条件和设备条件的灵活组合逻辑：

```java
// RuleAdapterService.adapt() - 统一的条件评估逻辑
// 1. 评估时间条件
boolean timeMet;
boolean isTimeTrigger = FactKey.TRIGGERED_EVENT_TYPE_TIME.equals(facts.get(FactKey.TRIGGERED_EVENT_TYPE));
if (isTimeTrigger) {
    // 时间触发：检查是否为当前规则的时间触发事件
    timeMet = ruleDefinition.getRuleId().equals(facts.get(FactKey.TRIGGERED_RULE_ID));
} else {
    // 事件触发：评估时间条件作为守卫条件
    timeMet = timeConditionEvaluator.isTimeConditionMet(ruleDefinition.getTimeConditions(), LocalDateTime.now());
}

Operators.Logic logic = ruleDefinition.getLogic();

// 短路判断：OR逻辑下时间满足即可；AND逻辑下时间不满足即失败
if ((logic == Operators.Logic.OR && timeMet) || (logic == Operators.Logic.AND && !timeMet)) {
    return timeMet;
}

// 2. 评估设备条件
boolean deviceConditionsMet = evaluateDeviceTriggerConditions(ruleDefinition, facts);

// 3. 根据logic组合最终结果
return logic == Operators.Logic.AND ? (timeMet && deviceConditionsMet) : (timeMet || deviceConditionsMet);
```

#### 业务场景示例
- **定时空调控制**：每天18:00触发，但需要检查温度传感器值 > 28°C
- **定时照明控制**：每天19:00触发，但需要检查光照传感器值 < 100lux
- **持续状态监控**：温度 > 30°C 持续300秒触发告警（使用STATES_KEEP_SECONDS + subOperator=GREATER_THAN）
- **范围持续监控**：CO2浓度介于400-800之间持续900秒触发通风（使用STATES_KEEP_SECONDS + subOperator=BETWEEN）
- **定时安防模式**：每天22:00触发，但需要检查所有门窗传感器状态为关闭

### 3. FactKey常量系统

引入统一的Facts键名管理，避免硬编码字符串：

```java
public class FactKey {
    // 触发相关
    public static final String TRIGGERED_RULE_ID = "triggeredRuleId";
    public static final String TRIGGERED_EVENT_TYPE = "triggeredEventType";
    public static final String TRIGGERED_EVENT_TYPE_TIME = "TIME_ACTIVATION_EVENT";
    public static final String TRIGGERED_EVENT_TYPE_DEVICE = "DEVICE_ACTIVATION_EVENT";
    public static final String TRIGGERED_TIME = "timestamp";

    // 设备状态键生成
    public static String getStateKey(String deviceCode, String pointId) {
        return deviceCode + "." + pointId;
    }

    // 状态持续时间键生成
    public static String getStateKeepTimeFactKey(String deviceCode, String pointId) {
        return getStateKey(deviceCode, pointId) + "_keepTime";
    }
}
```

#### 使用示例
```java
// 设备状态访问
Object temperature = facts.get(FactKey.getStateKey("temp_sensor_001", "temperature"));

// 事件类型检查
boolean isTimeEvent = FactKey.TRIGGERED_EVENT_TYPE_TIME.equals(
    facts.get(FactKey.TRIGGERED_EVENT_TYPE));

// 持续时间条件
Object keepTime = facts.get(FactKey.getStateKeepTimeFactKey("motion_sensor_001", "presence"));
```

### 1. 异步处理实现

```java
@Service
public class RuleEngineServiceImpl implements RuleEngineService {

    @Async("ruleEvaluationExecutor")
    @EventListener
    public void handleStateChangeEvent(StateChangeEvent event) {
        // 异步处理状态变化事件
        processDeviceEventInternal(event.getDeviceCode(),
                                 event.getPointId(),
                                 event.getNewValue());
    }

    @Override
    public void processDeviceEvent(String deviceCode, String pointId, Object value) {
        // 立即返回，异步处理
        ruleEvaluationExecutor.execute(() -> {
            processDeviceEventInternal(deviceCode, pointId, value);
        });
    }
}
```

### 2. 状态持续监控实现

```java
@Service
public class StateManager {

    public void processDevicePointUpdate(String deviceCode, String pointId, Object value, String dataType) {
        String stateKey = generateStateKey(deviceCode, pointId);

        // 优化：只处理已注册的设备点位
        DevicePointState pointState = devicePointStates.get(stateKey);
        if (pointState == null) {
            logger.debug("Ignoring update for unregistered device point: {}.{}", deviceCode, pointId);
            return;
        }

        // 更新设备状态
        Object oldValue = pointState.getCurrentValue();
        pointState.updateValue(value, dataType);

        // 检查相关的状态条件监控
        checkStateConditions(deviceCode, pointId, pointState);

        // 发布状态变化事件（解耦设计）
        eventPublisher.publishDeviceStateChange(deviceCode, pointId, oldValue, value, dataType);
    }

    // 引用关系管理
    public void registerDevicePointState(String deviceCode, String pointId, Long ruleId) {
        String stateKey = generateStateKey(deviceCode, pointId);
        DevicePointState pointState = devicePointStates.computeIfAbsent(stateKey,
            k -> new DevicePointState(deviceCode, pointId));
        pointState.addRuleReference(ruleId);
    }

    public void unregisterDevicePointState(String deviceCode, String pointId, Long ruleId) {
        String stateKey = generateStateKey(deviceCode, pointId);
        DevicePointState pointState = devicePointStates.get(stateKey);

        if (pointState != null) {
            pointState.removeRuleReference(ruleId);

            // 如果没有规则引用了，则移除设备状态
            if (!pointState.hasRuleReferences()) {
                devicePointStates.remove(stateKey);
            }
        }
    }

    // 定时清理历史数据
    @Scheduled(fixedRateString = "#{${rule.engine.state.cleanup-interval-minutes:10} * 60 * 1000}")
    public void scheduledCleanupValueHistory() {
        int retentionHours = ruleEngineProperties.getState().getHistoryRetentionHours();
        devicePointStates.values().forEach(state -> state.cleanupOldHistory(retentionHours));
    }
}
```

### 3. 规则适配器核心逻辑

```java
@Service
public class RuleAdapterService {

    public Rule adapt(RuleDefinition ruleDefinition) {
        return new RuleBuilder()
            .name(ruleDefinition.getRuleName())  // 使用规则名称而非ID
            .description(ruleDefinition.getDescription())
            .priority(ruleDefinition.getPriority())
            .when(facts -> {
                if (!ruleDefinition.isEnabled()) {
                    return false;
                }

                try {
                    // 1. 评估时间条件
                    boolean timeMet;
                    boolean isTimeTrigger = FactKey.TRIGGERED_EVENT_TYPE_TIME.equals(facts.get(FactKey.TRIGGERED_EVENT_TYPE));
                    if (isTimeTrigger) {
                        // 时间触发：检查是否为当前规则的时间触发事件
                        timeMet = ruleDefinition.getRuleId().equals(facts.get(FactKey.TRIGGERED_RULE_ID));
                    } else {
                        // 事件触发：评估时间条件作为守卫条件
                        timeMet = timeConditionEvaluator.isTimeConditionMet(ruleDefinition.getTimeConditions(), LocalDateTime.now());
                    }

                    Operators.Logic logic = ruleDefinition.getLogic();

                    // 短路判断：OR逻辑下时间满足即可；AND逻辑下时间不满足即失败
                    if ((logic == Operators.Logic.OR && timeMet) || (logic == Operators.Logic.AND && !timeMet)) {
                        return timeMet;
                    }

                    // 2. 评估设备条件
                    boolean deviceConditionsMet = evaluateDeviceTriggerConditions(ruleDefinition, facts);

                    // 3. 根据logic组合最终结果
                    return logic == Operators.Logic.AND ? (timeMet && deviceConditionsMet) : (timeMet || deviceConditionsMet);

                } catch (Exception e) {
                    logger.error("Error evaluating conditions for rule {}: {}",
                               ruleDefinition.getRuleId(), e.getMessage(), e);
                    return false;
                }
            })
            .then(facts -> {
                // 执行动作逻辑
                executeActions(ruleDefinition, facts);
            })
            .build();
    }
}
```

## 部署和运维

### 1. 部署要求

- **硬件要求**：最低4核8G，推荐8核16G
- **软件依赖**：Java 11+, SQLite 3.x
- **网络要求**：支持MQTT连接，HTTP API访问

### 2. 配置管理

- **环境配置**：支持dev/test/prod多环境配置
- **动态配置**：支持运行时配置更新
- **配置校验**：启动时进行配置有效性检查

### 3. 运维监控

- **健康检查**：提供HTTP健康检查接口
- **指标暴露**：支持Prometheus指标采集
- **日志管理**：支持日志轮转和远程日志收集

## 项目发现的问题和改进建议

### 1. 当前发现的问题

#### 功能实现问题

- **错误处理不统一**：不同模块的异常处理策略不一致
- **监控指标不完善**：缺少详细的业务监控指标
- **配置管理**：部分配置参数硬编码，需要外部化配置

### 2. 改进建议

#### 短期改进（1-2周）

1. **统一异常处理**：建立统一的异常处理机制和错误码体系
2. **完善单元测试**：提高核心业务逻辑的测试覆盖率
3. **增强监控指标**：添加更详细的业务监控指标

#### 中期改进（1-2月）

1. **配置外部化**：将硬编码配置参数移至配置文件
2. **性能优化**：基于实际运行数据进行性能调优
3. **API文档完善**：使用Swagger等工具自动生成API文档

#### 长期改进（3-6月）

1. **微服务拆分**：考虑将规则引擎拆分为多个微服务
2. **分布式支持**：支持多实例部署和负载均衡
3. **可视化管理**：开发规则管理的Web界面

### 3. 技术债务清单

- [ ] 完善单元测试覆盖率（目标80%+）
- [ ] 建立完整的集成测试套件
- [ ] 优化数据库查询性能
- [ ] 完善API文档和使用示例
- [ ] 建立代码质量检查流程
- [ ] 配置参数外部化管理
