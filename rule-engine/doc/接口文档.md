# 规则引擎 API接口文档

## 接口概述

规则引擎提供完整的API接口，包括规则管理、配置管理和监控功能。通过这些接口，开发者可以实现规则的完整生命周期管理、全局配置管理和系统监控。

### 核心功能

- **规则管理**：规则的CRUD操作、状态管理、批量操作
- **配置管理**：全局日历配置、系统配置管理
- **监控功能**：系统状态监控、规则执行统计

### 基础信息

- **Base URL**: `http://localhost:6001/api/engine`
- **Content-Type**: `application/json`
- **响应格式**: 统一的ApiResponse包装格式

## API控制器列表

### 1. RuleController - 规则管理

- **路径**: `/api/engine/rule`
- **功能**: 规则的CRUD操作和状态管理

### 2. ConfigController - 配置管理

- **路径**: `/api/engine/config`
- **功能**: 全局日历配置和系统配置管理

### 3. MonitorController - 监控管理

- **路径**: `/api/engine/monitor`
- **功能**: 系统状态监控和统计信息
  - 规则执行统计
  - 设备状态监控
  - 动作执行队列监控
  - 系统性能指标

## 通用说明

### 响应格式

所有接口都使用统一的ApiResponse格式返回：

```json
{
  "success": true,
  "message": "Success",
  "data": {},
  "timestamp": 1703123456789
}
```

**字段说明**：

- `success`: 布尔值，表示请求是否成功
- `message`: 字符串，返回消息或错误描述
- `data`: 对象，具体的返回数据，失败时为null
- `timestamp`: 长整型，响应时间戳

### 错误处理

- **成功响应**: HTTP 200，success=true
- **业务错误**: HTTP 200，success=false，message包含错误信息
- **系统错误**: HTTP 500，success=false，message包含错误信息

---

# ConfigController - 配置管理接口

## 接口概述

ConfigController提供全局配置管理功能，包括全局日历配置和系统配置的查询与更新。

### 基础信息

- **Base URL**: `http://localhost:6001/api/engine/config`
- **主要功能**: 全局日历管理、系统配置查询

## 接口详情

### 1. 全局日历管理

#### 1.1 获取全局日历

**接口描述**: 获取当前的全局日历配置

```http
GET /api/engine/config/calendar
```

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "excludeMonthDays": ["01-01", "05-01", "10-01", "12-25"],
    "dateRanges": {
      "SUMMER": [
        {
          "startDate": "06-01",
          "endDate": "08-31",
          "description": "夏季"
        }
      ],
      "WINTER": [
        {
          "startDate": "12-01",
          "endDate": "12-31",
          "description": "冬季前半段"
        },
        {
          "startDate": "01-01",
          "endDate": "02-28",
          "description": "冬季后半段"
        }
      ]
    }
  },
  "timestamp": 1703123456789
}
```

#### 1.2 更新全局日历

**接口描述**: 更新全局日历配置

```http
PUT /api/engine/config/calendar
Content-Type: application/json
```

**请求体**:

```json
{
  "excludeMonthDays": ["01-01", "05-01", "10-01", "12-25"],
  "dateRanges": {
    "SUMMER": [
      {
        "startDate": "06-01",
        "endDate": "08-31",
        "description": "夏季"
      }
    ],
    "WINTER": [
      {
        "startDate": "12-01",
        "endDate": "12-31",
        "description": "冬季前半段"
      },
      {
        "startDate": "01-01",
        "endDate": "02-28",
        "description": "冬季后半段"
      }
    ],
    "HOLIDAY": [
      {
        "startDate": "02-10",
        "endDate": "02-17",
        "description": "春节假期"
      }
    ]
  }
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "Global calendar updated successfully",
  "data": null,
  "timestamp": 1703123456789
}
```

#### 1.3 刷新日历缓存

**接口描述**: 刷新全局日历缓存

```http
POST /api/engine/config/calendar/refresh
```

**响应示例**:

```json
{
  "success": true,
  "message": "Calendar cache refreshed successfully",
  "data": null,
  "timestamp": 1703123456789
}
```

### 2. 系统配置管理

#### 2.1 获取系统配置

**接口描述**: 获取系统配置信息

```http
GET /api/engine/config/system
```

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "timezone": "Asia/Shanghai",
    "locale": "zh_CN",
    "dateFormat": "yyyy-MM-dd",
    "timeFormat": "HH:mm:ss",
    "engineVersion": "1.0.0",
    "buildTime": "2025-07-07T10:00:00"
  },
  "timestamp": 1703123456789
}
```

### 全局日历配置说明

#### excludeMonthDays字段

- **类型**: `List<String>`
- **格式**: `"MM-dd"`
- **作用**: 年度重复排除月日列表，这些月日每年都会被排除
- **示例**: `["01-01", "05-01", "10-01", "12-25"]`

#### monthDayRanges字段

- **类型**: `Map<String, List<MonthDayRange>>`
- **作用**: 命名时间段定义，支持跨年时间段
- **时间段名称**: 可自定义，如"SUMMER"、"WINTER"、"HOLIDAY"等
- **MonthDayRange结构**:
  - `startDate`: 开始月日，格式"MM-dd"
  - `endDate`: 结束月日，格式"MM-dd"
  - `description`: 时间段描述（可选）

#### 跨年时间段处理

当时间段跨年时（如冬季从12月到次年2月），需要拆分为多个时间段：

```json
"WINTER": [
  {
    "startDate": "12-01",
    "endDate": "12-31",
    "description": "冬季前半段"
  },
  {
    "startDate": "01-01",
    "endDate": "02-28",
    "description": "冬季后半段"
  }
]
```

### 使用场景示例

#### 场景1：配置节假日排除

```json
{
  "excludeMonthDays": ["01-01", "05-01", "10-01", "12-25"]
}
```

#### 场景2：配置季节时间段

```json
{
  "monthDayRanges": {
    "SUMMER": [{"startDate": "06-01", "endDate": "08-31", "description": "夏季"}],
    "WINTER": [
      {"startDate": "12-01", "endDate": "12-31", "description": "冬季前半段"},
      {"startDate": "01-01", "endDate": "02-28", "description": "冬季后半段"}
    ]
  }
}
```

#### 场景3：配置节假日时间段

```json
{
  "monthDayRanges": {
    "SPRING_FESTIVAL": [
      {"startDate": "02-10", "endDate": "02-17", "description": "春节假期"}
    ],
    "NATIONAL_DAY": [
      {"startDate": "10-01", "endDate": "10-07", "description": "国庆假期"}
    ]
  }
}
```

---

# RuleController - 规则管理接口

## 接口详细说明

### 1. 规则查询接口

#### 1.1 根据规则ID查询规则

**接口描述**: 根据规则的唯一ID查询单个规则详情

```http
GET /api/engine/rule/{ruleId}
```

**路径参数**:

- `ruleId` (long, 必需): 规则唯一ID

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "ruleId": 1001,
    "ruleName": "办公室照明控制",
    "bizId": "biz_001",
    "groupId": "group_lighting",
    "priority": 1,
    "enabled": true,
    "logic": "AND",
    "timeConditions": [...],
    "triggerCondition": {...},
    "actions": [...],
    "createTime": "2024-12-19 10:30:00",
    "updateTime": "2024-12-19 10:30:00"
  },
  "timestamp": 1703123456789
}
```

**调用示例**:

```bash
curl -X GET "http://localhost:6001/api/engine/rule/1001"
```

#### 1.2 根据业务ID查询规则

**接口描述**: 根据业务系统的唯一标识查询规则

```http
GET /api/engine/rule/bizId/{bizId}
```

**路径参数**:

- `bizId` (string, 必需): 业务系统唯一标识

**响应格式**: 与1.1相同

**调用示例**:

```bash
curl -X GET "http://localhost:6001/api/engine/rule/bizId/biz_001"
```

#### 1.3 根据分组ID查询规则列表

**接口描述**: 根据分组ID查询该分组下的所有规则

```http
GET /api/engine/rule/groupId/{groupId}
```

**路径参数**:

- `groupId` (string, 必需): 规则分组ID

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": [
    {
      "ruleId": 1001,
      "ruleName": "办公室照明控制",
      ...
    },
    {
      "ruleId": 1002,
      "ruleName": "会议室空调控制",
      ...
    }
  ],
  "timestamp": 1703123456789
}
```

#### 1.4 查询所有规则（支持过滤）

**接口描述**: 查询所有规则，支持多种过滤条件

```http
GET /api/engine/rule
```

**查询参数**:

- `enabled` (boolean, 可选): 是否只查询启用的规则
- `deviceCode` (string, 可选): 按目标设备Code过滤
- `bizId` (string, 可选): 按业务ID过滤
- `groupId` (string, 可选): 按分组ID过滤
- `groupIds` (string列表, 可选): 按分组ID列表过滤

**调用示例**:

```bash
# 查询所有启用的规则
curl -X GET "http://localhost:6001/api/engine/rule?enabled=true"

# 查询指定设备的规则
curl -X GET "http://localhost:6001/api/engine/rule?deviceCode=light_001"
```

### 2. 规则创建和更新接口

#### 2.1 创建或更新规则

**接口描述**: 创建新规则或更新已存在的规则

```http
POST /api/engine/rule
```

**请求体**: RuleDefinition对象（详见数据模型章节）,必须包含bizId

**请求示例**:

```json
{
  "ruleId": null,
  "bizId": "biz_lighting_001",
  "ruleName": "办公室照明控制",
  "groupId": "group_lighting",
  "priority": 1,
  "enabled": true,
  "logic": "AND",
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 8-18 * * ?"],
      "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
      "logic": "AND",
      "enabled": true
    }
  ],
  "triggerCondition": {
    "logic": "AND",
    "conditions": [
      {
        "sourceDeviceCode": "sensor_001",//设备标识符
        "pointId": "occupancy",          //点位属性
        "operator": "EQUALS",
        "value": "UNOCCUPIED",          //点位属性值
        "durationSeconds": 900,
        "enabled": true
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "priority":0,
      "params": {
           "deviceCode":"light_01"     //设备标识符
           "tagCode":"light_01_button" //点位属性
           "val":"on"                  //点位属性值
      },
      "checkParams":{},                //检查执行是否成功用到的参数，预留
      "enabled": true
    }
  ]
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "Rule saved successfully: 1001",
  "data": null,
  "timestamp": 1703123456789
}
```

#### 2.2 批量创建规则

**接口描述**: 批量创建多个规则

```http
POST /api/engine/rule/batch
```

**请求体**: RuleDefinition对象数组，每个必须包含bizId

**调用示例**:

```bash
curl -X POST "http://localhost:6001/api/engine/rule/batch" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "bizId": "biz_001",
      "ruleName": "规则1",
      ...
    },
    {
      "bizId": "biz_002", 
      "ruleName": "规则2",
      ...
    }
  ]'
```

#### 2.3 批量更新规则

**接口描述**: 批量更新多个已存在的规则

```http
PUT /api/engine/rule/batch
```

**请求体**: RuleDefinition对象数组（必须包含ruleId或bizId）

### 3. 规则状态管理接口

#### 3.1 启用规则

**接口描述**: 启用指定的规则

```http
POST /api/engine/rule/enable/{ruleId}
```

**路径参数**:

- `ruleId` (long, 必需): 规则ID

**响应示例**:

```json
{
  "success": true,
  "message": "Rule enabled successfully: 1001",
  "data": null,
  "timestamp": 1703123456789
}
```

#### 3.2 禁用规则

**接口描述**: 禁用指定的规则

```http
POST /api/engine/rule/disable/{ruleId}
```

#### 3.3 根据业务ID启用规则

**接口描述**: 根据业务ID启用规则

```http
POST /api/engine/rule/enable/bizId/{bizId}
```

#### 3.4 根据业务ID禁用规则

**接口描述**: 根据业务ID禁用规则

```http
POST /api/engine/rule/disable/bizId/{bizId}
```

#### 3.5 根据分组ID批量启用规则

**接口描述**: 启用指定分组下的所有规则

```http
POST /api/engine/rule/enable/group/{groupId}
```

#### 3.6 根据分组ID批量禁用规则

**接口描述**: 禁用指定分组下的所有规则

```http
POST /api/engine/rule/disable/group/{groupId}
```

### 4. 规则删除接口

#### 4.1 删除规则

**接口描述**: 根据规则ID删除规则

```http
DELETE /api/engine/rule/{ruleId}
```

#### 4.2 根据业务ID删除规则

**接口描述**: 根据业务ID删除规则

```http
DELETE /api/engine/rule/bizId/{bizId}
```

#### 4.3 根据分组ID批量删除规则

**接口描述**: 删除指定分组下的所有规则

```http
DELETE /api/engine/rule/group/{groupId}
```

### 5. 管理接口

#### 5.1 获取规则统计信息

**接口描述**: 获取规则的统计信息

```http
GET /api/engine/rule/statistics
```

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "totalRules": 150,
    "enabledRules": 145,
    "timestamp": 1703123456789
  },
  "timestamp": 1703123456789
}
```

#### 5.2 刷新规则缓存

**接口描述**: 手动刷新规则缓存

```http
POST /api/engine/rule/refresh
```

**响应示例**:

```json
{
  "success": true,
  "message": "Rule cache refreshed successfully",
  "data": null,
  "timestamp": 1703123456789
}
```

### 6. 设备状态查询接口

#### 6.1 查询所有规则相关的设备即时状态

**接口描述**: 获取所有规则引用的设备点位的当前状态信息

```http
GET /api/engine/rule/device/status
```

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "LB_1F_cz_1.jc": {
      "deviceCode": "LB_1F_cz_1",
      "pointId": "jc",
      "currentValue": "0",
      "previousValue": "1",
      "currentValueStartTime": "2024-12-19T15:30:00",
      "lastUpdateTime": "2024-12-19T15:30:00",
      "valueHistory": {
        "2024-12-19T15:30:00": "0",
        "2024-12-19T15:25:00": "1"
      },
      "properties": {},
      "ruleIds": [1001, 1002],
      "stateSummary": {
        "deviceCode": "LB_1F_cz_1",
        "pointId": "jc",
        "currentValue": "0",
        "previousValue": "1",
        "currentValueStartTime": "2024-12-19T15:30:00",
        "lastUpdateTime": "2024-12-19T15:30:00",
        "currentValueDurationSeconds": 300,
        "valueHistoryCount": 2,
        "ruleReferencesCount": 2
      }
    },
    "temp_sensor_001.temperature": {
      "deviceCode": "temp_sensor_001",
      "pointId": "temperature",
      "currentValue": 25.5,
      "previousValue": 24.8,
      "currentValueStartTime": "2024-12-19T15:28:00",
      "lastUpdateTime": "2024-12-19T15:28:00",
      "valueHistory": {},
      "properties": {},
      "ruleIds": [1003],
      "stateSummary": {
        "deviceCode": "temp_sensor_001",
        "pointId": "temperature",
        "currentValue": 25.5,
        "previousValue": 24.8,
        "currentValueStartTime": "2024-12-19T15:28:00",
        "lastUpdateTime": "2024-12-19T15:28:00",
        "currentValueDurationSeconds": 120,
        "valueHistoryCount": 0,
        "ruleReferencesCount": 1
      }
    }
  },
  "timestamp": 1703123456789
}
```

**调用示例**:

```bash
curl -X GET "http://localhost:6001/api/engine/rule/device/status"
```

#### 6.2 查询有持续状态监控的设备即时状态

**接口描述**: 获取所有配置了持续时间条件的设备状态监控信息

```http
GET /api/engine/rule/device/keepStatus
```

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": [
    {
      "conditionId": "rule_1001_condition_1",
      "ruleId": 1001,
      "deviceCode": "LB_1F_cz_1",
      "pointId": "jc",
      "operator": "STATES_KEEP_SECONDS",
      "subOperator": "EQUALS",
      "value": "0",
      "value2": null,
      "durationSeconds": 300,
      "dataType": "STRING",
      "conditionStartTime": "2024-12-19T15:30:00",
      "currentlyMet": true,
      "conditionExpression": "LB_1F_cz_1.jc EQUALS 0 FOR 300 SECONDS",
      "currentDurationSeconds": 180,
      "durationSatisfied": false
    },
    {
      "conditionId": "rule_1002_condition_1",
      "ruleId": 1002,
      "deviceCode": "temp_sensor_001",
      "pointId": "temperature",
      "operator": "STATES_KEEP_SECONDS",
      "subOperator": "GREATER_THAN",
      "value": 30,
      "value2": null,
      "durationSeconds": 600,
      "dataType": "DOUBLE",
      "conditionStartTime": "2024-12-19T15:25:00",
      "currentlyMet": true,
      "conditionExpression": "temp_sensor_001.temperature GREATER_THAN 30 FOR 600 SECONDS",
      "currentDurationSeconds": 480,
      "durationSatisfied": false
    }
  ],
  "timestamp": 1703123456789
}
```

**调用示例**:

```bash
curl -X GET "http://localhost:6001/api/engine/rule/device/keepStatus"
```

#### 6.3 查询某个规则的即时事实条件

**接口描述**: 根据业务ID获取指定规则的当前Facts状态，用于调试和监控规则评估过程

```http
GET /api/engine/rule/status/bizId/{bizId}
```

**路径参数**:

- `bizId` (string, 必需): 业务系统唯一标识

**响应示例**:

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "rule_facts": {
      "LB_1F_cz_1.jc": "0",
      "temp_sensor_001.temperature": 25.5,
      "humidity_sensor_001.humidity": 45.2,
      "LB_1F_cz_1.jc_keep_time": 180,
      "temp_sensor_001.temperature_keep_time": 480,
      "triggered_time": 1703123456789,
      "current_time": "2024-12-19T15:35:00"
    }
  },
  "timestamp": 1703123456789
}
```

**调用示例**:

```bash
curl -X GET "http://localhost:6001/api/engine/rule/status/bizId/office_lighting_001"
```

**使用场景**:

- **规则调试**: 查看规则评估时的完整Facts数据
- **状态监控**: 实时监控规则相关的设备状态
- **问题排查**: 分析规则为什么没有触发或触发异常

## 数据模型

### RuleDefinition（规则定义）

规则定义是规则引擎的核心数据模型，包含规则的完整配置信息。

```json
{
  "ruleId": 1001,                    // 规则唯一ID（长整型）
  "ruleName": "办公室照明控制",        // 规则名称
  "bizId": "biz_001",               // 业务系统唯一标识
  "groupId": "group_lighting",       // 规则分组ID
  "priority": 1,                     // 规则优先级（数值越小优先级越高）
  "enabled": true,                   // 是否启用
  "logic": "AND",                    // 时间条件和设备条件的逻辑关系：AND | OR
  "timeConditions": [...],           // 时间条件列表
  "triggerCondition": {...},         // 设备触发条件
  "actions": [...],                  // 动作列表
  "deactivationActions": [...],      // 失活动作列表（仅TIME_DRIVEN使用）
  "description": "规则描述",          // 规则描述
  "createTime": "2024-12-19 10:30:00", // 创建时间
  "updateTime": "2024-12-19 10:30:00"  // 更新时间
}
```

### TimeCondition（时间条件）

定义规则的时间约束条件，支持复杂的时间组合逻辑。

```json
{
  "timeCronExpressions": [           // Cron表达式列表
    "0 0 8-12 * * ?",              // 上午8-12点
    "0 0 14-18 * * ?"              // 下午2-6点
  ],
  "workDays": [                      // 工作日列表
    "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"
  ],
  "season": "summer",                // 季节：summer | winter | all
  "includeDates": ["2024-06-15"],    // 强制包含的日期
  "excludeDates": ["2024-06-16"],    // 强制排除的日期
  "includeMonthDays": ["02-14", "05-01", "10-01", "12-25"],  // 年度重复包含月日
  "excludeMonthDays": ["04-01", "12-24"],  // 年度重复排除月日
  "dateRanges": [                    // 起止时间列表（OR关系）
    {
      "startDate": "2024-01-01",     // 起始日期（包含）
      "endDate": "2024-01-31",       // 结束日期（包含）
      "description": "一月份"        // 时间段描述（可选）
    },
    {
      "startDate": "2024-06-01",
      "endDate": "2024-06-30",
      "description": "六月份"
    }
  ],
  "monthDayRanges": [               // 月日时间段列表（OR关系）
    {
      "startDate": "06-01",          // 起始月日，格式"MM-dd"
      "endDate": "08-31",            // 结束月日，格式"MM-dd"
      "description": "夏季"          // 时间段描述（可选）
    },
    {
      "startDate": "12-01",          // 支持跨年时间段
      "endDate": "02-28",            // 如12月到次年2月
      "description": "冬季"
    }
  ],
  "logic": "AND",                    // 逻辑关系：AND | OR
  "enabled": true,                   // 是否启用
  "description": "工作时间条件"       // 条件描述
}
```

#### 时间条件字段说明

- **timeCronExpressions**: Cron表达式列表，定义具体的时间段
- **workDays**: 工作日列表，支持英文全称或缩写
- **season**: 季节条件，由全局日历定义具体时间范围
- **includeDates**: 强制包含的日期列表，优先级最高
- **excludeDates**: 强制排除的日期列表，优先级次高
- **includeMonthDays**: 年度重复包含月日列表，格式"MM-dd"，每年重复生效
- **excludeMonthDays**: 年度重复排除月日列表，格式"MM-dd"，每年重复排除
- **dateRanges**: 起止时间列表，支持多个时间段的OR关系组合
  - **startDate**: 起始日期（包含边界）
  - **endDate**: 结束日期（包含边界）
  - **description**: 时间段描述（可选）
- **monthDayRanges**: 月日时间段列表，支持多个月日时间段的OR关系组合
  - **startDate**: 起始月日，格式"MM-dd"（包含边界）
  - **endDate**: 结束月日，格式"MM-dd"（包含边界）
  - **description**: 时间段描述（可选）
  - **跨年支持**: 当startDate > endDate时自动识别为跨年时间段
- **logic**: 当前时间条件与其他时间条件的逻辑关系
- **enabled**: 是否启用此时间条件

#### 时间条件评估优先级

1. **强制包含日期** (includeDates) - 优先级最高
2. **年度重复包含月日** (includeMonthDays) - 年度重复包含
3. **强制排除日期** (excludeDates) - 优先级次高
4. **年度重复排除月日** (excludeMonthDays) - 年度重复排除
5. **季节条件** (season)
6. **起止时间列表** (dateRanges) - 任意一个时间段匹配即可
7. **月日时间段列表** (monthDayRanges) - 任意一个月日时间段匹配即可
8. **节假日检查** - 由全局日历定义
9. **工作日条件** (workDays)
10. **Cron表达式** (timeCronExpressions)

### TriggerCondition（触发条件）

定义设备触发条件的组合逻辑。

```json
{
  "logic": "AND",                    // 匹配逻辑：AND | OR
  "conditions": [...],               // 设备条件列表
  "enabled": true,                   // 是否启用
  "description": "设备触发条件组合"    // 条件描述
}
```

### DeviceCondition（设备条件）

定义单个设备的具体触发条件。

```json
{
  "sourceDeviceCode": "LB_B2F_nco_6",    // 数据源设备标识符Code
  "pointId": "co",                       // 监控点位属性ID，不拼上设备标识符
  "operator": "GREATER_THAN",           // 操作符（见操作符说明）
  "value": 80,                          // 比较值
  "upperValue": 35,                     // 上限值（BETWEEN操作符使用）
  "subOperator": "EQUALS",              // 子操作符，仅当operator=STATES_KEEP_SECONDS时生效，默认为EQUALS
  "durationSeconds": 600,               // 持续时间（秒）
  "dataType": "DOUBLE",                 // 数据类型：STRING | INTEGER | DOUBLE | BOOLEAN
  "enabled": true,                      // 是否启用
  "description": "温度大于30度持续600秒"   // 条件描述
}
```

### ActionDefinition（动作定义）

定义规则触发后执行的动作。

```json
{
  "actionType": "DEVICE_CONTROL",            // 动作类型（见动作类型说明）
  "priority": 0,                             //开头类的action必须独立，并且设置这个值为0
  "params": {                                // 动作参数，对于设备操作来说，点位和点位属性值
    "topic":"inx/gw/set/W7MGFZRcB4/bacnet",  //消息主题
    "data":
        {
           "deviceCode":"L5_14F_vrf_34"      //设备标识符(modbus)/控制器(bacnet)/设备模块(knx)
           "tagCode":"co"                    //属性点位，Modbus不用拼，bacnet和knx需要拼接设备标识符
           "val":"80"                        //设置的点位值
        }
  },
  "checkParams":{},                          //检查执行是否成功用到的参数，预留
  "enabled": true,                           // 是否启用
  "description": "开启照明设备"                // 动作描述
}
```

## 支持的操作符

### 基础比较操作符

- `EQUALS`: 等于
- `NOT_EQUALS`: 不等于
- `GREATER_THAN`: 大于
- `GREATER_THAN_OR_EQUAL`: 大于等于
- `LESS_THAN`: 小于
- `LESS_THAN_OR_EQUAL`: 小于等于

### 字符串操作符

- `CONTAINS`: 包含
- `NOT_CONTAINS`: 不包含
- `STARTS_WITH`: 以...开始
- `ENDS_WITH`: 以...结束

### 范围操作符

- `BETWEEN`: 介于两个值之间（需要value和upperValue）
- `NOT_BETWEEN`: 不在两个值之间
- `IN`: 在列表中
- `NOT_IN`: 不在列表中

### 持续时间操作符

- `STATES_KEEP_SECONDS`: 状态持续指定秒数（需要durationSeconds和subOperator）
  - 当使用此操作符时，实际的值比较逻辑由`subOperator`字段指定
  - `subOperator`支持所有基础比较操作符：EQUALS、GREATER_THAN、LESS_THAN、BETWEEN等
  - 示例：`operator="STATES_KEEP_SECONDS", subOperator="GREATER_THAN"` 表示"大于某值持续X秒"

## 支持的动作类型

### DEVICE_CONTROL（设备控制）

控制目标设备执行特定操作。

**参数示例**:

```json
{
  "actionType": "DEVICE_CONTROL",
  "priority":1,                             //执行优行级，越小越高，如果是开关类的，必须设置为1，其他大于1
  "params": {
    "topic":"inx/gw/set/W7MGFZRcB4/bacnet", //消息主题
    "data":
        {
           "deviceCode":"warm_white"       //设备标识符(modbus)/控制器(bacnet)/设备模块(knx)
           "tagCode":"button"              //属性点位，Modbus不用拼，bacnet和knx需要拼接设备标识符
           "val":"on"
        }
  }
 "checkParams":{},//检查执行是否成功用到的参数，预留
}
```

### SEND_MESSAGE（发送消息）

发送通知消息。

**参数示例**:

```json
{
  "actionType": "SEND_MESSAGE",
  "params": {
    "messageType": "email",
    "recipient": "<EMAIL>",
    "subject": "设备告警",
    "content": "设备 {deviceCode} 温度过高"
  }
}
```

### CALL_API（调用API）

调用外部API接口。

**参数示例**:

```json
{
  "actionType": "CALL_API",
  "params": {
    "url": "http://api.example.com/notify",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "deviceCode": "{deviceCode}",
      "alert": "temperature_high"
    }
  }
}
```

### LOG_EVENT（记录事件）

记录事件日志。

**参数示例**:

```json
{
  "actionType": "LOG_EVENT",
  "params": {
    "level": "INFO",
    "message": "规则 {ruleId} 被触发",
    "category": "rule_execution"
  }
}
```

## 条件逻辑说明

### logic字段说明

`logic`字段控制时间条件和设备条件之间的逻辑关系：

- **AND**: 时间条件和设备条件都必须满足
- **OR**: 时间条件或设备条件满足其一即可

#### 使用场景示例

**AND逻辑场景**：

```json
{
  "logic": "AND",
  "timeConditions": [{"timeCronExpressions": ["0 0 9-17 ? * ?"]}],
  "triggerCondition": {
    "conditions": [{"sourceDeviceCode": "temp_sensor", "operator": "GREATER_THAN", "value": 25}]
  }
}
```

含义：工作时间(9:00-17:00) **且** 温度>25°C 时触发

**OR逻辑场景**：

```json
{
  "logic": "OR",
  "timeConditions": [{"timeCronExpressions": ["0 0 9-17 ? * ?"]}],
  "triggerCondition": {
    "conditions": [{"sourceDeviceCode": "motion_sensor", "operator": "EQUALS", "value": "DETECTED"}]
  }
}
```

含义：工作时间(9:00-17:00) **或** 检测到人体移动 时触发

### 年度重复月日使用场景

**节假日特殊照明控制**：

```json
{
  "bizId": "holiday_lighting_control",
  "ruleName": "节假日特殊照明控制",
  "logic": "AND",
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 18 ? * *"],
      "includeMonthDays": [
        "01-01",  // 元旦
        "02-14",  // 情人节
        "05-01",  // 劳动节
        "10-01",  // 国庆节
        "12-25"   // 圣诞节
      ],
      "excludeMonthDays": [
        "04-01"   // 愚人节排除
      ],
      "logic": "AND",
      "enabled": true
    }
  ],
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "set_holiday_mode",
        "brightness": "100",
        "color": "festive"
      }
    }
  ]
}
```

**说明**：

- **年度重复包含**：每年的元旦、情人节、劳动节、国庆节、圣诞节都会在18:00触发特殊照明
- **年度重复排除**：每年的愚人节不执行此规则
- **优势**：无需每年更新规则配置，自动适用于所有年份

## 使用示例

### 场景1：办公室照明自动控制

**需求**: 工作日8-18点，当办公室无人超过15分钟时自动关闭照明

**实现步骤**:

1. **创建规则**:
   
   ```bash
   curl -X POST "http://localhost:6001/api/engine/rule" \
   -H "Content-Type: application/json" \
   -d '{
    "bizId": "office_lighting_001",
    "ruleName": "办公室照明自动控制",
   
    "groupId": "lighting_control",
    "priority": 1,
    "enabled": true,
    "logic": "AND",
    "timeConditions": [
      {
        "timeCronExpressions": ["0 0 8-18 * * ?"],
        "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
        "logic": "AND",
        "enabled": true
      }
    ],
    "triggerCondition": {
      "logic": "AND",
      "conditions": [
        {
          "sourceDeviceCode": "occupancy_sensor_001",
          "pointId": "occupancy_status",
          "operator": "STATES_KEEP_SECONDS",
          "value": "UNOCCUPIED",
          "subOperator": "EQUALS",
          "durationSeconds": 900,
          "dataType": "STRING",
          "enabled": true
        }
      ]
    },
    "actions": [
      {
        "actionType": "DEVICE_CONTROL",
   
        "params": {
           "topic":"inx/gw/set/W7MGFZRcB4/bacnet",
           "data":
               {
                  "deviceCode":"RPC1005"
                  "tagCode":"1_1_AHU_5_SWD"
                  "val":"turn_off"
               }
        },
       "checkParams":{},//检查执行是否成功用到的参数，预留
        "enabled": true
      }
    ]
   }'
   ```

2. **查询规则状态**:
   
   ```bash
   curl -X GET "http://localhost:6001/api/engine/rule/bizId/office_lighting_001"
   ```

3. **临时禁用规则**:
   
   ```bash
   curl -X POST "http://localhost:6001/api/engine/rule/disable/bizId/office_lighting_001"
   ```

### 场景2：会议室环境控制

**需求**: 夏季工作时间，当会议室温度超过26度且湿度超过60%时启动空调

**实现步骤**:

```bash
curl -X POST "http://localhost:6001/api/engine/rule" \
  -H "Content-Type: application/json" \
  -d '{
    "bizId": "meeting_room_hvac_001",
    "ruleName": "会议室环境控制",

    "groupId": "hvac_control",
    "priority": 2,
    "enabled": true,
    "logic": "AND",
    "timeConditions": [
      {
        "timeCronExpressions": ["0 0 8-18 * * ?"],
        "season": "summer",
        "logic": "AND",
        "enabled": true
      }
    ],
    "triggerCondition": {
      "logic": "AND",
      "conditions": [
        {
          "sourceDeviceCode": "temp_sensor_001",
          "pointId": "temperature",
          "operator": "STATES_KEEP_SECONDS",
          "value": 26,
          "subOperator": "GREATER_THAN",
          "durationSeconds": 300,
          "dataType": "DOUBLE",
          "enabled": true
        },
        {
          "sourceDeviceCode": "humidity_sensor_001",
          "pointId": "humidity",
          "operator": "GREATER_THAN",
          "value": 60,
          "dataType": "DOUBLE",
          "enabled": true
        }
      ]
    },
    "actions": [
      {
        "actionType": "DEVICE_CONTROL",
        "priority":0,
        "params": {
            "topic":"inx/gw/set/W7MGFZRcB4/knx",
            "data":
                {
                   "deviceCode":"kong_tiao_001"
                   "tagCode":"kong_tiao_001_button"
                   "val":"turn_on"
                }
        },
        "checkParams":{},//检查执行是否成功用到的参数，预留
        "enabled": true
      },
      {
        "actionType": "SEND_MESSAGE",
        "params": {
            "topic":"inx/gw/set/W7MGFZRcB4/knx",
            "data":
                {
                   "deviceCode":"MTN647593-1"
                   "tagCode":"kong_tiao_001_b"
                   "val":"20"
                }
        },
        "enabled": true
      }
    ]
  }'
```

### 场景3：智能照明OR逻辑控制

**需求**: 工作时间且光照不足时开灯，或者检测到人体移动时立即开灯

```bash
curl -X POST "http://localhost:6001/api/engine/rule" \
  -H "Content-Type: application/json" \
  -d '{
    "bizId": "smart_lighting_or_logic",
    "ruleName": "智能照明OR逻辑控制",

    "groupId": "smart_lighting",
    "priority": 1,
    "enabled": true,
    "logic": "OR",
    "timeConditions": [
      {
        "timeCronExpressions": ["0 0 9-17 ? * ?"],
        "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
        "logic": "AND",
        "enabled": true
      }
    ],
    "triggerCondition": {
      "logic": "OR",
      "conditions": [
        {
          "sourceDeviceCode": "light_sensor_001",
          "pointId": "illuminance",
          "operator": "LESS_THAN",
          "value": 60,
          "dataType": "DOUBLE",
          "enabled": true
        },
        {
          "sourceDeviceCode": "motion_sensor_001",
          "pointId": "presence",
          "operator": "EQUALS",
          "value": "DETECTED",
          "dataType": "STRING",
          "enabled": true
        }
      ]
    },
    "actions": [
      {
        "actionType": "DEVICE_CONTROL",
        "priority":0,
        "params": {
            "topic":"inx/gw/set/W7MGFZRcB4/bacnet",
            "data":
                {
                   "deviceCode":"RPC1005"
                   "tagCode":"light_001_button"
                   "val":"on"
                }
        },
        "checkParams":{},//检查执行是否成功用到的参数，预留
        "enabled": true
      }
    ]
  }'
```

### 场景4：批量管理照明规则

**需求**: 为多个办公室批量创建相似的照明控制规则

```bash
curl -X POST "http://localhost:6001/api/engine/rule/batch" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "bizId": "office_101_lighting",
      "ruleName": "办公室101照明控制",

      "groupId": "office_lighting",
      "priority": 1,
      "enabled": true,
      "logic": "AND",
      "timeConditions": [...],
      "triggerCondition": {...},
      "actions": [...]
    },
    {
      "bizId": "office_102_lighting",
      "ruleName": "办公室102照明控制",

      "groupId": "office_lighting",
      "priority": 1,
      "enabled": true,
      "logic": "AND",
      "timeConditions": [...],
      "triggerCondition": {...},
      "actions": [...]
    }
  ]'
```

### 场景4：规则统计和监控

**查询系统规则统计**:

```bash
curl -X GET "http://localhost:6001/api/engine/rule/statistics"
```

**查询特定分组的规则**:

```bash
curl -X GET "http://localhost:6001/api/engine/rule/groupId/office_lighting"
```

**批量禁用分组规则**:

```bash
curl -X POST "http://localhost:6001/api/engine/rule/disable/group/office_lighting"
```

## 常见错误码和处理(TODO优化)

### 业务错误（HTTP 200，success=false）

| 错误信息                                     | 原因                   | 解决方案            |
| ---------------------------------------- | -------------------- | --------------- |
| "Rule ID and Biz ID cannot all be empty" | 创建规则时ruleId和bizId都为空 | 至少提供bizId用于业务标识 |
| "Rule not found: {id}"                   | 指定的规则不存在             | 检查规则ID或业务ID是否正确 |
| "No rules found for group: {groupId}"    | 指定分组下没有规则            | 检查分组ID是否正确      |
| "Rule save failed, bizId: {bizId}"       | 规则保存失败               | 检查规则定义是否完整和正确   |

### 系统错误（HTTP 500，success=false）

| 错误类型     | 可能原因               | 解决方案          |
| -------- | ------------------ | ------------- |
| 数据库连接错误  | SQLite数据库文件损坏或权限问题 | 检查数据库文件和权限    |
| JSON解析错误 | 请求体格式不正确           | 检查JSON格式和字段类型 |
| 内部服务异常   | 规则引擎内部组件异常         | 查看系统日志，联系技术支持 |

## 使用注意事项

### 1. 规则设计原则

- **单一职责**: 每个规则只负责一个具体的控制逻辑
- **条件简化**: 避免过于复杂的条件组合，建议单个规则的设备条件不超过5个
- **优先级设置**: 重要规则设置更高优先级（数值越小优先级越高）
- **测试验证**: 规则上线前进行充分测试

### 2. 性能考虑

- **批量操作**: 批量创建/更新规则时，建议每批不超过100个规则
- **合理缓存**: 规则变更后可调用刷新接口更新缓存
- **监控告警**: 定期查询规则统计信息，监控系统状态

### 3. 数据一致性

- **唯一标识**: bizId在业务系统中必须唯一
- **分组管理**: 合理使用groupId进行规则分组，便于批量管理
- **状态同步**: 规则启用/禁用操作会同步更新内存状态和数据库

### 4. 错误处理建议

- **重试机制**: API调用失败时实现指数退避重试
- **降级策略**: 规则引擎不可用时的备用方案
- **日志记录**: 详细记录操作日志，便于问题排查

### 5. 安全考虑

- **参数验证**: 严格验证输入参数，特别是设备Code和动作参数
- **权限控制**: 根据业务需要实现API访问权限控制
- **敏感信息**: 避免在规则参数中存储敏感信息如密码等

## 相关概念简介

### 规则引擎基本原理

本规则引擎基于EasyRules框架，采用事实-规则-动作（Fact-Rule-Action）模式：

1. **事实（Facts）**: 设备状态、时间信息等输入数据
2. **规则（Rules）**: 基于事实的条件判断逻辑
3. **动作（Actions）**: 规则触发后执行的操作

### 触发类型说明

- **EVENT_DRIVEN（事件驱动）**: 由设备事件触发，时间作为守卫条件
- **TIME_DRIVEN（时间驱动）**: 由时间直接触发，支持定时执行

### 状态管理机制

系统内置设备状态管理器，支持：

- 设备状态缓存和持续时间监控
- 状态变化事件发布
- 自动清理过期状态数据

### 动作执行监控接口

系统提供专门的动作执行监控接口，用于跟踪和管理动作执行状态：

#### 获取动作执行队列状态

```http
GET /api/engine/monitor/action-execution-queue
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "totalCount": 15,
    "statusCount": {
      "EXECUTING": 3,
      "SUCCESS": 10,
      "FAILED": 2
    },
    "seqMappingCount": 3
  },
  "timestamp": 1703001234567
}
```

#### 获取动作执行统计信息

```http
GET /api/engine/monitor/action-execution-stats
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "actionTypeCount": {
      "DEVICE_CONTROL": 12,
      "SEND_MESSAGE": 3
    },
    "ruleCount": {
      "2507041151484460": 8,
      "2507041151484461": 7
    },
    "retryCount": {
      "0": 13,
      "1": 2
    }
  },
  "timestamp": 1703001234567
}
```

#### 获取动作监控统计

```http
GET /api/engine/monitor/action-monitor-stats
```

#### 手动触发队列清理

```http
POST /api/engine/monitor/action-execution-cleanup
```

**响应示例**：
```json
{
  "success": true,
  "message": "Action execution queue cleanup triggered successfully",
  "timestamp": 1703001234567
}
```

通过这些接口，您可以构建复杂的物联网设备控制逻辑，实现智能化的楼宇管理系统，并对动作执行过程进行全面监控。
