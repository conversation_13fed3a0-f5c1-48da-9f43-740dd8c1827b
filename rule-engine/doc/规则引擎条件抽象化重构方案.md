# 规则引擎条件抽象化重构方案

## 文档信息

- **文档版本**: 1.0.0
- **创建日期**: 2025-06-28
- **文档状态**: 设计方案
- **实施优先级**: 中期规划（当前版本不实施）

## 1. 背景和问题分析

### 1.1 当前架构的限制

当前规则引擎在条件表达方面存在以下限制：

1. **条件类型固化**: 时间条件和设备条件在架构中是分离的，无法灵活组合
2. **逻辑表达能力不足**: 无法表达复杂的嵌套逻辑，如 `(时间条件 AND 设备条件) OR 其他设备条件`
3. **扩展性受限**: 新增条件类型（如地理位置、天气等）需要大量架构调整
4. **触发机制僵化**: 事件驱动和时间驱动的区分过于绝对

### 1.2 典型问题场景

**场景**: 智能照明控制
- 需求: `(时间在9:00~17:00 且光照强度<60%) 或者 有人来了` → 开灯
- 当前问题: 无法在单个规则中表达此逻辑，需要创建多个规则

### 1.3 业务发展需求

随着物联网场景的复杂化，预期会出现更多复杂的条件组合需求：
- 多维度条件组合（时间+设备+环境+用户行为）
- 深层嵌套逻辑表达
- 动态条件类型扩展

## 2. 设计目标和原则

### 2.1 设计目标

1. **统一条件抽象**: 将所有类型的条件统一为抽象的Condition接口
2. **灵活逻辑组合**: 支持任意深度的AND/OR嵌套组合
3. **易于扩展**: 新增条件类型只需实现Condition接口
4. **向后兼容**: 现有规则配置能够平滑迁移
5. **性能优化**: 支持条件评估的短路优化和缓存

### 2.2 设计原则

1. **开闭原则**: 对扩展开放，对修改封闭
2. **单一职责**: 每个条件类型专注于自己的评估逻辑
3. **组合优于继承**: 通过组合实现复杂逻辑，而非深层继承
4. **渐进式重构**: 支持分阶段实施，降低风险

## 3. 技术方案设计

### 3.1 核心接口设计

```java
/**
 * 抽象条件接口
 * 所有条件类型的统一抽象
 */
public interface Condition {
    /**
     * 评估条件是否满足
     * @param facts 事实数据
     * @return 条件是否满足
     */
    boolean evaluate(Facts facts);
    
    /**
     * 获取条件依赖的设备点位
     * @return 依赖的设备点位集合
     */
    Set<DevicePointRef> getDependencies();
    
    /**
     * 获取条件类型
     * @return 条件类型枚举
     */
    ConditionType getType();
    
    /**
     * 获取条件描述（用于日志和调试）
     * @return 条件描述
     */
    String getDescription();
    
    /**
     * 验证条件配置是否有效
     * @return 验证结果
     */
    ValidationResult validate();
}
```

### 3.2 条件类型枚举

```java
public enum ConditionType {
    TIME("时间条件"),
    DEVICE("设备条件"), 
    COMPOSITE("复合条件"),
    LOCATION("位置条件"),    // 预留扩展
    WEATHER("天气条件"),     // 预留扩展
    USER_BEHAVIOR("用户行为条件"); // 预留扩展
    
    private final String description;
}
```

### 3.3 复合条件设计

```java
/**
 * 复合条件 - 支持嵌套逻辑组合
 */
public class CompositeCondition implements Condition {
    
    public enum LogicOperator {
        AND("且"),
        OR("或");
    }
    
    private LogicOperator operator;
    private List<Condition> subConditions;
    private String description;
    private boolean enabled = true;
    
    @Override
    public boolean evaluate(Facts facts) {
        if (!enabled || subConditions == null || subConditions.isEmpty()) {
            return true;
        }
        
        if (operator == LogicOperator.AND) {
            return subConditions.stream()
                .filter(Condition::isEnabled)
                .allMatch(condition -> condition.evaluate(facts));
        } else {
            return subConditions.stream()
                .filter(Condition::isEnabled)
                .anyMatch(condition -> condition.evaluate(facts));
        }
    }
    
    @Override
    public Set<DevicePointRef> getDependencies() {
        return subConditions.stream()
            .flatMap(condition -> condition.getDependencies().stream())
            .collect(Collectors.toSet());
    }
}
```

### 3.4 具体条件实现

```java
/**
 * 时间条件实现
 */
public class TimeConditionImpl implements Condition {
    private List<String> timeCronExpressions;
    private List<String> workDays;
    private String season;
    private List<LocalDate> includeDates;
    private List<LocalDate> excludeDates;
    private List<DateRange> dateRanges;
    private boolean enabled = true;
    
    @Override
    public boolean evaluate(Facts facts) {
        if (!enabled) return true;
        
        LocalDateTime now = LocalDateTime.now();
        return timeConditionEvaluator.isTimeConditionMet(this, now);
    }
    
    @Override
    public Set<DevicePointRef> getDependencies() {
        return Collections.emptySet(); // 时间条件不依赖设备点位
    }
}

/**
 * 设备条件实现
 */
public class DeviceConditionImpl implements Condition {
    private String sourceDeviceCode;
    private String pointId;
    private String operator;
    private Object value;
    private Object upperValue;
    private long durationSeconds;
    private String dataType;
    private boolean enabled = true;
    
    @Override
    public boolean evaluate(Facts facts) {
        if (!enabled) return true;
        
        // 持续时间条件特殊处理
        if (Operators.Duration.STATES_KEEP_SECONDS.equals(operator)) {
            return evaluateStateKeepTime(facts);
        }
        
        // 常规设备条件评估
        String factKey = FactKey.getStateKey(sourceDeviceCode, pointId);
        Object factValue = facts.get(factKey);
        
        if (factValue == null) {
            return false;
        }
        
        return ValueComparator.compare(factValue, operator, value, upperValue, dataType);
    }
    
    @Override
    public Set<DevicePointRef> getDependencies() {
        return Set.of(new DevicePointRef(sourceDeviceCode, pointId));
    }
}
```

## 4. 数据模型重构

### 4.1 新的RuleDefinition结构

```java
public class RuleDefinition {
    // 基础字段保持不变
    private Long ruleId;
    private String ruleName;

    // ... 其他字段
    
    // 统一的条件定义（替换原有的timeConditions和triggerCondition）
    private Condition rootCondition;
    
    // 动作定义保持不变
    private List<ActionDefinition> actions;
    private List<ActionDefinition> deactivationActions;
    
    // 新增：触发策略（替换原有的triggerType）
    private TriggerStrategy triggerStrategy;
}
```

### 4.2 触发策略重新设计

```java
public enum TriggerStrategy {
    /**
     * 事件驱动：由设备事件触发条件评估
     */
    EVENT_DRIVEN,
    
    /**
     * 时间驱动：由时间调度器触发条件评估
     */
    TIME_DRIVEN,
    
    /**
     * 混合驱动：既可以由事件触发，也可以由时间触发
     * 适用于包含时间条件和设备条件的复杂规则
     */
    HYBRID_DRIVEN,
    
    /**
     * 智能驱动：系统根据条件类型自动判断触发策略
     */
    AUTO_DETECT
}
```

## 5. 核心组件重构

### 5.1 条件评估引擎

```java
/**
 * 统一的条件评估引擎
 */
@Service
public class ConditionEvaluationEngine {
    
    /**
     * 评估条件树
     */
    public boolean evaluateCondition(Condition condition, Facts facts) {
        if (condition == null) {
            return true;
        }
        
        try {
            return condition.evaluate(facts);
        } catch (Exception e) {
            logger.error("Error evaluating condition: {}", condition.getDescription(), e);
            return false;
        }
    }
    
    /**
     * 递归提取所有依赖
     */
    public Set<DevicePointRef> extractDependencies(Condition condition) {
        if (condition == null) {
            return Collections.emptySet();
        }
        
        return condition.getDependencies();
    }
    
    /**
     * 条件短路优化
     */
    private boolean evaluateWithShortCircuit(CompositeCondition composite, Facts facts) {
        // AND条件：遇到false立即返回
        // OR条件：遇到true立即返回
        // 实现短路优化逻辑
    }
}
```

### 5.2 智能触发调度器

```java
/**
 * 智能触发调度器
 * 根据条件类型自动判断触发策略
 */
@Service
public class IntelligentTriggerScheduler {
    
    /**
     * 分析规则的触发需求
     */
    public TriggerRequirement analyzeTriggerRequirement(RuleDefinition rule) {
        Set<ConditionType> conditionTypes = analyzeConditionTypes(rule.getRootCondition());
        
        boolean hasTimeCondition = conditionTypes.contains(ConditionType.TIME);
        boolean hasDeviceCondition = conditionTypes.contains(ConditionType.DEVICE);
        
        if (hasTimeCondition && hasDeviceCondition) {
            return TriggerRequirement.HYBRID;
        } else if (hasTimeCondition) {
            return TriggerRequirement.TIME_ONLY;
        } else {
            return TriggerRequirement.EVENT_ONLY;
        }
    }
    
    /**
     * 递归分析条件类型
     */
    private Set<ConditionType> analyzeConditionTypes(Condition condition) {
        // 递归遍历条件树，收集所有条件类型
    }
}
```

## 6. JSON序列化支持

### 6.1 多态序列化配置

```java
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = TimeConditionImpl.class, name = "TIME"),
    @JsonSubTypes.Type(value = DeviceConditionImpl.class, name = "DEVICE"),
    @JsonSubTypes.Type(value = CompositeCondition.class, name = "COMPOSITE")
})
public interface Condition {
    // 接口定义
}
```

### 6.2 示例JSON结构

```json
{
  "ruleId": "smart_lighting_001",
  "ruleName": "智能照明控制",
  "rootCondition": {
    "type": "COMPOSITE",
    "operator": "OR",
    "subConditions": [
      {
        "type": "COMPOSITE", 
        "operator": "AND",
        "subConditions": [
          {
            "type": "TIME",
            "timeCronExpressions": ["0 0 9-17 ? * ?"],
            "enabled": true
          },
          {
            "type": "DEVICE",
            "sourceDeviceCode": "light_sensor_001",
            "pointId": "illuminance",
            "operator": "LESS_THAN",
            "value": 60,
            "dataType": "DOUBLE",
            "enabled": true
          }
        ]
      },
      {
        "type": "DEVICE",
        "sourceDeviceCode": "motion_sensor_001", 
        "pointId": "presence",
        "operator": "EQUALS",
        "value": "DETECTED",
        "enabled": true
      }
    ]
  },
  "triggerStrategy": "HYBRID_DRIVEN",
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "turn_on"
      }
    }
  ]
}
```

## 7. 向后兼容性设计

### 7.1 数据迁移策略

```java
/**
 * 规则格式迁移器
 * 将旧格式规则转换为新格式
 */
@Component
public class RuleMigrationService {

    /**
     * 迁移旧格式规则
     */
    public RuleDefinition migrateFromLegacyFormat(LegacyRuleDefinition legacy) {
        RuleDefinition newRule = new RuleDefinition();

        // 复制基础字段
        copyBasicFields(legacy, newRule);

        // 转换条件结构
        Condition rootCondition = convertConditions(legacy);
        newRule.setRootCondition(rootCondition);

        // 推断触发策略
        TriggerStrategy strategy = inferTriggerStrategy(legacy);
        newRule.setTriggerStrategy(strategy);

        return newRule;
    }

    private Condition convertConditions(LegacyRuleDefinition legacy) {
        List<Condition> conditions = new ArrayList<>();

        // 转换时间条件
        if (legacy.getTimeConditions() != null && !legacy.getTimeConditions().isEmpty()) {
            for (TimeCondition timeCondition : legacy.getTimeConditions()) {
                conditions.add(convertTimeCondition(timeCondition));
            }
        }

        // 转换设备条件
        if (legacy.getTriggerCondition() != null) {
            Condition deviceCondition = convertTriggerCondition(legacy.getTriggerCondition());
            conditions.add(deviceCondition);
        }

        // 根据原有逻辑组合条件
        if (conditions.size() == 1) {
            return conditions.get(0);
        } else if (conditions.size() > 1) {
            // 旧格式中时间条件和设备条件是AND关系
            CompositeCondition composite = new CompositeCondition();
            composite.setOperator(CompositeCondition.LogicOperator.AND);
            composite.setSubConditions(conditions);
            return composite;
        }

        return null;
    }
}
```

### 7.2 API兼容性

```java
/**
 * 兼容性适配器
 * 保持旧API的兼容性
 */
@RestController
@RequestMapping("/api/v1/rules")
public class LegacyRuleController {

    @PostMapping("/legacy")
    public ResponseEntity<String> createLegacyRule(@RequestBody LegacyRuleDefinition legacy) {
        // 转换为新格式
        RuleDefinition newRule = migrationService.migrateFromLegacyFormat(legacy);

        // 使用新的规则管理器处理
        String result = ruleManager.createRule(newRule);

        return ResponseEntity.ok(result);
    }
}
```

## 8. 实施计划

### 8.1 阶段划分

**阶段一：基础设施建设（2-3周）**
- 设计和实现Condition接口体系
- 实现TimeConditionImpl和DeviceConditionImpl
- 建立JSON序列化支持
- 实现数据迁移工具

**阶段二：核心引擎重构（3-4周）**
- 重构RuleAdapterService为ConditionEvaluationEngine
- 实现智能触发调度器
- 重构FactsBuilder和DependencyAnalyzer
- 更新StateManager的注册逻辑

**阶段三：集成和测试（2-3周）**
- 全面集成测试
- 性能测试和优化
- 向后兼容性测试
- 文档更新

**阶段四：灰度发布（1-2周）**
- 小范围灰度测试
- 监控和问题修复
- 全量发布

### 8.2 风险评估和缓解

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| 性能回归 | 中 | 条件评估性能下降 | 实现短路优化、缓存机制 |
| 兼容性问题 | 高 | 现有规则无法正常工作 | 完善迁移工具、充分测试 |
| 复杂度增加 | 中 | 开发和维护成本上升 | 完善文档、代码规范 |
| 数据迁移失败 | 高 | 历史数据丢失 | 备份策略、回滚机制 |

### 8.3 成功标准

1. **功能完整性**: 所有现有功能正常工作
2. **性能指标**: 条件评估性能不低于当前版本的90%
3. **兼容性**: 100%的现有规则能够成功迁移
4. **扩展性**: 能够轻松添加新的条件类型
5. **可维护性**: 代码复杂度在可控范围内

## 9. 技术细节补充

### 9.1 性能优化策略

```java
/**
 * 条件评估缓存
 */
@Component
public class ConditionEvaluationCache {

    private final Cache<String, Boolean> evaluationCache;

    /**
     * 缓存条件评估结果
     * 适用于短时间内重复评估的场景
     */
    public boolean evaluateWithCache(Condition condition, Facts facts) {
        String cacheKey = generateCacheKey(condition, facts);

        return evaluationCache.get(cacheKey, () -> {
            return condition.evaluate(facts);
        });
    }
}
```

### 9.2 条件验证框架

```java
/**
 * 条件验证器
 */
public interface ConditionValidator {
    ValidationResult validate(Condition condition);
}

/**
 * 复合条件验证器
 */
@Component
public class CompositeConditionValidator implements ConditionValidator {

    @Override
    public ValidationResult validate(Condition condition) {
        if (!(condition instanceof CompositeCondition)) {
            return ValidationResult.success();
        }

        CompositeCondition composite = (CompositeCondition) condition;

        // 检查嵌套深度
        if (calculateDepth(composite) > MAX_NESTING_DEPTH) {
            return ValidationResult.error("嵌套深度超过限制");
        }

        // 检查子条件数量
        if (composite.getSubConditions().size() > MAX_SUB_CONDITIONS) {
            return ValidationResult.error("子条件数量超过限制");
        }

        // 递归验证子条件
        for (Condition subCondition : composite.getSubConditions()) {
            ValidationResult result = validate(subCondition);
            if (!result.isValid()) {
                return result;
            }
        }

        return ValidationResult.success();
    }
}
```

### 9.3 调试和监控支持

```java
/**
 * 条件评估跟踪器
 * 用于调试和性能监控
 */
@Component
public class ConditionEvaluationTracker {

    /**
     * 跟踪条件评估过程
     */
    public boolean evaluateWithTracking(Condition condition, Facts facts, String ruleId) {
        long startTime = System.currentTimeMillis();

        try {
            boolean result = condition.evaluate(facts);

            // 记录评估结果
            logEvaluationResult(ruleId, condition, result, startTime);

            return result;
        } catch (Exception e) {
            // 记录评估异常
            logEvaluationError(ruleId, condition, e, startTime);
            throw e;
        }
    }

    private void logEvaluationResult(String ruleId, Condition condition,
                                   boolean result, long startTime) {
        long duration = System.currentTimeMillis() - startTime;

        logger.debug("Rule {} condition evaluation: {} -> {} ({}ms)",
                    ruleId, condition.getDescription(), result, duration);

        // 发送监控指标
        meterRegistry.timer("condition.evaluation.duration")
            .record(duration, TimeUnit.MILLISECONDS);
    }
}
```

## 10. 总结

### 10.1 方案优势

1. **统一抽象**: 所有条件类型统一为Condition接口，架构更加清晰
2. **灵活组合**: 支持任意复杂的逻辑组合，满足复杂业务需求
3. **易于扩展**: 新增条件类型只需实现接口，无需修改核心逻辑
4. **向后兼容**: 通过迁移工具保证现有规则的平滑升级
5. **性能优化**: 支持短路评估、缓存等优化策略

### 10.2 实施建议

1. **分阶段实施**: 降低风险，确保每个阶段的稳定性
2. **充分测试**: 重点关注兼容性和性能测试
3. **监控完善**: 建立完善的监控体系，及时发现问题
4. **文档同步**: 及时更新技术文档和用户手册

### 10.3 后续扩展方向

1. **条件类型扩展**: 位置条件、天气条件、用户行为条件等
2. **智能优化**: 基于机器学习的条件评估优化
3. **可视化配置**: 图形化的条件配置界面
4. **分布式支持**: 支持跨节点的条件评估

---

**注意**: 本方案为中期规划，当前版本不实施。建议在业务需求明确后，结合实际情况调整实施计划。
