# 规则引擎动作执行机制技术文档

## 1. 功能与背景

### 1.1 功能概述

规则引擎动作执行机制是规则引擎的核心组成部分，负责在规则条件满足时执行相应的动作。动作执行机制支持多种动作类型，包括设备控制、消息发送、API调用和日志记录等。本文档详细描述了动作执行机制的设计、实现和工作流程。

### 1.2 背景与需求

在物联网场景中，规则引擎需要根据设备状态变化或时间条件触发一系列动作。这些动作可能包括控制设备、发送通知消息、调用外部API等。动作执行机制需要满足以下关键需求：

1. **优先级控制**：某些动作（如设备开关）必须先执行且成功后，才能执行其他动作（如设置属性）
2. **执行状态跟踪**：需要跟踪每个动作的执行状态，包括成功、失败、超时等
3. **反馈处理**：处理设备执行指令后的反馈信息，更新执行状态
4. **超时重试**：对于未收到反馈的动作，需要支持超时重试机制
5. **执行日志**：记录详细的执行日志，便于问题排查

### 1.3 主要特性

动作执行机制提供以下核心特性：

- **优先级执行控制**：priority=1的动作必须先执行且成功后才能执行其他动作
- **执行状态队列管理**：维护动作执行状态队列，跟踪每个动作的执行情况
- **MQTT反馈处理**：通过MQTT消息接收设备执行反馈，更新执行状态
- **超时重试机制**：支持可配置的超时时间和重试次数（默认3秒超时，最多重试3次）
- **异步非阻塞执行**：使用线程池和CompletableFuture实现异步非阻塞执行
- **执行日志记录**：独立的动作执行日志文件，详细记录执行过程
- **监控接口**：提供REST API监控动作执行状态和统计信息
- **重复操作防护**：30分钟窗口内防止重复执行相同操作

## 2. 代码模块调用关系

### 2.1 核心组件

动作执行机制由以下核心组件组成：

1. **ActionExecutionCoordinator**：动作执行协调器，负责优先级控制和执行协调
2. **ActionExecutor**：动作执行器，负责执行具体的动作
3. **ActionExecutionMonitor**：执行监控器，负责超时检测和重试
4. **ActionExecutionRecord**：执行记录，存储动作执行状态和上下文
5. **ActionExecutionContext**：执行上下文，管理一组相关动作的执行
6. **ActionExecutionLogger**：执行日志记录器，记录执行过程
7. **MessageReceiver**：消息接收器，处理MQTT反馈消息

### 2.2 组件关系图

```mermaid
graph TD
    RuleEngine[RuleEngineService] --> RuleAdapter[RuleAdapterService]
    RuleAdapter --> ActionCoord[ActionExecutionCoordinator]
    ActionCoord --> ActionExec[ActionExecutor]
    ActionCoord --> ActionMonitor[ActionExecutionMonitor]
    ActionCoord --> ActionLogger[ActionExecutionLogger]
    ActionExec --> MQTT[MessageSender]
    MQTT --> MQTTClient[MQTT Client]
    MQTTClient --> MQTTReceiver[MessageReceiver]
    MQTTReceiver --> ActionCoord

    subgraph "动作执行核心组件"
        ActionCoord
        ActionExec
        ActionMonitor
        ActionLogger
    end

    subgraph "数据模型"
        ActionRecord[ActionExecutionRecord]
        ActionContext[ActionExecutionContext]
    end

    ActionCoord --> ActionRecord
    ActionCoord --> ActionContext

    subgraph "监控接口"
        MonitorAPI[MonitorController]
    end

    MonitorAPI --> ActionCoord
    MonitorAPI --> ActionMonitor
```

### 2.3 组件职责说明

| 组件                         | 主要职责                          |
| -------------------------- | ----------------------------- |
| ActionExecutionCoordinator | 动作执行协调器，负责优先级控制、执行队列管理、状态跟踪   |
| ActionExecutor             | 动作执行器，负责执行具体类型的动作，如设备控制、消息发送等 |
| ActionExecutionMonitor     | 执行监控器，负责检测超时动作并触发重试           |
| ActionExecutionRecord      | 执行记录，存储动作执行状态、重试次数、错误信息等      |
| ActionExecutionContext     | 执行上下文，管理一组相关动作的执行状态和关系        |
| ActionExecutionLogger      | 执行日志记录器，记录详细的执行过程日志           |
| MessageReceiver            | 消息接收器，处理MQTT反馈消息并更新执行状态       |

## 3. 动作执行实例生命周期

### 3.1 生命周期状态图

```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建执行记录
    PENDING --> EXECUTING: 开始执行
    EXECUTING --> SUCCESS: 收到成功反馈
    EXECUTING --> FAILED: 收到失败反馈
    EXECUTING --> TIMEOUT: 超时未收到反馈
    TIMEOUT --> EXECUTING: 重试(次数<3)
    TIMEOUT --> FAILED: 重试次数>=3
    SUCCESS --> [*]: 清理队列
    FAILED --> [*]: 清理队列
```

### 3.2 状态说明

| 状态        | 说明                  |
| --------- | ------------------- |
| PENDING   | 初始状态，动作已创建但尚未开始执行   |
| EXECUTING | 动作正在执行中，等待反馈        |
| SUCCESS   | 动作执行成功，已收到成功反馈      |
| FAILED    | 动作执行失败，已收到失败反馈或内部错误 |
| TIMEOUT   | 动作执行超时，未在指定时间内收到反馈  |

### 3.3 生命周期事件

| 事件   | 触发条件            | 状态转换                |
| ---- | --------------- | ------------------- |
| 创建   | 规则条件满足，创建动作执行记录 | → PENDING           |
| 执行   | 开始执行动作          | PENDING → EXECUTING |
| 成功反馈 | 收到MQTT成功反馈消息    | EXECUTING → SUCCESS |
| 失败反馈 | 收到MQTT失败反馈消息    | EXECUTING → FAILED  |
| 超时   | 执行超过指定时间未收到反馈   | EXECUTING → TIMEOUT |
| 重试   | 超时后重试次数未达上限     | TIMEOUT → EXECUTING |
| 最终失败 | 重试次数达到上限        | TIMEOUT → FAILED    |
| 清理   | 执行完成后清理队列       | SUCCESS/FAILED → 销毁 |

## 4. 动作执行时序图

### 4.1 正常执行流程

```mermaid
sequenceDiagram
    participant Rule as RuleEngine
    participant Coord as ActionExecutionCoordinator
    participant Exec as ActionExecutor
    participant MQTT as MessageSender
    participant Device as 设备
    participant Receiver as MessageReceiver

    Rule->>Coord: executeActions(ruleId, actions, facts)
    Note over Coord: 按优先级排序动作

    alt 存在关键动作(priority=1)
        Coord->>Exec: executeActionWithRecord(record)
        Exec->>MQTT: sendToMqtt(topic, data)
        MQTT->>Device: 发送控制指令
        Device-->>Receiver: 发送执行反馈
        Receiver-->>Coord: handleExecutionResponse(seq, result)

        alt 关键动作成功
            Coord->>Coord: 继续执行普通动作
        else 关键动作失败
            Coord->>Coord: 停止执行后续动作
        end
    end

    loop 对每个普通动作
        Coord->>Exec: executeActionWithRecord(record)
        Exec->>MQTT: sendToMqtt(topic, data)
        MQTT->>Device: 发送控制指令
        Device-->>Receiver: 发送执行反馈
        Receiver-->>Coord: handleExecutionResponse(seq, result)
    end

    Coord->>Coord: 记录执行完成
```

### 4.2 超时重试流程

```mermaid
sequenceDiagram
    participant Coord as ActionExecutionCoordinator
    participant Exec as ActionExecutor
    participant Monitor as ActionExecutionMonitor
    participant MQTT as MessageSender
    participant Device as 设备
    participant Receiver as MessageReceiver

    Coord->>Exec: executeActionWithRecord(record)
    Exec->>MQTT: sendToMqtt(topic, data)
    MQTT->>Device: 发送控制指令

    loop 每秒检查
        Monitor->>Coord: 检查超时执行记录

        alt 发现超时记录
            Monitor->>Coord: retryExecution(record)
            Coord->>Exec: executeActionWithRecord(record)
            Exec->>MQTT: sendToMqtt(topic, data)
            MQTT->>Device: 重新发送控制指令

            alt 收到反馈
                Device-->>Receiver: 发送执行反馈
                Receiver-->>Coord: handleExecutionResponse(seq, result)
                Coord->>Coord: 更新执行状态
            else 仍然超时且重试次数<3
                Monitor->>Coord: 继续重试
            else 重试次数>=3
                Monitor->>Coord: 标记为最终失败
                Coord->>Coord: 清理队列
            end
        end
    end
```

## 5. 配置参数

动作执行机制支持以下配置参数，可在`application.yml`中设置：

```yaml
rule:
  engine:
    action-execution:
      timeout-seconds: 3          # 执行超时时间（秒）
      max-retry-count: 3          # 最大重试次数
      queue-cleanup-interval: 300 # 队列清理间隔（秒）
      enable-priority-control: true # 是否启用优先级控制
      critical-action-timeout: 10   # 关键动作超时时间（秒）
    logging:
      action-execution:
        enabled: true             # 是否启用执行日志
        file-name: action-execution.log # 日志文件名
        max-file-size: 10MB       # 日志文件大小限制
        max-history: 30           # 日志文件保留天数
```

## 6. 监控接口

动作执行机制提供以下REST API用于监控和管理：

| 接口                                             | 方法   | 说明       |
| ---------------------------------------------- | ---- | -------- |
| `/api/engine/monitor/action-execution-queue`   | GET  | 获取执行队列状态 |
| `/api/engine/monitor/action-execution-stats`   | GET  | 获取执行统计信息 |
| `/api/engine/monitor/action-monitor-stats`     | GET  | 获取监控统计信息 |
| `/api/engine/monitor/action-execution-cleanup` | POST | 手动触发队列清理 |

## 7. 最佳实践

### 7.1 动作优先级设置

- 设备开关类动作应设置`priority=1`，确保先执行且成功
- 属性设置类动作应使用默认优先级（99）
- 消息通知类动作可设置较低优先级，如`priority=100`

### 7.2 执行超时配置

- 对于响应较慢的设备，可适当增加`timeout-seconds`
- 关键动作可设置较长的`critical-action-timeout`，确保有足够时间等待反馈

### 7.3 日志级别配置

- 生产环境建议启用`action-execution.enabled=true`
- 问题排查时可查看`action-execution.log`获取详细执行日志

## 8. 常见问题

### 8.1 动作执行超时

可能原因：

- 设备响应慢或未响应
- 网络延迟或丢包
- MQTT消息未正确发送或接收

解决方案：

- 增加超时时间配置
- 检查设备连接状态
- 检查MQTT服务器连接状态

### 8.2 关键动作失败导致后续动作未执行

可能原因：

- 设备离线或故障
- 动作参数配置错误
- 设备不支持指定操作

解决方案：

- 检查设备状态和连接
- 验证动作参数正确性
- 确认设备支持相应操作

### 8.3 执行队列堆积

可能原因：

- 大量规则同时触发
- 设备响应缓慢
- 系统资源不足

解决方案：

- 优化规则触发条件，避免同时触发
- 增加线程池大小
- 增加系统资源配置

## 9. 技术实现细节

### 9.1 避免忙等待机制

动作执行机制使用`CompletableFuture`替代传统的`Thread.sleep()`轮询机制，避免忙等待：

```java
// 创建CompletableFuture用于等待完成通知
CompletableFuture<Boolean> completionFuture = new CompletableFuture<>();
completionFutures.put(executionId, completionFuture);

// 等待完成或超时 - 不再使用Thread.sleep轮询
Boolean result = completionFuture.get(timeoutMs, TimeUnit.MILLISECONDS);
```

### 9.2 类型安全的参数处理

动作定义中的参数支持多种数据类型，通过安全的类型转换方法处理：

```java
/**
 * 安全地将Object转换为String
 * 兼容String、Integer、Double、Boolean等类型
 */
private String getStringValue(Object value) {
    if (value == null) {
        return "null";
    }
    return value.toString();
}
```

### 9.3 线程安全设计

- 使用`ConcurrentHashMap`管理执行队列和序列号映射
- 使用`ThreadPoolTaskExecutor`进行异步执行
- 使用`ScheduledExecutorService`进行定时监控

### 9.4 资源管理

- 执行完成后自动清理`CompletableFuture`
- 定期清理过期的执行记录
- 正确的生命周期管理（`@PostConstruct`和`@PreDestroy`）

## 10. 性能优化

### 10.1 异步执行

- 关键动作同步等待，确保执行顺序
- 普通动作异步执行，提高并发性能
- 使用独立线程池，避免阻塞主线程

### 10.2 内存优化

- 执行记录使用Map存储上下文，避免固定字段限制
- 定期清理过期记录，防止内存泄漏
- 使用弱引用管理CompletableFuture

### 10.3 网络优化

- MQTT消息使用QoS 1级别，确保消息送达
- 序列号机制确保消息匹配的准确性
- 重复操作检测避免网络资源浪费

## 11. 扩展性设计

### 11.1 动作类型扩展

动作执行器支持多种动作类型，可轻松扩展新的动作类型：

- `DEVICE_CONTROL`：设备控制
- `SEND_MESSAGE`：消息发送
- `CALL_API`：API调用
- `LOG_EVENT`：日志记录

### 11.2 反馈机制扩展

当前支持MQTT反馈，可扩展支持其他协议：

- HTTP回调
- WebSocket通知
- 数据库状态查询

### 11.3 监控指标扩展

提供丰富的监控指标，支持自定义扩展：

- 执行成功率
- 平均执行时间
- 重试次数分布
- 队列长度变化

## 12. 版本历史

| 版本    | 日期         | 主要变更                        |
| ----- | ---------- | --------------------------- |
| 1.0.0 | 2025-07-10 | 初始版本，支持基本的动作执行功能            |
| 1.0.0 | 2025-07-10 | 添加优先级控制和超时重试机制              |
| 1.0.0 | 2025-07-10 | 优化忙等待问题，使用CompletableFuture |
| 1.0.0 | 2025-07-10 | 添加类型安全的参数处理                 |

---

*本文档描述了规则引擎动作执行机制的完整技术实现，包括设计理念、核心组件、执行流程和最佳实践。如有疑问或建议，请联系开发团队。*
