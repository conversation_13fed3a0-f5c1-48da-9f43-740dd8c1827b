# Device Edge Service

IoT设备边缘管理服务，用于IoT楼宇自动化系统的设备管理和应用功能。

## 项目结构

```
device-edge/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/inxaiot/edge/
│   │   │       ├── EdgeApplication.java               # 主启动类
│   │   │       ├── config/                            # 配置类
│   │   │       │   ├── DatabaseConfig.java           # 数据库配置
│   │   │       │   └── RedisConfig.java              # Redis配置
│   │   │       ├── common/                           # 公共包
│   │   │       │   ├── constant/                     # 常量定义
│   │   │       │   │   └── DeviceStatus.java        # 设备状态常量
│   │   │       │   ├── controller/                   # 公共控制器
│   │   │       │   │   └── HealthController.java    # 健康检查控制器
│   │   │       │   ├── response/                     # 响应对象
│   │   │       │   │   └── ApiResponse.java         # 统一响应格式
│   │   │       │   └── util/                         # 工具类
│   │   │       │       ├── DateUtils.java           # 日期工具类
│   │   │       │       └── JsonUtils.java           # JSON工具类
│   │   │       ├── controller/                       # 控制器层
│   │   │       │   ├── app/                          # 应用功能控制器
│   │   │       │   │   └── AppConfigController.java # 应用配置控制器
│   │   │       │   └── device/                       # 设备管理控制器
│   │   │       │       └── DeviceInfoController.java # 设备信息控制器
│   │   │       ├── service/                          # 服务层
│   │   │       │   ├── app/                          # 应用功能服务
│   │   │       │   │   ├── AppConfigService.java    # 应用配置服务接口
│   │   │       │   │   └── impl/                     # 服务实现
│   │   │       │   │       └── AppConfigServiceImpl.java # 应用配置服务实现
│   │   │       │   └── device/                       # 设备管理服务
│   │   │       │       ├── DeviceInfoService.java   # 设备信息服务接口
│   │   │       │       └── impl/                     # 服务实现
│   │   │       │           └── DeviceInfoServiceImpl.java # 设备信息服务实现
│   │   │       ├── mapper/                           # 数据访问层(DAO)
│   │   │       │   ├── app/                          # 应用功能数据访问
│   │   │       │   │   └── AppConfigMapper.java     # 应用配置Mapper
│   │   │       │   └── device/                       # 设备管理数据访问
│   │   │       │       ├── DeviceInfoMapper.java    # 设备信息Mapper
│   │   │       │       └── DeviceStatusMapper.java  # 设备状态Mapper
│   │   │       └── entity/                           # 实体类
│   │   │           ├── app/                          # 应用功能实体
│   │   │           │   └── AppConfig.java           # 应用配置实体
│   │   │           └── device/                       # 设备管理实体
│   │   │               ├── DeviceConfig.java        # 设备配置实体
│   │   │               ├── DeviceInfo.java          # 设备信息实体
│   │   │               └── DeviceStatus.java        # 设备状态实体
│   │   ├── resources/
│   │   │   ├── application.yml                        # 主配置文件
│   │   │   ├── application-mqtt.yaml                  # MQTT配置
│   │   │   ├── logback-spring.xml                     # 日志配置
│   │   │   ├── schema.sql                             # SQLite数据库脚本
│   │   │   ├── schema-mysql.sql                       # MySQL数据库脚本
│   │   │   └── mapper/                                # MyBatis映射文件
│   │   │       ├── device/                            # 设备相关映射
│   │   │       └── app/                               # 应用相关映射
│   │   └── docker/
│   │       └── Dockerfile                             # Docker构建文件
│   └── test/
│       └── java/
│           └── com/inxaiot/edge/
│               └── DeviceEdgeApplicationTests.java    # 测试类
├── pom.xml                                            # Maven配置
└── README.md                                          # 项目说明
```

## 技术栈

- **Spring Boot 2.7.18** - 主框架
- **MyBatis** - 数据持久化
- **Redis** - 缓存和会话存储
- **SQLite/MySQL** - 数据库（支持切换）
- **MQTT** - 消息通信
- **FastJSON** - JSON处理
- **Lombok** - 代码简化
- **Docker** - 容器化部署

## 功能特性

### 设备管理模块
- **设备信息管理**：设备的增删改查、状态更新
- **设备状态监控**：实时设备状态数据管理
- **设备配置管理**：设备参数配置
- **设备日志记录**：设备操作日志

### 应用功能模块
- **应用配置管理**：系统配置参数管理
- **应用日志记录**：应用操作日志
- **轻量级业务功能**：辅助业务功能

### 公共功能模块
- **统一响应格式**：标准化API响应
- **工具类库**：日期、JSON等工具类
- **常量定义**：系统常量管理
- **健康检查**：服务状态监控

### 架构特点
- **分层架构**：Controller -> Service -> Mapper 三层结构
- **模块化设计**：按业务功能分包组织
- **统一数据访问**：MyBatis统一数据访问
- **缓存支持**：Redis缓存提升性能

## 配置说明

### 数据库配置
支持SQLite和MySQL数据库切换：

**SQLite（默认）:**
```yaml
spring:
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: jdbc:sqlite:${device.edge.data.path}/device_edge.db
```

**MySQL:**
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    username: your_username
    password: your_password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
```

### MQTT配置
```yaml
mqtt:
  broker:
    url: tcp://localhost:1883
    username: 
    password: 
    client-id: device-edge-${random.uuid}
```

## 启动方式

### 开发环境
```bash
mvn spring-boot:run
```

### 生产环境
```bash
mvn clean package
java -jar target/device-edge.jar
```

### Docker部署
```bash
mvn clean package -P docker
docker run -p 6002:6002 com.inxaiot/device-edge:0.0.1
```

## API接口

### 健康检查
- `GET /api/health` - 服务健康状态
- `GET /api/health/info` - 服务信息

### 设备管理
- `POST /api/device/info` - 添加设备
- `GET /api/device/info/{deviceId}` - 查询设备
- `PUT /api/device/info` - 更新设备
- `DELETE /api/device/info/{deviceId}` - 删除设备
- `GET /api/device/info` - 查询所有设备
- `PUT /api/device/info/{deviceId}/status` - 更新设备状态

### 应用配置
- `POST /api/app/config/{appName}/{configKey}` - 设置配置
- `GET /api/app/config/{appName}/{configKey}` - 获取配置
- `DELETE /api/app/config/{appName}/{configKey}` - 删除配置
- `GET /api/app/config/{appName}` - 获取应用所有配置

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| DB_DRIVER_CLASS | 数据库驱动类 | org.sqlite.JDBC |
| DB_URL | 数据库连接URL | jdbc:sqlite:${device.edge.data.path}/device_edge.db |
| DB_USERNAME | 数据库用户名 | |
| DB_PASSWORD | 数据库密码 | |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |
| REDIS_PASSWORD | Redis密码 | |
| DATA_PATH | 数据存储路径 | /data/device-edge |
| LOG_PATH | 日志存储路径 | /data/device-edge/logs |

## 包结构说明

### 新的包结构特点
1. **按层次分包**：Controller、Service、Mapper、Entity 按层次组织
2. **按业务分组**：每层内部按 app 和 device 业务模块分组
3. **公共模块独立**：common 包包含通用功能
4. **配置集中管理**：config 包统一管理配置类

### 包结构对比

**旧结构（按业务垂直分包）：**
```
com.inxaiot.edge/
├── device/
│   ├── controller/
│   ├── service/
│   ├── dao/
│   └── entity/
└── app/
    ├── controller/
    ├── service/
    ├── dao/
    └── entity/
```

**新结构（按层次水平分包）：**
```
com.inxaiot.edge/
├── controller/
│   ├── app/
│   └── device/
├── service/
│   ├── app/
│   └── device/
├── mapper/
│   ├── app/
│   └── device/
└── entity/
    ├── app/
    └── device/
```

### 新结构的优势
1. **层次清晰**：每个技术层次一目了然
2. **便于维护**：同层组件集中管理
3. **扩展性好**：新增业务模块只需在各层添加对应包
4. **符合规范**：遵循Spring Boot推荐的包结构
5. **IDE友好**：IDE能更好地识别和组织代码

## 开发说明

1. **分层架构**：Controller -> Service -> Mapper 三层结构
2. **业务模块**：app（应用功能）和 device（设备管理）两大业务模块
3. **公共功能**：common 包提供通用功能支持
4. **配置管理**：config 包统一管理各种配置类
5. **缓存优化**：使用Redis进行数据缓存
6. **消息通信**：支持MQTT消息通信
7. **容器化部署**：集成Docker容器化部署
