@echo off
echo Testing Device Edge API...

set BASE_URL=http://localhost:6002

echo.
echo 1. Testing Health Check...
curl -X GET "%BASE_URL%/api/health"

echo.
echo.
echo 2. Testing Service Info...
curl -X GET "%BASE_URL%/api/health/info"

echo.
echo.
echo 3. Testing Get All Devices...
curl -X GET "%BASE_URL%/api/device/info"

echo.
echo.
echo 4. Testing Get Device by ID...
curl -X GET "%BASE_URL%/api/device/info/device001"

echo.
echo.
echo 5. Testing Get App Config...
curl -X GET "%BASE_URL%/api/app/config/system"

echo.
echo.
echo 6. Testing Get Specific App Config...
curl -X GET "%BASE_URL%/api/app/config/system/max_devices"

echo.
echo.
echo API Testing Complete!
pause
